# Security Best Practices

This document outlines security best practices for this application, particularly regarding API keys and sensitive information.

## Supabase Security

### API Keys

Supabase provides two types of API keys:

1. **Anon/Public Key**: This key is safe to use in client-side code. It has limited permissions and is subject to Row Level Security (RLS) policies.

2. **Service Role Key**: This key has admin privileges and can bypass RLS policies. It should NEVER be used in client-side code or committed to your repository.

### Environment Variables

All sensitive keys should be stored in environment variables:

- Client-side keys should use the `VITE_` prefix (e.g., `VITE_SUPABASE_ANON_KEY`)
- Server-side keys should NOT use the `VITE_` prefix (e.g., `SUPABASE_SERVICE_ROLE_KEY`)

### Securing Your Keys

1. **Reset Your Keys**: If you've accidentally exposed your service role key, immediately reset it in the Supabase dashboard.

2. **Use Environment Variables**: Always use environment variables for sensitive information.

3. **Check Your .gitignore**: Ensure that `.env` files are in your `.gitignore` to prevent committing sensitive information.

4. **Use Edge Functions**: For operations that require the service role key, use Supabase Edge Functions.

## Encryption

For secure data transmission between the client and server:

1. **Use Environment Variables**: Store encryption keys in environment variables.

2. **Update Edge Functions**: When changing encryption keys, update both client-side code and Edge Functions.

## Deployment

When deploying your application:

1. **Set Environment Variables**: Configure all required environment variables in your hosting platform.

2. **Update Supabase Edge Functions**: Run `npm run update:supabase-env` to update the environment variables for your Edge Functions.

## Security Checklist

- [ ] Reset exposed Supabase service role key
- [ ] Update client code to use environment variables
- [ ] Ensure `.env` is in `.gitignore`
- [ ] Update encryption keys in both client and server code
- [ ] Deploy changes to production
- [ ] Update Edge Function environment variables
