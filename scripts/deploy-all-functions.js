#!/usr/bin/env node

/**
 * This script deploys all Supabase edge functions.
 */

import { execSync } from 'child_process';

try {
  // Check if the Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'inherit' });
  } catch (error) {
    throw new Error('Supabase CLI is not installed. Please install it using: npm install -g supabase');
  }

  // Deploy all functions
  execSync('supabase functions deploy stripe-checkout', { stdio: 'inherit' });
  execSync('supabase functions deploy gemini-api', { stdio: 'inherit' });
  execSync('supabase functions deploy data-processor', { stdio: 'inherit' });
  execSync('supabase functions deploy stripe', { stdio: 'inherit' });
  execSync('supabase functions deploy stripe-webhook', { stdio: 'inherit' });
  execSync('supabase functions deploy chart-processor', { stdio: 'inherit' });
  execSync('supabase functions deploy Osis-api', { stdio: 'inherit' });
  execSync('supabase functions deploy api-processor', { stdio: 'inherit' });
  execSync('supabase functions deploy market-data', { stdio: 'inherit' });
  execSync('supabase functions deploy chart-data', { stdio: 'inherit' });
  execSync('supabase functions deploy aura', { stdio: 'inherit' });
  execSync('supabase functions deploy tradingview-poster', { stdio: 'inherit' });
  execSync('supabase functions deploy agent-runner', { stdio: 'inherit' });

} catch (error) {
  process.exit(1);
}
