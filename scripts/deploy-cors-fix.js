#!/usr/bin/env node

/**
 * This script deploys all Supabase edge functions with CORS fixes.
 */

import { execSync } from 'child_process';

try {
  // Check if the Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'inherit' });
  } catch (error) {
    throw new Error('Supabase CLI is not installed. Please install it using: npm install -g supabase');
  }

  // Deploy all functions
  // Deploy stripe function
  execSync('supabase functions deploy stripe', { stdio: 'inherit' });

  // Deploy stripe-checkout function
  execSync('supabase functions deploy stripe-checkout', { stdio: 'inherit' });

  // Deploy gemini-api function
  execSync('supabase functions deploy gemini-api', { stdio: 'inherit' });

  // Deploy data-processor function
  execSync('supabase functions deploy data-processor', { stdio: 'inherit' });

  // Deploy stripe-webhook function
  execSync('supabase functions deploy stripe-webhook', { stdio: 'inherit' });

  // Deploy chart-processor function
  execSync('supabase functions deploy chart-processor', { stdio: 'inherit' });

  // Deploy Osis-api function
  execSync('supabase functions deploy Osis-api', { stdio: 'inherit' });

  // Deploy api-processor function
  execSync('supabase functions deploy api-processor', { stdio: 'inherit' });

  // Deploy market-data function
  execSync('supabase functions deploy market-data', { stdio: 'inherit' });

  // Deploy chart-data function
  execSync('supabase functions deploy chart-data', { stdio: 'inherit' });

} catch (error) {
  process.exit(1);
}
