# Stripe Integration Update

## Overview

This update improves the Stripe subscription integration with the application, ensuring that users' subscription statuses are properly reflected in the UI and that message limits are correctly enforced.

## Key Changes

### 1. Subscription Management

- Enhanced the ManageSubscription page to display accurate subscription information
- Added a manual sync button to force refresh subscription data
- Improved error handling and user feedback during subscription changes

### 2. Webhook Integration

- Created scripts to manage Stripe webhook configuration:
  - `update-webhook.js`: Updates the Stripe webhook endpoint
  - `deploy-stripe-function.js`: Deploys the Stripe function to Supabase
  - `test-webhook.js`: Tests the webhook locally
  - `trigger-webhook-event.js`: Triggers test webhook events
- Fixed webhook endpoint to handle events without requiring authentication
- Corrected webhook URL path to ensure proper event processing

### 3. UI Improvements

- Updated the premium plan card in the Subscription page to ensure proper width on all screen sizes
- Enhanced the TokenDisplay component to show accurate message limits based on subscription type

### 4. Verification Tools

- Added a comprehensive verification script (`verify-subscription.js`) to check the entire subscription system
- Created a user sync script (`sync-user-subscription.js`) to manually sync a specific user's subscription
- Created detailed documentation in `STRIPE_INTEGRATION.md` and `WEBHOOK_SETUP.md`

## How to Use

### Setting Up the Webhook

1. Ensure you have the required environment variables in your `.env` file:
   ```
   NEXT_PUBLIC_VITE_SUPABASE_URL=your_VITE_SUPABASE_URL
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_webhook_secret
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_publishable_key
   ```

2. Deploy the Stripe function and update the webhook:
   ```bash
   npm run deploy-stripe
   ```

3. Verify the setup:
   ```bash
   npm run verify-subscription
   ```

### Testing the Webhook

1. Start the webhook listener:
   ```bash
   npm run test-webhook
   ```

2. In another terminal, trigger a test event:
   ```bash
   npm run trigger-webhook
   ```

### Manually Syncing a User's Subscription

If a user reports issues with their subscription, you can manually sync their data:

1. Run the sync script:
   ```bash
   npm run sync-user
   ```

2. Enter the user's email or ID when prompted.

3. The script will:
   - Find the user in the database
   - Retrieve their Stripe subscription
   - Update their subscription record
   - Reset their token usage

## Troubleshooting

If users report issues with their subscription:

1. Run the verification script:
   ```bash
   npm run verify-subscription
   ```

2. Check the Supabase logs:
   ```bash
   supabase functions logs stripe
   ```

3. Have the user try the manual sync button on the ManageSubscription page

4. If the issue persists, manually sync the user's subscription:
   ```bash
   npm run sync-user
   ```

For more detailed information, refer to:
- `STRIPE_INTEGRATION.md`: Comprehensive overview of the integration
- `WEBHOOK_SETUP.md`: Detailed webhook setup instructions 