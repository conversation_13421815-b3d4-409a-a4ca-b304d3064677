import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
// Remove the component tagger import since we're not using it
// import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [
      react()
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    define: {
      // Explicitly make Vite replace env variables
      'process.env.WHOP_CLIENT_ID': JSON.stringify(env.WHOP_CLIENT_ID),
      'process.env.WHOP_CLIENT_SECRET': JSON.stringify(env.WHOP_CLIENT_SECRET),
      'process.env.WHOP_REDIRECT_URI': JSON.stringify(env.WHOP_REDIRECT_URI)
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./src/test/setup.ts'],
      css: false,
    }
  };
});
