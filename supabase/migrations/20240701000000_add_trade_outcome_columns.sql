-- Add columns to track trade outcomes
ALTER TABLE "public"."trades" 
ADD COLUMN IF NOT EXISTS "outcome" TEXT CHECK (outcome IN ('take_profit', 'stop_loss', NULL)),
ADD COLUMN IF NOT EXISTS "close_price" NUMERIC,
ADD COLUMN IF NOT EXISTS "close_date" TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS "profit_amount" NUMERIC;

-- Comment on the columns to document their purpose
COMMENT ON COLUMN "public"."trades"."outcome" 
IS 'The outcome of the trade: take_profit, stop_loss, or NULL if still open';

COMMENT ON COLUMN "public"."trades"."close_price" 
IS 'The price at which the trade was closed';

COMMENT ON COLUMN "public"."trades"."close_date" 
IS 'The date when the trade was closed';

COMMENT ON COLUMN "public"."trades"."profit_amount" 
IS 'The profit/loss amount for this trade';
