-- Create tables for the Discover page feature
-- This migration adds support for agent marketplace functionality

-- Table to store published agents
CREATE TABLE IF NOT EXISTS public.published_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  publisher_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL DEFAULT 'general',
  tags TEXT[] DEFAULT '{}',
  is_featured BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  download_count INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0.00,
  total_reviews INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure unique published agent per user
  UNIQUE(agent_id, publisher_id)
);

-- Table to store agent categories
CREATE TABLE IF NOT EXISTS public.agent_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  icon_url TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Table to store agent reviews and ratings
CREATE TABLE IF NOT EXISTS public.agent_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  published_agent_id UUID REFERENCES public.published_agents(id) ON DELETE CASCADE NOT NULL,
  reviewer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure one review per user per published agent
  UNIQUE(published_agent_id, reviewer_id)
);

-- Table to track agent imports (user's library)
CREATE TABLE IF NOT EXISTS public.agent_imports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  published_agent_id UUID REFERENCES public.published_agents(id) ON DELETE CASCADE NOT NULL,
  imported_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  imported_agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  custom_name TEXT, -- User can rename imported agent
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure unique import per user per published agent
  UNIQUE(published_agent_id, imported_by)
);

-- Table to store agent usage statistics
CREATE TABLE IF NOT EXISTS public.agent_usage_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  published_agent_id UUID REFERENCES public.published_agents(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  executions_count INTEGER DEFAULT 0,
  last_execution TIMESTAMP WITH TIME ZONE,
  total_execution_time INTEGER DEFAULT 0, -- in milliseconds
  success_rate DECIMAL(5,2) DEFAULT 0.00, -- percentage
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure unique stats per user per published agent
  UNIQUE(published_agent_id, user_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS published_agents_publisher_id_idx ON public.published_agents(publisher_id);
CREATE INDEX IF NOT EXISTS published_agents_category_idx ON public.published_agents(category);
CREATE INDEX IF NOT EXISTS published_agents_is_active_idx ON public.published_agents(is_active);
CREATE INDEX IF NOT EXISTS published_agents_is_featured_idx ON public.published_agents(is_featured);
CREATE INDEX IF NOT EXISTS published_agents_average_rating_idx ON public.published_agents(average_rating);
CREATE INDEX IF NOT EXISTS published_agents_download_count_idx ON public.published_agents(download_count);
CREATE INDEX IF NOT EXISTS published_agents_created_at_idx ON public.published_agents(created_at);

CREATE INDEX IF NOT EXISTS agent_reviews_published_agent_id_idx ON public.agent_reviews(published_agent_id);
CREATE INDEX IF NOT EXISTS agent_reviews_reviewer_id_idx ON public.agent_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS agent_reviews_rating_idx ON public.agent_reviews(rating);

CREATE INDEX IF NOT EXISTS agent_imports_published_agent_id_idx ON public.agent_imports(published_agent_id);
CREATE INDEX IF NOT EXISTS agent_imports_imported_by_idx ON public.agent_imports(imported_by);
CREATE INDEX IF NOT EXISTS agent_imports_is_active_idx ON public.agent_imports(is_active);

CREATE INDEX IF NOT EXISTS agent_usage_stats_published_agent_id_idx ON public.agent_usage_stats(published_agent_id);
CREATE INDEX IF NOT EXISTS agent_usage_stats_user_id_idx ON public.agent_usage_stats(user_id);

-- Add RLS policies
-- Published agents policies
CREATE POLICY "Anyone can view active published agents"
  ON public.published_agents FOR SELECT
  USING (is_active = true);

CREATE POLICY "Users can publish their own agents"
  ON public.published_agents FOR INSERT
  WITH CHECK (auth.uid() = publisher_id);

CREATE POLICY "Publishers can update their own published agents"
  ON public.published_agents FOR UPDATE
  USING (auth.uid() = publisher_id);

CREATE POLICY "Publishers can delete their own published agents"
  ON public.published_agents FOR DELETE
  USING (auth.uid() = publisher_id);

-- Agent categories policies
CREATE POLICY "Anyone can view active categories"
  ON public.agent_categories FOR SELECT
  USING (is_active = true);

-- Agent reviews policies
CREATE POLICY "Anyone can view reviews"
  ON public.agent_reviews FOR SELECT
  USING (true);

CREATE POLICY "Users can create reviews"
  ON public.agent_reviews FOR INSERT
  WITH CHECK (auth.uid() = reviewer_id);

CREATE POLICY "Users can update their own reviews"
  ON public.agent_reviews FOR UPDATE
  USING (auth.uid() = reviewer_id);

CREATE POLICY "Users can delete their own reviews"
  ON public.agent_reviews FOR DELETE
  USING (auth.uid() = reviewer_id);

-- Agent imports policies
CREATE POLICY "Users can view their own imports"
  ON public.agent_imports FOR SELECT
  USING (auth.uid() = imported_by);

CREATE POLICY "Users can create imports"
  ON public.agent_imports FOR INSERT
  WITH CHECK (auth.uid() = imported_by);

CREATE POLICY "Users can update their own imports"
  ON public.agent_imports FOR UPDATE
  USING (auth.uid() = imported_by);

CREATE POLICY "Users can delete their own imports"
  ON public.agent_imports FOR DELETE
  USING (auth.uid() = imported_by);

-- Agent usage stats policies
CREATE POLICY "Users can view their own usage stats"
  ON public.agent_usage_stats FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own usage stats"
  ON public.agent_usage_stats FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own usage stats"
  ON public.agent_usage_stats FOR UPDATE
  USING (auth.uid() = user_id);

-- Enable RLS on all tables
ALTER TABLE public.published_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_imports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_usage_stats ENABLE ROW LEVEL SECURITY;

-- Create functions for updating ratings and stats
CREATE OR REPLACE FUNCTION public.update_published_agent_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Update average rating and total reviews for the published agent
  UPDATE public.published_agents
  SET
    average_rating = (
      SELECT COALESCE(AVG(rating), 0)
      FROM public.agent_reviews
      WHERE published_agent_id = COALESCE(NEW.published_agent_id, OLD.published_agent_id)
    ),
    total_reviews = (
      SELECT COUNT(*)
      FROM public.agent_reviews
      WHERE published_agent_id = COALESCE(NEW.published_agent_id, OLD.published_agent_id)
    ),
    updated_at = timezone('utc'::text, now())
  WHERE id = COALESCE(NEW.published_agent_id, OLD.published_agent_id);

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create function to update download count
CREATE OR REPLACE FUNCTION public.increment_download_count()
RETURNS TRIGGER AS $$
BEGIN
  -- Increment download count when agent is imported
  UPDATE public.published_agents
  SET
    download_count = download_count + 1,
    updated_at = timezone('utc'::text, now())
  WHERE id = NEW.published_agent_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER update_published_agent_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.agent_reviews
  FOR EACH ROW
  EXECUTE FUNCTION public.update_published_agent_rating();

CREATE TRIGGER increment_download_count_trigger
  AFTER INSERT ON public.agent_imports
  FOR EACH ROW
  EXECUTE FUNCTION public.increment_download_count();

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_published_agents_updated_at
  BEFORE UPDATE ON public.published_agents
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_agent_reviews_updated_at
  BEFORE UPDATE ON public.agent_reviews
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_agent_usage_stats_updated_at
  BEFORE UPDATE ON public.agent_usage_stats
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default categories
INSERT INTO public.agent_categories (name, description, sort_order) VALUES
  ('Day Trading', 'Agents focused on intraday trading strategies', 1),
  ('Technical Analysis', 'Agents using technical indicators and chart patterns', 2),
  ('Options Trading', 'Agents specialized in options strategies', 3),
  ('Swing Trading', 'Agents for medium-term position trading', 4),
  ('Risk Management', 'Agents focused on risk assessment and management', 5),
  ('Market Scanning', 'Agents for finding trading opportunities', 6),
  ('Fundamental Analysis', 'Agents using company fundamentals', 7),
  ('General', 'General purpose trading agents', 8)
ON CONFLICT (name) DO NOTHING;

-- Add comments to tables
COMMENT ON TABLE public.published_agents IS 'Stores agents published to the marketplace';
COMMENT ON TABLE public.agent_categories IS 'Stores agent categories for organization';
COMMENT ON TABLE public.agent_reviews IS 'Stores user reviews and ratings for published agents';
COMMENT ON TABLE public.agent_imports IS 'Tracks which users have imported which published agents';
COMMENT ON TABLE public.agent_usage_stats IS 'Stores usage statistics for published agents';
