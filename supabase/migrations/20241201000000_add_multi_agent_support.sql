-- Add single agent support to chats and messages tables
-- This migration adds support for storing agent selection preferences and execution results

-- Add selected_agent column to chats table
ALTER TABLE public.chats
ADD COLUMN IF NOT EXISTS selected_agent TEXT DEFAULT NULL;

-- Add agent-related columns to messages table
ALTER TABLE public.messages
ADD COLUMN IF NOT EXISTS agent_results JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS selected_agent TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS is_single_agent_analysis BOOLEAN DEFAULT FALSE;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS chats_selected_agent_idx ON public.chats (selected_agent);
CREATE INDEX IF NOT EXISTS messages_agent_results_idx ON public.messages USING GIN (agent_results);
CREATE INDEX IF NOT EXISTS messages_is_single_agent_analysis_idx ON public.messages (is_single_agent_analysis);

-- Add comments for documentation
COMMENT ON COLUMN public.chats.selected_agent IS 'Agent ID selected for this chat session';
COMMENT ON COLUMN public.messages.agent_results IS 'Results from agent execution for this message';
COMMENT ON COLUMN public.messages.selected_agent IS 'Agent ID that was selected when this message was sent';
COMMENT ON COLUMN public.messages.is_single_agent_analysis IS 'Whether this message used single agent analysis';

-- Update RLS policies to include new columns
-- The existing policies should automatically cover the new columns since they use user_id matching

-- Create a function to validate agent results structure
CREATE OR REPLACE FUNCTION validate_agent_results(agent_results JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  -- If agent_results is null, it's valid
  IF agent_results IS NULL THEN
    RETURN TRUE;
  END IF;

  -- If it's not an array, it's invalid
  IF jsonb_typeof(agent_results) != 'array' THEN
    RETURN FALSE;
  END IF;

  -- Check that each element has required fields
  -- This is a basic validation - could be expanded
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add check constraint for agent_results validation
ALTER TABLE public.messages
ADD CONSTRAINT messages_agent_results_valid
CHECK (validate_agent_results(agent_results));

-- Create a function to get chat with agent selection
CREATE OR REPLACE FUNCTION get_chat_with_agent(chat_id UUID, user_id UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  user_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  chat_type TEXT,
  selected_agent TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id,
    c.title,
    c.user_id,
    c.created_at,
    c.updated_at,
    c.chat_type::TEXT,
    c.selected_agent
  FROM public.chats c
  WHERE c.id = chat_id AND c.user_id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_chat_with_agent(UUID, UUID) TO authenticated;

-- Create a function to update chat agent selection
CREATE OR REPLACE FUNCTION update_chat_agent(chat_id UUID, user_id UUID, agent_id TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.chats
  SET
    selected_agent = agent_id,
    updated_at = NOW()
  WHERE id = chat_id AND user_id = user_id;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION update_chat_agent(UUID, UUID, TEXT) TO authenticated;

-- Create a view for messages with agent information
CREATE OR REPLACE VIEW public.messages_with_agents AS
SELECT
  m.id,
  m.chat_id,
  m.role,
  m.content,
  m.created_at,
  m.agent_results,
  m.selected_agent,
  m.is_single_agent_analysis,
  c.title as chat_title,
  c.selected_agent as chat_selected_agent
FROM public.messages m
JOIN public.chats c ON m.chat_id = c.id;

-- Grant access to the view
GRANT SELECT ON public.messages_with_agents TO authenticated;

-- Add RLS policy for the view
CREATE POLICY "Users can view their own messages with agents"
  ON public.messages_with_agents FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.chats
      WHERE chats.id = messages_with_agents.chat_id
      AND chats.user_id = auth.uid()
    )
  );

-- Enable RLS on the view
ALTER VIEW public.messages_with_agents ENABLE ROW LEVEL SECURITY;

-- Create an index on agent execution results for analytics
CREATE INDEX IF NOT EXISTS messages_agent_execution_stats_idx
ON public.messages ((agent_results->>'executionTime')::INTEGER)
WHERE agent_results IS NOT NULL;

-- Create a function to get agent usage statistics
CREATE OR REPLACE FUNCTION get_agent_usage_stats(user_id UUID, days_back INTEGER DEFAULT 30)
RETURNS TABLE (
  agent_id TEXT,
  agent_name TEXT,
  execution_count BIGINT,
  avg_execution_time NUMERIC,
  success_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  WITH agent_executions AS (
    SELECT
      (jsonb_array_elements(m.agent_results)->>'agentId')::TEXT as agent_id,
      (jsonb_array_elements(m.agent_results)->>'agentName')::TEXT as agent_name,
      (jsonb_array_elements(m.agent_results)->>'executionTime')::INTEGER as execution_time,
      CASE WHEN jsonb_array_elements(m.agent_results)->>'error' IS NULL THEN 1 ELSE 0 END as success
    FROM public.messages m
    JOIN public.chats c ON m.chat_id = c.id
    WHERE c.user_id = user_id
      AND m.agent_results IS NOT NULL
      AND m.created_at >= NOW() - INTERVAL '1 day' * days_back
  )
  SELECT
    ae.agent_id,
    ae.agent_name,
    COUNT(*)::BIGINT as execution_count,
    AVG(ae.execution_time)::NUMERIC as avg_execution_time,
    (SUM(ae.success)::NUMERIC / COUNT(*)::NUMERIC * 100) as success_rate
  FROM agent_executions ae
  GROUP BY ae.agent_id, ae.agent_name
  ORDER BY execution_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_agent_usage_stats(UUID, INTEGER) TO authenticated;
