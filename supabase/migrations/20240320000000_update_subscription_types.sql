-- Update the subscription_type enum to remove 'free' and add 'basic'
ALTER TYPE subscription_type RENAME TO subscription_type_old;
CREATE TYPE subscription_type AS ENUM ('basic', 'pro', 'premium');

-- Convert existing data
ALTER TABLE profiles 
  ALTER COLUMN subscription_type TYPE subscription_type 
  USING CASE 
    WHEN subscription_type_old::text = 'free' THEN NULL 
    ELSE subscription_type_old::text::subscription_type 
  END;

ALTER TABLE subscriptions 
  ALTER COLUMN subscription_type TYPE subscription_type 
  USING CASE 
    WHEN subscription_type_old::text = 'free' THEN NULL 
    ELSE subscription_type_old::text::subscription_type 
  END;

-- Drop the old type
DROP TYPE subscription_type_old;

-- Update any existing free subscriptions to be null
UPDATE profiles SET subscription_type = NULL WHERE subscription_type IS NULL;
UPDATE subscriptions SET subscription_type = NULL WHERE subscription_type IS NULL;

-- Add check constraints to ensure valid subscription types
ALTER TABLE profiles 
  ADD CONSTRAINT valid_subscription_type 
  CHECK (subscription_type IN ('basic', 'pro', 'premium'));

ALTER TABLE subscriptions 
  ADD CONSTRAINT valid_subscription_type 
 