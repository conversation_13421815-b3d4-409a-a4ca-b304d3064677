-- Create table for storing agent backtesting results
CREATE TABLE IF NOT EXISTS public.agent_backtests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  timeframe TEXT NOT NULL,
  result JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_agent_backtests_user_id ON public.agent_backtests(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_backtests_agent_id ON public.agent_backtests(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_backtests_symbol ON public.agent_backtests(symbol);
CREATE INDEX IF NOT EXISTS idx_agent_backtests_created_at ON public.agent_backtests(created_at);

-- Enable RLS on the table
ALTER TABLE public.agent_backtests ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own backtests" ON public.agent_backtests
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own backtests" ON public.agent_backtests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own backtests" ON public.agent_backtests
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own backtests" ON public.agent_backtests
  FOR DELETE USING (auth.uid() = user_id);

-- Add comment to the table
COMMENT ON TABLE public.agent_backtests IS 'Stores backtesting results for trading agents';
