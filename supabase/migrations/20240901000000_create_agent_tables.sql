-- Create tables for the AI agent builder feature

-- Table to store user-created agents
CREATE TABLE IF NOT EXISTS public.agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  configuration JSONB NOT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  public_description TEXT,
  tags TEXT[] DEFAULT '{}',
  likes_count INTEGER DEFAULT 0,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Table to store agent execution history
CREATE TABLE IF NOT EXISTS public.agent_runs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  result JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Table to store agent likes
CREATE TABLE IF NOT EXISTS public.agent_likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(agent_id, user_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS agents_user_id_idx ON public.agents(user_id);
CREATE INDEX IF NOT EXISTS agents_is_public_idx ON public.agents(is_public);
CREATE INDEX IF NOT EXISTS agents_likes_count_idx ON public.agents(likes_count);
CREATE INDEX IF NOT EXISTS agents_usage_count_idx ON public.agents(usage_count);
CREATE INDEX IF NOT EXISTS agent_runs_agent_id_idx ON public.agent_runs(agent_id);
CREATE INDEX IF NOT EXISTS agent_runs_symbol_idx ON public.agent_runs(symbol);
CREATE INDEX IF NOT EXISTS agent_runs_created_at_idx ON public.agent_runs(created_at);
CREATE INDEX IF NOT EXISTS agent_likes_agent_id_idx ON public.agent_likes(agent_id);
CREATE INDEX IF NOT EXISTS agent_likes_user_id_idx ON public.agent_likes(user_id);

-- Add RLS policies
-- Users can only see their own agents OR public agents
CREATE POLICY "Users can view their own agents or public agents"
  ON public.agents FOR SELECT
  USING (auth.uid() = user_id OR is_public = true);

-- Users can only create agents for themselves
CREATE POLICY "Users can create their own agents"
  ON public.agents FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own agents
CREATE POLICY "Users can update their own agents"
  ON public.agents FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can only delete their own agents
CREATE POLICY "Users can delete their own agents"
  ON public.agents FOR DELETE
  USING (auth.uid() = user_id);

-- Users can only see runs of their own agents
CREATE POLICY "Users can view runs of their own agents"
  ON public.agent_runs FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM public.agents
    WHERE agents.id = agent_runs.agent_id
    AND agents.user_id = auth.uid()
  ));

-- Users can only create runs for their own agents
CREATE POLICY "Users can create runs for their own agents"
  ON public.agent_runs FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.agents
    WHERE agents.id = agent_runs.agent_id
    AND agents.user_id = auth.uid()
  ));

-- Users can only delete runs of their own agents
CREATE POLICY "Users can delete runs of their own agents"
  ON public.agent_runs FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM public.agents
    WHERE agents.id = agent_runs.agent_id
    AND agents.user_id = auth.uid()
  ));

-- Agent likes policies
CREATE POLICY "Users can view all agent likes"
  ON public.agent_likes FOR SELECT
  USING (true);

CREATE POLICY "Users can create their own likes"
  ON public.agent_likes FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own likes"
  ON public.agent_likes FOR DELETE
  USING (auth.uid() = user_id);

-- Enable RLS on the tables
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_runs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_likes ENABLE ROW LEVEL SECURITY;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the updated_at column
CREATE TRIGGER update_agents_updated_at
BEFORE UPDATE ON public.agents
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add comment to the tables
COMMENT ON TABLE public.agents IS 'Stores user-created trading agents';
COMMENT ON TABLE public.agent_runs IS 'Stores execution history of trading agents';
