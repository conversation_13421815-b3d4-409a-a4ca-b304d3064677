// Technical Indicators
// Functions to calculate technical indicators

/**
 * Calculate Simple Moving Average (SMA)
 * @param data - Array of price data
 * @param period - Period for the SMA
 * @returns Array of SMA values
 */
function calculateSMA(data: number[], period: number): number[] {
  const result: number[] = [];

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j];
    }

    result.push(sum / period);
  }

  return result;
}

/**
 * Calculate Exponential Moving Average (EMA)
 * @param data - Array of price data
 * @param period - Period for the EMA
 * @returns Array of EMA values
 */
function calculateEMA(data: number[], period: number): number[] {
  if (!data || data.length < period) {
    return Array(data?.length || 0).fill(NaN);
  }

  const result: number[] = [];
  const multiplier = 2 / (period + 1);

  // Calculate initial SMA for the first EMA value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += data[i];
  }
  let ema = sum / period;

  // Fill initial values with NaN
  for (let i = 0; i < period - 1; i++) {
    result.push(NaN);
  }

  // First EMA value is the SMA
  result.push(ema);

  // Calculate subsequent EMA values
  for (let i = period; i < data.length; i++) {
    ema = (data[i] - ema) * multiplier + ema;
    result.push(ema);
  }

  return result;
}

/**
 * Calculate Relative Strength Index (RSI)
 * @param data - Array of price data
 * @param period - Period for the RSI (typically 14)
 * @returns Array of RSI values
 */
export function calculateRSI(data: number[], period: number = 14): number[] {
  if (!data || data.length < period + 1) {
    console.warn(`RSI calculation requires at least ${period + 1} data points, got ${data?.length || 0}`);
    return [];
  }

  const result: number[] = [];
  const gains: number[] = [];
  const losses: number[] = [];

  // Calculate price changes
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }

  console.log(`RSI calculation: ${data.length} data points, ${gains.length} price changes`);

  // Calculate initial average gain and loss for the first RSI value
  let avgGain = 0;
  let avgLoss = 0;

  for (let i = 0; i < period; i++) {
    avgGain += gains[i];
    avgLoss += losses[i];
  }

  avgGain /= period;
  avgLoss /= period;

  console.log(`Initial averages: gain=${avgGain.toFixed(6)}, loss=${avgLoss.toFixed(6)}`);

  // Calculate RSI values
  for (let i = 0; i < data.length; i++) {
    if (i < period) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    if (i === period) {
      // First RSI value uses the initial averages calculated above
      if (avgLoss === 0) {
        result.push(100); // No losses, RSI is 100
      } else {
        const rs = avgGain / avgLoss;
        const rsi = 100 - (100 / (1 + rs));
        result.push(rsi);
        console.log(`First RSI value: ${rsi.toFixed(2)} (RS=${rs.toFixed(6)})`);
      }
    } else {
      // Subsequent values use smoothed averages (Wilder's smoothing)
      avgGain = ((avgGain * (period - 1)) + gains[i - 1]) / period;
      avgLoss = ((avgLoss * (period - 1)) + losses[i - 1]) / period;

      if (avgLoss === 0) {
        result.push(100); // No losses, RSI is 100
      } else {
        const rs = avgGain / avgLoss;
        const rsi = 100 - (100 / (1 + rs));
        result.push(rsi);
      }
    }
  }

  const latestRsi = result[result.length - 1];
  console.log(`Latest RSI: ${latestRsi} (from ${result.length} calculated values)`);

  return result;
}

/**
 * Calculate Moving Average Convergence Divergence (MACD)
 * @param data - Array of price data
 * @param fastPeriod - Fast EMA period (typically 12)
 * @param slowPeriod - Slow EMA period (typically 26)
 * @param signalPeriod - Signal EMA period (typically 9)
 * @returns Object with MACD line, signal line, and histogram
 */
export function calculateMACD(
  data: number[],
  fastPeriod: number = 12,
  slowPeriod: number = 26,
  signalPeriod: number = 9
): { macd: number[], signal: number[], histogram: number[] } {
  if (!data || data.length < slowPeriod + signalPeriod) {
    console.warn(`MACD calculation requires at least ${slowPeriod + signalPeriod} data points, got ${data?.length || 0}`);
    return {
      macd: Array(data?.length || 0).fill(NaN),
      signal: Array(data?.length || 0).fill(NaN),
      histogram: Array(data?.length || 0).fill(NaN)
    };
  }

  // Calculate fast and slow EMAs
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);

  console.log(`MACD calculation: ${data.length} data points, fast EMA period: ${fastPeriod}, slow EMA period: ${slowPeriod}`);

  // Calculate MACD line (fast EMA - slow EMA)
  const macdLine: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (isNaN(fastEMA[i]) || isNaN(slowEMA[i])) {
      macdLine.push(NaN);
    } else {
      macdLine.push(fastEMA[i] - slowEMA[i]);
    }
  }

  // Calculate signal line (EMA of MACD line)
  // We need to calculate EMA on the MACD values, preserving the original array structure
  const signalLine: number[] = [];
  const macdMultiplier = 2 / (signalPeriod + 1);
  let signalEMA: number | null = null;

  for (let i = 0; i < macdLine.length; i++) {
    if (isNaN(macdLine[i])) {
      signalLine.push(NaN);
      continue;
    }

    if (signalEMA === null) {
      // Find enough valid MACD values to calculate initial SMA for signal line
      const validMacdValues: number[] = [];
      for (let j = i; j < macdLine.length && validMacdValues.length < signalPeriod; j++) {
        if (!isNaN(macdLine[j])) {
          validMacdValues.push(macdLine[j]);
        }
      }

      if (validMacdValues.length < signalPeriod) {
        signalLine.push(NaN);
        continue;
      }

      // Calculate initial SMA for signal line
      signalEMA = validMacdValues.slice(0, signalPeriod).reduce((sum, val) => sum + val, 0) / signalPeriod;

      // Fill previous signal values with NaN
      for (let k = 0; k < signalPeriod - 1; k++) {
        if (k < i) {
          // These were already filled, skip
        }
      }

      signalLine.push(signalEMA);
    } else {
      // Calculate EMA for signal line
      signalEMA = (macdLine[i] - signalEMA) * macdMultiplier + signalEMA;
      signalLine.push(signalEMA);
    }
  }

  // Calculate histogram (MACD line - signal line)
  const histogram: number[] = [];
  for (let i = 0; i < macdLine.length; i++) {
    if (isNaN(macdLine[i]) || isNaN(signalLine[i])) {
      histogram.push(NaN);
    } else {
      histogram.push(macdLine[i] - signalLine[i]);
    }
  }

  const latestMacd = macdLine[macdLine.length - 1];
  const latestSignal = signalLine[signalLine.length - 1];
  const latestHistogram = histogram[histogram.length - 1];

  console.log(`Latest MACD: ${latestMacd?.toFixed(4)}, Signal: ${latestSignal?.toFixed(4)}, Histogram: ${latestHistogram?.toFixed(4)}`);

  return {
    macd: macdLine,
    signal: signalLine,
    histogram
  };
}

/**
 * Calculate Volume Weighted Average Price (VWAP)
 * @param high - Array of high prices
 * @param low - Array of low prices
 * @param close - Array of close prices
 * @param volume - Array of volume data
 * @param timestamps - Array of timestamps (optional, for session detection)
 * @param period - Period for rolling VWAP calculation (optional)
 * @returns Array of VWAP values
 */
export function calculateVWAP(
  high: number[],
  low: number[],
  close: number[],
  volume: number[],
  period?: number,
  timestamps?: number[]
): number[] {
  if (!high || !low || !close || !volume ||
      high.length !== low.length ||
      low.length !== close.length ||
      close.length !== volume.length) {
    console.warn('VWAP calculation requires equal length arrays for high, low, close, and volume');
    return Array(high?.length || 0).fill(NaN);
  }

  // Validate volume data
  const hasValidVolume = volume.some(v => v > 0);
  if (!hasValidVolume) {
    console.warn('VWAP calculation: No valid volume data found, all volumes are zero or negative');
    return Array(high.length).fill(NaN);
  }

  // CRITICAL: Ensure data is in chronological order (oldest first)
  // Polygon API sometimes returns data in reverse order, which breaks VWAP calculation
  let sortedIndices = Array.from({ length: high.length }, (_, i) => i);

  if (timestamps && timestamps.length === high.length) {
    // Sort by timestamp to ensure chronological order
    sortedIndices.sort((a, b) => timestamps[a] - timestamps[b]);

    // Check if data was already sorted
    const wasSorted = sortedIndices.every((index, i) => index === i);
    if (!wasSorted) {
      console.log('VWAP: Data was not in chronological order, sorting by timestamp');

      // Reorder all arrays to be chronological
      const sortedHigh = sortedIndices.map(i => high[i]);
      const sortedLow = sortedIndices.map(i => low[i]);
      const sortedClose = sortedIndices.map(i => close[i]);
      const sortedVolume = sortedIndices.map(i => volume[i]);
      const sortedTimestamps = sortedIndices.map(i => timestamps[i]);

      // Replace original arrays with sorted ones
      high = sortedHigh;
      low = sortedLow;
      close = sortedClose;
      volume = sortedVolume;
      timestamps = sortedTimestamps;
    }
  } else {
    console.warn('VWAP: No timestamps provided, assuming data is already in chronological order');
  }

  const result: number[] = [];

  console.log(`VWAP calculation: ${close.length} data points${period ? `, rolling period: ${period}` : ' (session-based)'}`);
  console.log(`Volume range: ${Math.min(...volume)} to ${Math.max(...volume)}`);
  console.log(`Price range: ${Math.min(...close).toFixed(2)} to ${Math.max(...close).toFixed(2)}`);

  if (period && period > 0) {
    // Rolling VWAP calculation over specified period
    for (let i = 0; i < close.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
        continue;
      }

      let totalPriceVolume = 0;
      let totalVolume = 0;

      for (let j = i - period + 1; j <= i; j++) {
        // Skip if volume is invalid
        if (volume[j] <= 0) {
          continue;
        }

        const typicalPrice = (high[j] + low[j] + close[j]) / 3;
        totalPriceVolume += typicalPrice * volume[j];
        totalVolume += volume[j];
      }

      if (totalVolume === 0) {
        result.push(NaN);
      } else {
        result.push(totalPriceVolume / totalVolume);
      }
    }
  } else {
    // Session-based VWAP calculation
    // For intraday data, VWAP typically resets at market open
    // For daily data, we'll use cumulative VWAP from start of data

    let sessionPriceVolume = 0;
    let sessionVolume = 0;
    let lastSessionDate: string | null = null;

    for (let i = 0; i < close.length; i++) {
      // Check if we need to reset for a new session (new day)
      if (timestamps && timestamps[i]) {
        const currentDate = new Date(timestamps[i]).toDateString();

        if (lastSessionDate && currentDate !== lastSessionDate) {
          // New session detected, reset cumulative values
          sessionPriceVolume = 0;
          sessionVolume = 0;
          console.log(`VWAP: New session detected at index ${i}, resetting cumulative values`);
        }

        lastSessionDate = currentDate;
      }

      // Skip if volume is invalid
      if (volume[i] <= 0) {
        result.push(i > 0 ? result[i - 1] : NaN); // Use previous VWAP if available
        continue;
      }

      const typicalPrice = (high[i] + low[i] + close[i]) / 3;
      sessionPriceVolume += typicalPrice * volume[i];
      sessionVolume += volume[i];

      if (sessionVolume === 0) {
        result.push(NaN);
      } else {
        const vwap = sessionPriceVolume / sessionVolume;
        result.push(vwap);
      }
    }
  }

  const latestVwap = result[result.length - 1];
  const validVwapCount = result.filter(v => !isNaN(v)).length;

  console.log(`VWAP calculation complete: ${validVwapCount}/${result.length} valid values`);
  console.log(`Latest VWAP: ${latestVwap?.toFixed(4)}`);

  // Debug: Show first few and last few VWAP values for verification
  if (result.length > 0) {
    const firstFew = result.slice(0, 3).map(v => isNaN(v) ? 'NaN' : v.toFixed(4));
    const lastFew = result.slice(-3).map(v => isNaN(v) ? 'NaN' : v.toFixed(4));
    console.log(`First 3 VWAP values: [${firstFew.join(', ')}]`);
    console.log(`Last 3 VWAP values: [${lastFew.join(', ')}]`);

    // Show corresponding prices and volumes for context
    const firstPrices = close.slice(0, 3).map(p => p.toFixed(2));
    const lastPrices = close.slice(-3).map(p => p.toFixed(2));
    const firstVolumes = volume.slice(0, 3).map(v => v.toLocaleString());
    const lastVolumes = volume.slice(-3).map(v => v.toLocaleString());

    console.log(`First 3 close prices: [${firstPrices.join(', ')}]`);
    console.log(`Last 3 close prices: [${lastPrices.join(', ')}]`);
    console.log(`First 3 volumes: [${firstVolumes.join(', ')}]`);
    console.log(`Last 3 volumes: [${lastVolumes.join(', ')}]`);
  }

  return result;
}

/**
 * Calculate Bollinger Bands
 * @param data - Array of price data
 * @param period - Period for the SMA (typically 20)
 * @param multiplier - Standard deviation multiplier (typically 2)
 * @returns Object with upper band, middle band (SMA), and lower band
 */
function calculateBollingerBands(
  data: number[],
  period: number = 20,
  multiplier: number = 2
): { upper: number[], middle: number[], lower: number[] } {
  const middle = calculateSMA(data, period);
  const upper: number[] = [];
  const lower: number[] = [];

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upper.push(NaN);
      lower.push(NaN);
      continue;
    }

    // Calculate standard deviation
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += Math.pow(data[i - j] - middle[i], 2);
    }
    const stdDev = Math.sqrt(sum / period);

    upper.push(middle[i] + multiplier * stdDev);
    lower.push(middle[i] - multiplier * stdDev);
  }

  return { upper, middle, lower };
}

/**
 * Calculate Average True Range (ATR)
 * @param high - Array of high prices
 * @param low - Array of low prices
 * @param close - Array of close prices
 * @param period - Period for the ATR (typically 14)
 * @returns Array of ATR values
 */
function calculateATR(
  high: number[],
  low: number[],
  close: number[],
  period: number = 14
): number[] {
  const trueRanges: number[] = [];
  const result: number[] = [];

  // Calculate true ranges
  for (let i = 0; i < high.length; i++) {
    if (i === 0) {
      trueRanges.push(high[i] - low[i]); // First TR is simply the high - low
    } else {
      // TR = max(high - low, abs(high - prevClose), abs(low - prevClose))
      const tr1 = high[i] - low[i];
      const tr2 = Math.abs(high[i] - close[i - 1]);
      const tr3 = Math.abs(low[i] - close[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }

  // Calculate ATR
  for (let i = 0; i < high.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    if (i === period - 1) {
      // First ATR is simple average of TR values
      const sum = trueRanges.slice(0, period).reduce((acc, tr) => acc + tr, 0);
      result.push(sum / period);
      continue;
    }

    // Subsequent ATR values use smoothed average
    // ATR = ((Previous ATR * (period - 1)) + Current TR) / period
    result.push((result[i - 1] * (period - 1) + trueRanges[i]) / period);
  }

  return result;
}

/**
 * Calculate Stochastic Oscillator
 * @param high - Array of high prices
 * @param low - Array of low prices
 * @param close - Array of close prices
 * @param kPeriod - %K period (typically 14)
 * @param dPeriod - %D period (typically 3)
 * @returns Object with %K and %D values
 */
function calculateStochastic(
  high: number[],
  low: number[],
  close: number[],
  kPeriod: number = 14,
  dPeriod: number = 3
): { k: number[], d: number[] } {
  const k: number[] = [];

  // Calculate %K
  for (let i = 0; i < close.length; i++) {
    if (i < kPeriod - 1) {
      k.push(NaN); // Not enough data for the period
      continue;
    }

    // Find highest high and lowest low in the period
    let highestHigh = -Infinity;
    let lowestLow = Infinity;

    for (let j = 0; j < kPeriod; j++) {
      highestHigh = Math.max(highestHigh, high[i - j]);
      lowestLow = Math.min(lowestLow, low[i - j]);
    }

    // %K = ((Close - Lowest Low) / (Highest High - Lowest Low)) * 100
    if (highestHigh === lowestLow) {
      k.push(100); // Avoid division by zero
    } else {
      k.push(((close[i] - lowestLow) / (highestHigh - lowestLow)) * 100);
    }
  }

  // Calculate %D (SMA of %K)
  const d = calculateSMA(k.filter(val => !isNaN(val)), dPeriod);

  // Pad %D with NaN values to match the length of %K
  const paddedD: number[] = Array(k.length).fill(NaN);
  const validKCount = k.filter(val => !isNaN(val)).length;
  const dStartIndex = k.length - validKCount + dPeriod - 1;

  for (let i = 0; i < d.length; i++) {
    paddedD[dStartIndex + i] = d[i];
  }

  return { k, d: paddedD };
}

/**
 * Calculate all required indicators
 * @param historicalData - Historical price data
 * @param indicators - Array of indicator names to calculate
 * @returns Object with calculated indicators
 */
export function calculateIndicators(
  historicalData: {
    open: number[];
    high: number[];
    low: number[];
    close: number[];
    volume: number[];
    timestamp: number[];
  },
  indicators: string[]
): Record<string, any> {
  const result: Record<string, any> = {};

  console.log(`Calculating indicators: ${indicators.join(', ')}`);

  // Check if we have enough data to calculate indicators
  if (!historicalData.close || historicalData.close.length === 0) {
    console.error('No historical close data available for indicator calculation');
    return result;
  }

  console.log(`Historical data available: ${historicalData.close.length} data points`);

  for (const indicator of indicators) {
    const normalizedIndicator = indicator.toLowerCase().trim();
    console.log(`Processing indicator: ${normalizedIndicator}`);

    switch (normalizedIndicator) {
      case 'sma':
        result.sma = {
          sma20: calculateSMA(historicalData.close, 20),
          sma50: calculateSMA(historicalData.close, 50),
          sma200: calculateSMA(historicalData.close, 200)
        };
        break;
      case 'ema':
        result.ema = {
          ema12: calculateEMA(historicalData.close, 12),
          ema26: calculateEMA(historicalData.close, 26),
          ema50: calculateEMA(historicalData.close, 50),
          ema200: calculateEMA(historicalData.close, 200)
        };
        break;
      case 'rsi':
        result.rsi = calculateRSI(historicalData.close);
        break;
      case 'macd':
        result.macd = calculateMACD(historicalData.close);
        break;
      case 'vwap':
        result.vwap = calculateVWAP(
          historicalData.high,
          historicalData.low,
          historicalData.close,
          historicalData.volume
        );
        break;
      case 'bollinger':
        result.bollinger = calculateBollingerBands(historicalData.close);
        break;
      case 'atr':
        result.atr = calculateATR(
          historicalData.high,
          historicalData.low,
          historicalData.close
        );
        break;
      case 'stochastic':
        result.stochastic = calculateStochastic(
          historicalData.high,
          historicalData.low,
          historicalData.close
        );
        break;
      // Add more indicators as needed
    }
  }

  return result;
}
