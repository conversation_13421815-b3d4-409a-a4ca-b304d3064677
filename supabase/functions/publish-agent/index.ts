// Publish Agent Edge Function
// This function handles publishing user agents to the marketplace

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

interface PublishAgentRequest {
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header provided');
    }

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication token');
    }

    // Parse request body
    const requestData: PublishAgentRequest = await req.json();
    
    // Validate required fields
    if (!requestData.agentId || !requestData.name || !requestData.category) {
      throw new Error('Missing required fields: agentId, name, and category are required');
    }

    console.log(`Publishing agent ${requestData.agentId} for user ${user.id}`);

    // Verify the agent exists and belongs to the user
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', requestData.agentId)
      .eq('user_id', user.id)
      .single();

    if (agentError || !agent) {
      throw new Error('Agent not found or you do not have permission to publish it');
    }

    // Verify the category exists
    const { data: category, error: categoryError } = await supabase
      .from('agent_categories')
      .select('name')
      .eq('name', requestData.category)
      .eq('is_active', true)
      .single();

    if (categoryError || !category) {
      throw new Error('Invalid category specified');
    }

    // Check if agent is already published by this user
    const { data: existingPublished, error: existingError } = await supabase
      .from('published_agents')
      .select('id')
      .eq('agent_id', requestData.agentId)
      .eq('publisher_id', user.id)
      .maybeSingle();

    if (existingError && existingError.code !== 'PGRST116') {
      throw new Error('Error checking existing published agent');
    }

    let publishedAgent;

    if (existingPublished) {
      // Update existing published agent
      const { data: updatedAgent, error: updateError } = await supabase
        .from('published_agents')
        .update({
          name: requestData.name,
          description: requestData.description || null,
          category: requestData.category,
          tags: requestData.tags || [],
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPublished.id)
        .select('*')
        .single();

      if (updateError) {
        throw new Error(`Failed to update published agent: ${updateError.message}`);
      }

      publishedAgent = updatedAgent;
      console.log(`Updated published agent ${existingPublished.id}`);
    } else {
      // Create new published agent
      const { data: newAgent, error: insertError } = await supabase
        .from('published_agents')
        .insert({
          agent_id: requestData.agentId,
          publisher_id: user.id,
          name: requestData.name,
          description: requestData.description || null,
          category: requestData.category,
          tags: requestData.tags || [],
          is_active: true
        })
        .select('*')
        .single();

      if (insertError) {
        throw new Error(`Failed to publish agent: ${insertError.message}`);
      }

      publishedAgent = newAgent;
      console.log(`Created new published agent ${newAgent.id}`);
    }

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        publishedAgent: publishedAgent,
        message: existingPublished ? 'Agent updated successfully' : 'Agent published successfully'
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error in publish-agent function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
