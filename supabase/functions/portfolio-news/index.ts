// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_deno_apps

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

// Define CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    // Get the JWT from the Authorization header
    const authHeader = req.headers.get('Authorization');
    let userId = null;

    if (authHeader) {
      // Try to get the user from the JWT
      try {
        // The token is in the format 'Bearer <token>'
        const token = authHeader.replace('Bearer ', '');

        // Verify the JWT token
        const { data, error } = await supabaseClient.auth.getUser(token);

        if (error) {
          console.error('Error verifying JWT:', error);
        } else if (data.user) {
          userId = data.user.id;
        }
      } catch (error) {
        console.error('Error processing authentication:', error);
      }
    }

    // All actions require authentication
    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Parse the request body
    const requestBody = await req.json();
    console.log('Request body:', JSON.stringify(requestBody));

    const { action, portfolioId, stockTickers } = requestBody;

    // Handle different actions
    switch (action) {
      case 'getPortfolioNews':
        console.log(`Processing getPortfolioNews for portfolioId: ${portfolioId}, userId: ${userId}`);
        if (stockTickers && stockTickers.length > 0) {
          console.log(`Using provided stockTickers: ${JSON.stringify(stockTickers)}`);
        }
        return await getPortfolioNews(supabaseClient, userId, portfolioId, stockTickers);
      default:
        console.log(`Invalid action: ${action}`);
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
  } catch (error) {
    console.error('Error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Helper function to get a unique image from Google Images based on article title
async function getGoogleImage(article: any, ticker: string, usedImages: Set<string>): Promise<string> {
  try {
    // Get Google Custom Search API key and Search Engine ID
    const googleApiKey = Deno.env.get('GOOGLE_API_KEY');
    const searchEngineId = Deno.env.get('GOOGLE_SEARCH_ENGINE_ID');

    if (!googleApiKey || !searchEngineId) {
      throw new Error('Google API key or Search Engine ID not configured');
    }
    // Extract title and create a search query
    const title = article.title || '';
    const description = article.description || '';

    // Create a search query based on the article title and ticker
    // This will help find images that are directly relevant to the article
    let searchQuery = `${title} ${ticker} stock news`;

    // If the search query is too long, truncate it
    if (searchQuery.length > 100) {
      searchQuery = searchQuery.substring(0, 100);
    }

    // Add a random seed to ensure we get different images for similar queries
    const randomSeed = Math.floor(Math.random() * 1000);

    // Call Google Custom Search API to search for images
    const url = `https://www.googleapis.com/customsearch/v1?key=${googleApiKey}&cx=${searchEngineId}&q=${encodeURIComponent(searchQuery)}&searchType=image&num=10&imgSize=large&safe=active&start=${randomSeed % 10 + 1}`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Google API error: ${response.statusText}`);
    }

    const data = await response.json();

    // Check if we got any image results
    if (data.items && data.items.length > 0) {
      // Get all image URLs
      const imageUrls = data.items.map((item: any) => item.link);

      // Filter out any images that have already been used
      const unusedImages = imageUrls.filter((url: string) => !usedImages.has(url));

      // If we have unused images, return one of them
      if (unusedImages.length > 0) {
        // Get a random image from the unused images
        const randomIndex = Math.floor(Math.random() * unusedImages.length);
        const selectedImage = unusedImages[randomIndex];

        // Add the selected image to the used images set
        usedImages.add(selectedImage);
        return selectedImage;
      }
    }

    // If we couldn't get a Google image or all images are used, fall back to a stock image
    throw new Error('No suitable Google images found');
  } catch (error) {
    console.error('Error getting Google image:', error);

    // Fall back to a stock image based on the ticker and article content
    const title = article.title || '';
    const description = article.description || '';
    const content = (title + ' ' + description).toLowerCase();

    // Extract keywords from content for better image search
    const keywords = extractKeywords(content, ticker);

    // Generate a unique search query based on article content
    const searchQuery = keywords.join(',');

    // Create a unique image URL with a timestamp to ensure uniqueness
    const timestamp = Date.now();
    const articleId = article.id || title.substring(0, 10).replace(/\s+/g, '-');
    const randomSeed = Math.floor(Math.random() * 1000000);

    // Use a stock photo API as fallback
    const fallbackImage = `https://source.unsplash.com/featured/800x600/?${encodeURIComponent(searchQuery)}&t=${timestamp}&id=${articleId}&seed=${randomSeed}`;

    usedImages.add(fallbackImage);
    return fallbackImage;
  }
}

// Extract relevant keywords from article content
function extractKeywords(content: string, ticker: string): string[] {
  // Define categories of keywords to look for
  const keywordCategories = {
    finance: ['finance', 'stock', 'market', 'trading', 'investment', 'money', 'bank', 'financial'],
    tech: ['technology', 'software', 'digital', 'computer', 'ai', 'cloud', 'tech', 'data'],
    auto: ['car', 'vehicle', 'automotive', 'electric', 'ev', 'driving'],
    health: ['health', 'medical', 'medicine', 'healthcare', 'pharma', 'drug', 'biotech'],
    energy: ['energy', 'oil', 'gas', 'power', 'renewable', 'solar', 'wind'],
    retail: ['retail', 'store', 'shopping', 'consumer', 'product', 'ecommerce'],
    food: ['food', 'restaurant', 'grocery', 'beverage', 'coffee'],
    realestate: ['property', 'real estate', 'housing', 'mortgage', 'building'],
    media: ['media', 'entertainment', 'streaming', 'content', 'movie', 'show'],
    telecom: ['telecom', 'communication', 'phone', 'mobile', 'wireless', '5g'],
    industrial: ['industrial', 'manufacturing', 'factory', 'production'],
    business: ['business', 'company', 'corporate', 'industry', 'enterprise']
  };

  // Find matching keywords in the content
  const foundKeywords: string[] = [];

  // Add the ticker as a keyword
  foundKeywords.push(ticker);

  // Add 'stock market' as a default keyword for financial context
  foundKeywords.push('stock market');

  // Check for category keywords in the content
  for (const [category, keywords] of Object.entries(keywordCategories)) {
    for (const keyword of keywords) {
      if (content.includes(keyword)) {
        // Add both the category and the specific keyword
        foundKeywords.push(category);
        foundKeywords.push(keyword);
        break; // Only add each category once
      }
    }
  }

  // Extract potential keywords from the content (words longer than 4 characters)
  const contentWords = content.split(/\s+/) || [];
  for (const word of contentWords) {
    if (word.length > 4 && !foundKeywords.includes(word)) {
      foundKeywords.push(word.toLowerCase());
    }
  }

  // If we don't have enough keywords, add some general business/finance terms
  if (foundKeywords.length < 3) {
    foundKeywords.push('business');
    foundKeywords.push('finance');
  }

  // Limit to 5 keywords maximum to avoid too specific searches
  return foundKeywords.slice(0, 5);
}

// Helper function to get an image based on article content (not used anymore, kept for reference)
function _getArticleImage(article: any, ticker: string, attemptNumber: number = 0): string {
  // Map of tickers to industry categories
  const tickerToIndustry: Record<string, string> = {
    // Technology
    'AAPL': 'technology',
    'MSFT': 'technology',
    'GOOGL': 'technology',
    'GOOG': 'technology',
    'META': 'technology',
    'FB': 'technology',
    'AMZN': 'ecommerce',
    'NVDA': 'technology',
    'INTC': 'technology',
    'AMD': 'technology',
    'CSCO': 'technology',
    'ADBE': 'technology',
    'CRM': 'technology',
    'ORCL': 'technology',
    'IBM': 'technology',
    'QCOM': 'technology',

    // Automotive
    'TSLA': 'automotive',
    'F': 'automotive',
    'GM': 'automotive',
    'TM': 'automotive',

    // Finance
    'JPM': 'finance',
    'BAC': 'finance',
    'WFC': 'finance',
    'C': 'finance',
    'GS': 'finance',
    'MS': 'finance',
    'V': 'finance',
    'MA': 'finance',
    'PYPL': 'finance',
    'AXP': 'finance',
    'BLK': 'finance',
    'SCHW': 'finance',

    // Retail
    'WMT': 'retail',
    'TGT': 'retail',
    'COST': 'retail',
    'HD': 'retail',
    'LOW': 'retail',
    'SBUX': 'food',
    'MCD': 'food',
    'NKE': 'retail',

    // Healthcare
    'JNJ': 'healthcare',
    'PFE': 'healthcare',
    'MRK': 'healthcare',
    'UNH': 'healthcare',
    'CVS': 'healthcare',

    // Telecom
    'T': 'telecom',
    'VZ': 'telecom',

    // Entertainment
    'DIS': 'entertainment',
    'NFLX': 'entertainment',

    // Industrial
    'GE': 'industrial',
    'BA': 'aerospace',

    // Consumer Goods
    'PEP': 'consumer',
    'KO': 'consumer',

    // ETFs
    'SPY': 'index',
    'QQQ': 'index',
    'IWM': 'index',
    'VTI': 'index',
    'VOO': 'index',
    'VEA': 'index',
    'VWO': 'index',
    'BND': 'bonds',
    'AGG': 'bonds',
    'GLD': 'commodities',
    'SLV': 'commodities',
    'USO': 'commodities',
    'XLE': 'energy',
    'XLF': 'finance',
    'XLK': 'technology',
    'XLV': 'healthcare',
    'XLI': 'industrial',
    'XLP': 'consumer',
    'XLY': 'consumer',
    'XLB': 'materials',
    'XLU': 'utilities',
    'XLRE': 'realestate',
    'XLC': 'communication',
  };

  // Collection of high-quality stock images by industry
  const industryImages: Record<string, string[]> = {
    'technology': [
      'https://images.unsplash.com/photo-1518770660439-4636190af475?w=800&q=80', // Tech circuit board
      'https://images.unsplash.com/photo-**********-4bd374c3f58b?w=800&q=80', // Server room
      'https://images.unsplash.com/photo-1496171367470-9ed9a91ea931?w=800&q=80', // Laptop and coffee
      'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=800&q=80', // Code on screen
      'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&q=80', // Circuit board close-up
    ],
    'ecommerce': [
      'https://images.unsplash.com/photo-**********-824ae1b704d3?w=800&q=80', // Online shopping
      'https://images.unsplash.com/photo-1565104781149-56866f5d1d87?w=800&q=80', // Packages
      'https://images.unsplash.com/photo-1472851294608-062f824d29cc?w=800&q=80', // Warehouse
      'https://images.unsplash.com/photo-1607083206968-13611e3d76db?w=800&q=80', // Shopping cart
    ],
    'automotive': [
      'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&q=80', // Sports car
      'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&q=80', // Car factory
      'https://images.unsplash.com/photo-*************-40f8afb4b4d8?w=800&q=80', // EV charging
      'https://images.unsplash.com/photo-*************-fd3cad34687c?w=800&q=80', // Car dashboard
    ],
    'finance': [
      'https://images.unsplash.com/photo-*************-afdab827c52f?w=800&q=80', // Stock market data
      'https://images.unsplash.com/photo-*************-026b92b2d70b?w=800&q=80', // Credit cards
      'https://images.unsplash.com/photo-*************-23d4b2565df0?w=800&q=80', // Banking
      'https://images.unsplash.com/photo-*************-a35d0e7ab9b6?w=800&q=80', // Money/coins
    ],
    'retail': [
      'https://images.unsplash.com/photo-*************-76b7b1e5a7a5?w=800&q=80', // Shopping mall
      'https://images.unsplash.com/photo-*************-494d7ddbf7e0?w=800&q=80', // Shopping bags
      'https://images.unsplash.com/photo-*************-3205f6a55735?w=800&q=80', // Store interior
      'https://images.unsplash.com/photo-*************-0a96f2a4b9da?w=800&q=80', // Clothing rack
    ],
    'food': [
      'https://images.unsplash.com/photo-*************-4c7edcad34c4?w=800&q=80', // Restaurant
      'https://images.unsplash.com/photo-**********-52f8b828add9?w=800&q=80', // Coffee
      'https://images.unsplash.com/photo-*************-7b61b0ac49cb?w=800&q=80', // Food packaging
      'https://images.unsplash.com/photo-*************-dad2ebd01d17?w=800&q=80', // Fast food
    ],
    'healthcare': [
      'https://images.unsplash.com/photo-*************-2173dba999ef?w=800&q=80', // Medical research
      'https://images.unsplash.com/photo-1584982751601-97dcc096659c?w=800&q=80', // Medicine
      'https://images.unsplash.com/photo-1530497610245-94d3c16cda28?w=800&q=80', // Healthcare tech
      'https://images.unsplash.com/photo-1579684385127-1ef15d508118?w=800&q=80', // Lab research
    ],
    'telecom': [
      'https://images.unsplash.com/photo-**********-b99a580bb7a8?w=800&q=80', // Cell tower
      'https://images.unsplash.com/photo-**********-a72e53ae2d4f?w=800&q=80', // Fiber optics
      'https://images.unsplash.com/photo-**********-77bd85671d30?w=800&q=80', // Network cables
      'https://images.unsplash.com/photo-1563770557593-bda3e9a89ff1?w=800&q=80', // Mobile phones
    ],
    'entertainment': [
      'https://images.unsplash.com/photo-1478720568477-152d9b164e26?w=800&q=80', // Movie theater
      'https://images.unsplash.com/photo-1522869635100-9f4c5e86aa37?w=800&q=80', // Streaming
      'https://images.unsplash.com/photo-1603739903239-8b6e64c3b185?w=800&q=80', // TV production
      'https://images.unsplash.com/photo-1585647347384-2593bc35786b?w=800&q=80', // Entertainment at home
    ],
    'industrial': [
      'https://images.unsplash.com/photo-1504917595217-d4dc5ebe6122?w=800&q=80', // Factory
      'https://images.unsplash.com/photo-1565715101841-4e7ce0a8490b?w=800&q=80', // Industrial equipment
      'https://images.unsplash.com/photo-1542013936693-884638332954?w=800&q=80', // Manufacturing
      'https://images.unsplash.com/photo-1581093450021-4a7360e9a6b5?w=800&q=80', // Industrial worker
    ],
    'aerospace': [
      'https://images.unsplash.com/photo-1559128010-7c1ad6e1b6a5?w=800&q=80', // Airplane
      'https://images.unsplash.com/photo-1518623489648-a173ef7824f3?w=800&q=80', // Jet engine
      'https://images.unsplash.com/photo-1557800636-894a64c1696f?w=800&q=80', // Aerospace manufacturing
      'https://images.unsplash.com/photo-1566431483056-e6f4d1f26093?w=800&q=80', // Cockpit
    ],
    'consumer': [
      'https://images.unsplash.com/photo-1534349762230-e0cadf78f5da?w=800&q=80', // Grocery store
      'https://images.unsplash.com/photo-1553456558-aff63285bdd1?w=800&q=80', // Soft drinks
      'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?w=800&q=80', // Consumer products
      'https://images.unsplash.com/photo-1550989460-0adf9ea622e2?w=800&q=80', // Shopping cart
    ],
    'index': [
      'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&q=80', // Stock market
      'https://images.unsplash.com/photo-1535320903710-d993d3d77d29?w=800&q=80', // Bull statue
      'https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&q=80', // Trading screen
      'https://images.unsplash.com/photo-1569025690938-a00729c9e1f9?w=800&q=80', // Financial district
    ],
    'bonds': [
      'https://images.unsplash.com/photo-1621981386829-0d47118ae9f0?w=800&q=80', // Financial documents
      'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&q=80', // Treasury building
      'https://images.unsplash.com/photo-1633158829875-e5316a358c6f?w=800&q=80', // Financial data
      'https://images.unsplash.com/photo-1618044733300-9472054094ee?w=800&q=80', // Government building
    ],
    'commodities': [
      'https://images.unsplash.com/photo-1610375461369-d613b2a0a3f0?w=800&q=80', // Gold bars
      'https://images.unsplash.com/photo-1589735556200-d3f8bf7fa9c7?w=800&q=80', // Silver
      'https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=800&q=80', // Commodities trading
      'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&q=80', // Raw materials
    ],
    'energy': [
      'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=800&q=80', // Oil rig
      'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=800&q=80', // Solar panels
      'https://images.unsplash.com/photo-1548337138-e87d889cc369?w=800&q=80', // Wind turbines
      'https://images.unsplash.com/photo-1513828583688-c52646db42da?w=800&q=80', // Power plant
    ],
    'materials': [
      'https://images.unsplash.com/photo-1518391846015-55a9cc003b25?w=800&q=80', // Construction materials
      'https://images.unsplash.com/photo-1530982011887-3cc11cc85693?w=800&q=80', // Mining
      'https://images.unsplash.com/photo-1587293852726-70cdb56c2866?w=800&q=80', // Metal production
      'https://images.unsplash.com/photo-1565116175827-64847f972a3f?w=800&q=80', // Chemical plant
    ],
    'utilities': [
      'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=800&q=80', // Power lines
      'https://images.unsplash.com/photo-1509390144018-eed40ef9b3b7?w=800&q=80', // Electrical grid
      'https://images.unsplash.com/photo-1548083085-b2346b1cd91b?w=800&q=80', // Water treatment
      'https://images.unsplash.com/photo-1591964006776-90a7b9d17227?w=800&q=80', // Gas utility
    ],
    'realestate': [
      'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&q=80', // Modern building
      'https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&q=80', // Real estate development
      'https://images.unsplash.com/photo-1460317442991-0ec209397118?w=800&q=80', // City skyline
      'https://images.unsplash.com/photo-1448630360428-65456885c650?w=800&q=80', // Construction site
    ],
    'communication': [
      'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&q=80', // Communication tech
      'https://images.unsplash.com/photo-1557682250-33bd709cbe85?w=800&q=80', // Team meeting
      'https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=800&q=80', // People with phones
      'https://images.unsplash.com/photo-1577563908411-5077b6dc7624?w=800&q=80', // Media broadcasting
    ],
    // Default category for any ticker not specifically mapped
    'default': [
      'https://images.unsplash.com/photo-1535320903710-d993d3d77d29?w=800&q=80', // Bull statue
      'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&q=80', // Stock market
      'https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&q=80', // Trading screen
      'https://images.unsplash.com/photo-1569025690938-a00729c9e1f9?w=800&q=80', // Financial district
      'https://images.unsplash.com/photo-*************-afdab827c52f?w=800&q=80', // Stock chart
    ]
  };

  // First, try to find a relevant image based on the article title and description
  const title = article.title || '';
  const description = article.description || '';
  const content = (title + ' ' + description).toLowerCase();

  // Define keyword mappings to image categories
  const keywordToCategory: Record<string, string> = {
    // Technology keywords
    'ai': 'technology', 'artificial intelligence': 'technology', 'software': 'technology',
    'app': 'technology', 'tech': 'technology', 'digital': 'technology', 'cloud': 'technology',
    'computing': 'technology', 'computer': 'technology', 'internet': 'technology',
    'chip': 'technology', 'semiconductor': 'technology', 'processor': 'technology',
    'data': 'technology', 'cyber': 'technology', 'code': 'technology',

    // Finance keywords
    'stock': 'finance', 'market': 'finance', 'invest': 'finance', 'trading': 'finance',
    'financial': 'finance', 'bank': 'finance', 'money': 'finance', 'fund': 'finance',
    'asset': 'finance', 'wealth': 'finance', 'dividend': 'finance', 'profit': 'finance',
    'revenue': 'finance', 'earning': 'finance', 'fiscal': 'finance', 'credit': 'finance',
    'loan': 'finance', 'debt': 'finance', 'payment': 'finance',

    // Automotive keywords
    'car': 'automotive', 'vehicle': 'automotive', 'auto': 'automotive', 'drive': 'automotive',
    'electric vehicle': 'automotive', 'ev': 'automotive', 'tesla': 'automotive',
    'ford': 'automotive', 'gm': 'automotive', 'toyota': 'automotive', 'battery': 'automotive',
    'charging': 'automotive', 'autonomous': 'automotive', 'self-driving': 'automotive',

    // Retail keywords
    'store': 'retail', 'shop': 'retail', 'retail': 'retail', 'consumer': 'retail',
    'product': 'retail', 'brand': 'retail', 'customer': 'retail', 'shopping': 'retail',
    'ecommerce': 'retail', 'online shopping': 'retail', 'amazon': 'ecommerce',
    'walmart': 'retail', 'target': 'retail', 'costco': 'retail',

    // Healthcare keywords
    'health': 'healthcare', 'medical': 'healthcare', 'drug': 'healthcare',
    'pharma': 'healthcare', 'vaccine': 'healthcare', 'treatment': 'healthcare',
    'patient': 'healthcare', 'hospital': 'healthcare', 'doctor': 'healthcare',
    'clinical': 'healthcare', 'therapy': 'healthcare', 'biotech': 'healthcare',

    // Energy keywords
    'energy': 'energy', 'oil': 'energy', 'gas': 'energy', 'renewable': 'energy',
    'solar': 'energy', 'wind': 'energy', 'power': 'energy', 'electricity': 'energy',
    'fuel': 'energy', 'carbon': 'energy', 'climate': 'energy', 'green': 'energy',

    // Entertainment keywords
    'streaming': 'entertainment', 'movie': 'entertainment', 'show': 'entertainment',
    'film': 'entertainment', 'entertainment': 'entertainment', 'media': 'entertainment',
    'content': 'entertainment', 'disney': 'entertainment', 'netflix': 'entertainment',
    'subscriber': 'entertainment', 'audience': 'entertainment', 'viewer': 'entertainment',

    // Real estate keywords
    'real estate': 'realestate', 'property': 'realestate', 'housing': 'realestate',
    'home': 'realestate', 'mortgage': 'realestate', 'rent': 'realestate',
    'commercial': 'realestate', 'residential': 'realestate', 'building': 'realestate',
    'construction': 'realestate', 'development': 'realestate',

    // Food keywords
    'food': 'food', 'restaurant': 'food', 'dining': 'food', 'menu': 'food',
    'starbucks': 'food', 'mcdonald': 'food', 'coffee': 'food', 'beverage': 'food',
    'drink': 'food', 'grocery': 'food', 'meal': 'food',

    // Aerospace keywords
    'aerospace': 'aerospace', 'aircraft': 'aerospace', 'plane': 'aerospace',
    'flight': 'aerospace', 'boeing': 'aerospace', 'aviation': 'aerospace',
    'airline': 'aerospace', 'space': 'aerospace', 'rocket': 'aerospace',
    'satellite': 'aerospace', 'defense': 'aerospace', 'military': 'aerospace',

    // Telecom keywords
    'telecom': 'telecom', 'communication': 'telecom', 'wireless': 'telecom',
    'mobile': 'telecom', 'phone': 'telecom', '5g': 'telecom', 'network': 'telecom',
    'broadband': 'telecom', 'internet_provider': 'telecom', 'verizon': 'telecom', 'at&t': 'telecom',

    // Industrial keywords
    'industrial': 'industrial', 'manufacturing': 'industrial', 'factory': 'industrial',
    'production': 'industrial', 'supply chain': 'industrial', 'machinery': 'industrial',
    'equipment': 'industrial', 'automation': 'industrial', 'robotics': 'industrial',

    // Materials keywords
    'material': 'materials', 'chemical': 'materials', 'mining': 'materials',
    'metal': 'materials', 'steel': 'materials', 'commodity': 'materials',
    'raw material': 'materials', 'construction material': 'materials',

    // Utilities keywords
    'utility': 'utilities', 'water': 'utilities', 'power_company': 'utilities',
    'gas utility': 'utilities', 'power grid': 'utilities', 'infrastructure': 'utilities',

    // Economic keywords
    'economy': 'finance', 'inflation': 'finance', 'recession': 'finance',
    'gdp': 'finance', 'economic': 'finance', 'fed': 'finance',
    'federal reserve': 'finance', 'interest rate': 'finance', 'policy': 'finance',
    'growth': 'finance', 'forecast': 'finance', 'outlook': 'finance',

    // Commodities keywords
    'gold': 'commodities', 'silver': 'commodities', 'commodity_market': 'commodities',
    'precious_metal': 'commodities', 'oil_price': 'commodities', 'barrel': 'commodities',
    'resource': 'commodities', 'commodity_trading': 'commodities',

    // Index keywords
    'index': 'index', 's&p': 'index', 'dow': 'index', 'nasdaq': 'index',
    'etf': 'index', 'mutual_fund': 'index', 'portfolio_management': 'index', 'diversification': 'index',
    'allocation': 'index', 'benchmark': 'index', 'performance': 'index',

    // Bonds keywords
    'bond': 'bonds', 'treasury': 'bonds', 'yield': 'bonds', 'fixed income': 'bonds',
    'interest': 'bonds', 'maturity': 'bonds', 'coupon': 'bonds', 'debt security': 'bonds',
  };

  // Check if any keywords match the article content
  let matchedCategory = '';
  for (const [keyword, category] of Object.entries(keywordToCategory)) {
    if (content.includes(keyword)) {
      matchedCategory = category;
      break;
    }
  }

  // If we found a matching category based on content, use it
  // Otherwise, fall back to the ticker's industry
  const industry = matchedCategory || tickerToIndustry[ticker] || 'default';

  // Get the image array for this industry
  const images = industryImages[industry] || industryImages['default'];

  // Create a more unique hash based on multiple article properties
  // This ensures greater variety while maintaining consistency for the same article
  const titleHash = (article.id || title).split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);
  const descHash = description.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);
  const tickerHash = ticker.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);
  const dateHash = (article.published_utc || '').split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);

  // Combine hashes in a way that creates more variety
  const combinedHash = (titleHash * 13) + (descHash * 7) + (tickerHash * 5) + (dateHash * 3);

  // Add the attempt number to create different results on each attempt
  // This helps find unique images when the first choice is already used
  const attemptFactor = attemptNumber * 31; // Use a prime number

  // Add a secondary selection factor based on article content
  const secondaryFactor = content.length % 17; // Use a prime number for better distribution

  // Calculate final index with more entropy
  const imageIndex = (combinedHash + secondaryFactor + attemptFactor) % images.length;

  // Get the selected image
  const selectedImage = images[imageIndex];

  // Add query parameters with unique identifiers to prevent image caching and ensure uniqueness
  // Include attempt number, timestamp, and article ID to guarantee uniqueness
  const timestamp = Date.now();
  const uniqueParam = `&article_id=${article.id || combinedHash}&t=${timestamp}&attempt=${attemptNumber}`;

  return selectedImage + uniqueParam;
}

// Helper function to get company logo as fallback (not used anymore, kept for reference)
function __getCompanyLogo(ticker: string): string {
  // Common tickers to domain mappings
  const tickerToDomain: Record<string, string> = {
    'AAPL': 'apple.com',
    'MSFT': 'microsoft.com',
    'GOOGL': 'google.com',
    'GOOG': 'google.com',
    'AMZN': 'amazon.com',
    'META': 'meta.com',
    'FB': 'facebook.com',
    'TSLA': 'tesla.com',
    'NFLX': 'netflix.com',
    'NVDA': 'nvidia.com',
    'JPM': 'jpmorganchase.com',
    'V': 'visa.com',
    'MA': 'mastercard.com',
    'DIS': 'disney.com',
    'PYPL': 'paypal.com',
    'ADBE': 'adobe.com',
    'INTC': 'intel.com',
    'CSCO': 'cisco.com',
    'CRM': 'salesforce.com',
    'AMD': 'amd.com',
    'QCOM': 'qualcomm.com',
    'SBUX': 'starbucks.com',
    'NKE': 'nike.com',
    'PEP': 'pepsico.com',
    'KO': 'coca-cola.com',
    'WMT': 'walmart.com',
    'TGT': 'target.com',
    'COST': 'costco.com',
    'HD': 'homedepot.com',
    'LOW': 'lowes.com',
    'MCD': 'mcdonalds.com',
    'BA': 'boeing.com',
    'GE': 'ge.com',
    'IBM': 'ibm.com',
    'ORCL': 'oracle.com',
    'T': 'att.com',
    'VZ': 'verizon.com',
    'PFE': 'pfizer.com',
    'JNJ': 'jnj.com',
    'MRK': 'merck.com',
    'CVS': 'cvs.com',
    'UNH': 'unitedhealthgroup.com',
    'BAC': 'bankofamerica.com',
    'C': 'citigroup.com',
    'WFC': 'wellsfargo.com',
    'GS': 'goldmansachs.com',
    'MS': 'morganstanley.com',
    'BLK': 'blackrock.com',
    'SCHW': 'schwab.com',
    'SPGI': 'spglobal.com',
    'BRK.A': 'berkshirehathaway.com',
    'BRK.B': 'berkshirehathaway.com',
  };

  // If we have a mapping, use it
  if (tickerToDomain[ticker]) {
    return `https://logo.clearbit.com/${tickerToDomain[ticker]}`;
  }

  // Otherwise, make a guess based on the ticker
  // Convert ticker to lowercase and add .com
  return `https://logo.clearbit.com/${ticker.toLowerCase()}.com`;
}

async function getPortfolioNews(supabaseClient: any, userId: string, portfolioId: string, customStockTickers?: string[]) {
  try {
    console.log(`getPortfolioNews called with portfolioId: ${portfolioId}, userId: ${userId}`);
    console.log(`customStockTickers: ${customStockTickers ? JSON.stringify(customStockTickers) : 'none'}`);

    // Check if this is a special case for a generated portfolio that hasn't been saved yet
    let portfolioData: any = null;

    if (portfolioId === 'generated-portfolio') {
      console.log('Using generated portfolio mode - bypassing database lookup');

      // Create a mock portfolio object
      portfolioData = {
        id: 'generated-portfolio',
        user_id: userId,
        name: 'Generated Portfolio',
        description: 'This portfolio has not been saved yet',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Make sure we have stock tickers
      if (!customStockTickers || !Array.isArray(customStockTickers) || customStockTickers.length === 0) {
        console.warn('No stock tickers provided for generated portfolio, using defaults');
        customStockTickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META'];
      }
    } else {
      // Normal case - get the portfolio from the database
      console.log('Fetching portfolio data from database...');
      const { data: dbPortfolioData, error: portfolioError } = await supabaseClient
        .from('portfolios')
        .select('*')
        .eq('id', portfolioId)
        .eq('user_id', userId)
        .single();

      if (portfolioError) {
        console.error(`Error getting portfolio: ${portfolioError.message}`);
        throw new Error(`Error getting portfolio: ${portfolioError.message}`);
      }

      portfolioData = dbPortfolioData;
      console.log('Portfolio data retrieved successfully:', portfolioData);
    }

    // Get the portfolio stocks
    let stocksData: { ticker: string }[];

    // If this is a generated portfolio or custom stock tickers are provided, use those
    if (portfolioId === 'generated-portfolio' || (customStockTickers && Array.isArray(customStockTickers) && customStockTickers.length > 0)) {
      // For generated portfolios, we should always have customStockTickers
      const tickersToUse = customStockTickers || ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META'];

      // Convert the array of ticker strings to the expected format
      stocksData = tickersToUse.map(ticker => ({ ticker }));
      console.log(`Using ${stocksData.length} custom stock tickers:`, JSON.stringify(stocksData));
    } else {
      // Otherwise fetch stocks from the database for saved portfolios
      console.log(`Fetching stocks for portfolio ID: ${portfolioId}`);
      try {
        const { data, error: stocksError } = await supabaseClient
          .from('portfolio_stocks')
          .select('*')
          .eq('portfolio_id', portfolioId);

        if (stocksError) {
          console.error(`Error getting portfolio stocks: ${stocksError.message}`);
          throw new Error(`Error getting portfolio stocks: ${stocksError.message}`);
        }

        if (!data || data.length === 0) {
          console.warn(`No stocks found for portfolio ID: ${portfolioId}`);
          // Create mock data with some common tickers to avoid empty results
          stocksData = [
            { ticker: 'AAPL' },
            { ticker: 'MSFT' },
            { ticker: 'GOOGL' },
            { ticker: 'AMZN' },
            { ticker: 'META' }
          ];
          console.log('Using default stock tickers as fallback');
        } else {
          console.log(`Found ${data.length} stocks for portfolio ID: ${portfolioId}`);
          stocksData = data.map(stock => ({ ticker: stock.ticker }));
        }
      } catch (error) {
        console.error('Error in portfolio stocks query:', error);
        // Create mock data with some common tickers to avoid empty results
        stocksData = [
          { ticker: 'AAPL' },
          { ticker: 'MSFT' },
          { ticker: 'GOOGL' },
          { ticker: 'AMZN' },
          { ticker: 'META' }
        ];
        console.log('Using default stock tickers due to error');
      }
    }

    // Verify we have valid stock data
    if (!stocksData || !Array.isArray(stocksData) || stocksData.length === 0) {
      console.warn('No valid stock data found, using default tickers');
      stocksData = [
        { ticker: 'AAPL' },
        { ticker: 'MSFT' },
        { ticker: 'GOOGL' },
        { ticker: 'AMZN' },
        { ticker: 'META' }
      ];
    }

    // Get the API key for FinancialDatasets
    const apiKey = Deno.env.get('FINANCIALDATASETS_API_KEY');
    if (!apiKey) {
      console.warn('FinancialDatasets API key not configured, will use fallback news source');
    }

    // Fetch news for each stock in the portfolio
    console.log(`Fetching news for ${stocksData.length} stocks`);
    const newsPromises = stocksData.map(async (stock: { ticker: string }) => {
      try {
        console.log(`Processing news for ticker: ${stock.ticker}`);
        // Calculate date range for news (last 30 days)
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);

        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];

        // Try to fetch from FinancialDatasets API first if we have an API key
        if (apiKey) {
          console.log(`Attempting to fetch news for ${stock.ticker} from FinancialDatasets API`);
          try {
            const url = `https://api.financialdatasets.ai/news/?ticker=${stock.ticker}&start_date=${formattedStartDate}&end_date=${formattedEndDate}&limit=10`;

            const response = await fetch(url, {
              headers: {
                'X-API-KEY': apiKey
              }
            });

            if (response.ok) {
              const data = await response.json();
              if (data.news && data.news.length > 0) {
                console.log(`Successfully fetched ${data.news.length} news items for ${stock.ticker} from FinancialDatasets API`);
                return {
                  ticker: stock.ticker,
                  news: data.news
                };
              } else {
                console.warn(`No news found for ${stock.ticker} from FinancialDatasets API, will try fallback`);
              }
            } else {
              console.warn(`Failed to fetch news for ${stock.ticker} from FinancialDatasets API: ${response.statusText}, will try fallback`);
            }
          } catch (error) {
            console.error(`Error fetching news for ${stock.ticker} from FinancialDatasets API:`, error);
          }
        }

        // Fallback to mock news data
        console.log(`Generating mock news for ${stock.ticker}`);

        // Generate mock news data for now
        // In a real implementation, you would call another news API here
        const mockNews = generateMockNewsForTicker(stock.ticker, 5);
        console.log(`Generated ${mockNews.length} mock news items for ${stock.ticker}`);

        return {
          ticker: stock.ticker,
          news: mockNews
        };
      } catch (error) {
        console.error(`Error fetching news for ${stock.ticker}:`, error);
        // Even if there's an error, return some mock news to avoid empty results
        const fallbackNews = generateMockNewsForTicker(stock.ticker, 3);
        console.log(`Generated ${fallbackNews.length} fallback news items for ${stock.ticker} after error`);
        return {
          ticker: stock.ticker,
          news: fallbackNews
        };
      }
    });

    // Helper function to generate mock news when API fails
    function generateMockNewsForTicker(ticker: string, count: number): any[] {
      const mockNews: any[] = [];
      const today = new Date();

      const headlines = [
        `${ticker} Reports Strong Quarterly Earnings`,
        `Analysts Upgrade ${ticker} Stock Rating`,
        `${ticker} Announces New Product Launch`,
        `${ticker} Expands into New Markets`,
        `Investors Optimistic About ${ticker}'s Future`,
        `${ticker} Stock Rises on Positive Market Sentiment`,
        `${ticker} Partners with Industry Leaders`,
        `${ticker} CEO Discusses Company Strategy`,
        `${ticker} Addresses Industry Challenges`,
        `Market Trends Favor ${ticker}'s Business Model`
      ];

      for (let i = 0; i < count; i++) {
        const daysAgo = Math.floor(Math.random() * 30);
        const publishDate = new Date(today);
        publishDate.setDate(publishDate.getDate() - daysAgo);

        mockNews.push({
          id: `mock-${ticker}-${i}-${Date.now()}`,
          title: headlines[i % headlines.length],
          description: `Latest news and analysis about ${ticker} and its performance in the current market environment.`,
          published_utc: publishDate.toISOString(),
          article_url: `https://example.com/news/${ticker.toLowerCase()}/${i}`,
          source: 'Market News',
          ticker: ticker,
          url: `https://example.com/news/${ticker.toLowerCase()}/${i}` // Add url field for compatibility
        });
      }

      return mockNews;
    }

    console.log('Waiting for all news promises to resolve...');
    const newsResults = await Promise.all(newsPromises);
    console.log(`Received news results for ${newsResults.length} tickers`);

    // Log the results for each ticker
    newsResults.forEach(result => {
      console.log(`Ticker ${result.ticker}: ${result.news.length} news items`);
    });

    // Track used images to ensure no duplicates
    const usedImages = new Set<string>();

    // Combine all news articles
    console.log('Combining all news articles...');
    let allArticles = newsResults.flatMap(result =>
      result.news.map((article: any) => ({
        ...article,
        ticker: result.ticker
      }))
    );
    console.log(`Combined ${allArticles.length} total news articles`);

    // First, filter to keep only articles from the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const beforeFilterCount = allArticles.length;
    allArticles = allArticles.filter(article => {
      const publishDate = new Date(article.published_utc || 0);
      return publishDate >= thirtyDaysAgo;
    });
    console.log(`Filtered articles by date: ${beforeFilterCount} -> ${allArticles.length}`);

    if (allArticles.length === 0) {
      console.warn('No articles left after date filtering, using all articles');
      // If no articles left after filtering, use all of them
      allArticles = newsResults.flatMap(result =>
        result.news.map((article: any) => ({
          ...article,
          ticker: result.ticker
        }))
      );
      console.log(`Using all ${allArticles.length} articles without date filtering`);
    }

    // Mix up the articles to avoid grouping by ticker
    // We'll use a combination of date-based grouping and randomization

    // 1. Group articles by day (to maintain some recency preference)
    const articlesByDay: Record<string, any[]> = {};

    allArticles.forEach(article => {
      const publishDate = new Date(article.published_utc || 0);
      const dateKey = publishDate.toISOString().split('T')[0]; // YYYY-MM-DD

      if (!articlesByDay[dateKey]) {
        articlesByDay[dateKey] = [];
      }

      articlesByDay[dateKey].push(article);
    });

    // 2. For each day, shuffle the articles to mix up tickers
    Object.keys(articlesByDay).forEach(dateKey => {
      // Fisher-Yates shuffle algorithm
      const articles = articlesByDay[dateKey];
      for (let i = articles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [articles[i], articles[j]] = [articles[j], articles[i]];
      }
    });

    // 3. Combine all articles back, starting with most recent days
    allArticles = Object.keys(articlesByDay)
      .sort((a, b) => b.localeCompare(a)) // Sort dates in descending order
      .flatMap(dateKey => articlesByDay[dateKey]);

    console.log('Processing articles to add images...');
    // Process articles to ensure each has a unique image
    const allNews = await Promise.all(allArticles.map(async (article: any) => {
      try {
        // If article already has an image_url from the API, keep track of it
        if (article.image_url) {
          usedImages.add(article.image_url);
        } else {
          // Get a unique image from Google Images based on the article title
          article.image_url = await getGoogleImage(article, article.ticker, usedImages);
        }
        return article;
      } catch (error) {
        console.error(`Error adding image to article for ${article.ticker}:`, error);
        // Ensure the article has at least a fallback image
        if (!article.image_url) {
          // Create a fallback image URL based on ticker
          article.image_url = `https://source.unsplash.com/featured/800x600/?stock,${article.ticker}&t=${Date.now()}`;
        }
        return article;
      }
    }));

    // Log the number of unique images
    console.log(`Generated ${usedImages.size} unique images for ${allNews.length} articles`);

    // Log a sample of the news data to verify structure
    if (allNews.length > 0) {
      console.log('Sample news article:', JSON.stringify(allNews[0]));
    } else {
      console.log('No news articles found');

      // If we still have no news, create some mock news as a last resort
      if (allNews.length === 0 && stocksData && stocksData.length > 0) {
        console.log('Creating emergency mock news as last resort');
        const emergencyNews: any[] = [];
        for (const stock of stocksData) {
          const mockArticles = generateMockNewsForTicker(stock.ticker, 3);
          for (const article of mockArticles) {
            article.ticker = stock.ticker;
            article.image_url = `https://source.unsplash.com/featured/800x600/?stock,${stock.ticker}&t=${Date.now()}&id=${Math.random()}`;
            emergencyNews.push(article);
          }
        }
        console.log(`Created ${emergencyNews.length} emergency mock news articles`);
        allNews.push(...emergencyNews);
      }
    }

    const responseData = {
      portfolio: portfolioData,
      news: allNews
    };

    console.log(`Returning response with ${allNews.length} news articles`);

    return new Response(
      JSON.stringify(responseData),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error getting portfolio news:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}
