# TradingView Poster Edge Function

This Supabase Edge Function automatically selects a stock from a list of 20 most active stocks based on the time of day, analyzes it using Aura's technical analysis, generates a sentiment message with <PERSON>, and posts it to TradingView.

## How It Works

1. **Stock Selection**: The function selects one of 20 most active stocks based on the current time, rotating through the stocks every 21 minutes during market hours (9am-4pm) without duplicates. It uses a combination of time-based factors to ensure variety even when called at irregular intervals.

2. **Technical Analysis**: The selected stock is analyzed using Aura's public test endpoint. The function compares bullish and bearish scores to determine a definitive signal (always either bullish or bearish, never neutral).

3. **Message Generation**: Based on Aura's analysis, the function uses <PERSON> to generate a natural-sounding message:
   - BULLISH: A message about buying calls or going long
   - BEARISH: A message about shorting or buying puts

   Note: The function never generates neutral messages. It compares bullish and bearish scores and always chooses one of these two signals.

4. **TradingView Posting**: The generated message is posted to TradingView's API for the specific stock.

## Environment Variables

The function requires the following environment variables:

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
- `SUPABASE_ANON_KEY`: Your Supabase anon/public key (needed for Aura API authentication)
- `GEMINI_API_KEY`: Your Google Gemini API key

Note: The TradingView cookie is hard-coded in the function for authentication.

## Deployment

To deploy this function, run:

```bash
supabase functions deploy tradingview-poster
```

Or use the deployment script:

```bash
node scripts/deploy-tradingview-poster.js
```

## Scheduling

This function can be triggered manually by making a POST request to the function URL, or it can be scheduled to run automatically using a cron job or a service like GitHub Actions.

## Manual Stock Selection

You can manually select a specific stock by adding a `stockIndex` query parameter to the URL:

```
https://your-supabase-url/functions/v1/tradingview-poster?stockIndex=5
```

This will use the stock at index 5 in the list (in this case, Amazon) regardless of the current time.

## Example Output

```json
{
  "success": true,
  "stock": {
    "symbol": "AAPL",
    "name": "Apple",
    "exchange": "NASDAQ"
  },
  "message": "I'm buying calls on Apple ($AAPL) because Osis.co's analysis is showing bullish signals today.",
  "tradingViewResult": {
    "id": "12345678",
    "status": "success"
  }
}
```

## Special Cases

The function handles special cases for certain symbols:

- For S&P 500 analysis, it analyzes VOO but posts to TradingView as SPX
- For Nasdaq 100 analysis, it analyzes QQQ but posts to TradingView as NDX
- Different exchanges (NASDAQ, NYSE, CRYPTO, INDEX) are properly formatted for TradingView
