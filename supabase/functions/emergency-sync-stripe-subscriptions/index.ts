// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Constants for plans and limits
const PLAN_TYPES = {
  BASIC: 'basic',  // The basic weekly plan - $5.99/week
  PRO: 'pro',      // The pro weekly plan - $9.99/week
};

// Price ID to plan mapping
const PRICE_ID_TO_PLAN = {
  // Basic Plan - $5.99/week
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.BASIC,
  // Pro Plan - $9.99/week
  'price_1ROYKjDebmd1GpTv5oYNMKMv': PLAN_TYPES.PRO,
};

// Initialize Stripe
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Helper function to safely extract customer ID from subscription object
const getCustomerIdFromSubscription = (subscription: any): string | null => {
  try {
    // Check if customer is an object with an id property
    if (subscription.customer && typeof subscription.customer === 'object' && subscription.customer.id) {
      return subscription.customer.id;
    }

    // Check if customer is a string
    if (subscription.customer && typeof subscription.customer === 'string') {
      return subscription.customer;
    }

    // Log the actual customer value for debugging
    console.log(`Unexpected customer format for subscription ${subscription.id}:`,
      JSON.stringify(subscription.customer));

    return null;
  } catch (error) {
    console.error(`Error extracting customer ID from subscription ${subscription.id}:`, error);
    return null;
  }
};

// Function to get user ID from customer ID
const getUserIdFromCustomer = async (customerId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('user_id')
      .eq('stripe_customer_id', customerId)
      .single();

    if (error) {
      console.error(`Error getting user ID for customer ${customerId}:`, error);
      return null;
    }

    return data?.user_id || null;
  } catch (error) {
    console.error(`Error in getUserIdFromCustomer for ${customerId}:`, error);
    return null;
  }
};

// Function to update user tokens based on subscription type
const updateUserTokens = async (userId: string, planType: string, isTrialing: boolean) => {
  try {
    // Determine token limit based on plan type and trial status
    let tokenLimit = 0;
    if (isTrialing) {
      // Trial users get 3 messages per day
      tokenLimit = 3;
    } else if (planType === PLAN_TYPES.BASIC) {
      // Basic plan gets 100 messages per day
      tokenLimit = 100;
    } else if (planType === PLAN_TYPES.PRO) {
      // Pro plan gets 200 messages per day
      tokenLimit = 200;
    }

    // Check if user already has a token record
    const { data: existingTokens, error: checkError } = await supabase
      .from('user_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw new Error(`Failed to check existing tokens: ${checkError.message}`);
    }

    const now = new Date();
    const tokenData = {
      user_id: userId,
      tokens_remaining: tokenLimit,
      last_reset: now.toISOString(),
      last_reset_date: now.toISOString(),
      updated_at: now.toISOString()
    };

    // Update or insert token record
    const { error: tokenError } = await supabase
      .from('user_tokens')
      .upsert(tokenData, { onConflict: 'user_id' });

    if (tokenError) {
      throw new Error(`Failed to update user tokens: ${tokenError.message}`);
    }
  } catch (error) {
    throw error;
  }
};

// Function to update user's subscription in Supabase
const updateSubscription = async (userId: string, subscription: any) => {
  try {
    // Get price ID and determine plan type
    const priceId = subscription.items.data[0].price.id;
    const planType = PRICE_ID_TO_PLAN[priceId];

    if (!planType) {
      console.warn(`Unknown price ID: ${priceId}, defaulting to BASIC plan`);
    }

    // Extract customer ID safely
    const customerId = getCustomerIdFromSubscription(subscription);
    if (!customerId) {
      throw new Error(`Failed to extract customer ID from subscription ${subscription.id}`);
    }

    // Convert Unix timestamps to ISO strings
    const currentPeriodEnd = unixTimestampToISOString(subscription.current_period_end);
    const cancelAt = unixTimestampToISOString(subscription.cancel_at);

    // Prepare subscription data
    const subscriptionData = {
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: customerId,
      stripe_price_id: priceId,
      status: subscription.status,
      current_period_end: currentPeriodEnd,
      cancel_at_period_end: subscription.cancel_at_period_end,
      cancel_at: cancelAt,
      updated_at: new Date().toISOString()
    };

    // Update or insert subscription record
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'user_id' });

    if (subscriptionError) {
      throw new Error(`Failed to update subscription record: ${subscriptionError.message}`);
    }

    const validStatuses = ['active', 'trialing', 'incomplete', 'past_due'];

    if (validStatuses.includes(subscription.status)) {
      // Update profile with new subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          subscription_type: planType || PLAN_TYPES.BASIC,
          has_seen_onboarding: true,
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Update user tokens
      try {
        const isTrialing = subscription.status === 'trialing';
        await updateUserTokens(userId, planType || PLAN_TYPES.BASIC, isTrialing);
      } catch (tokenError) {
        throw new Error(`Failed to update user tokens: ${tokenError.message}`);
      }
    } else if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
      // For canceled or unpaid subscriptions, remove subscription type from profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          subscription_type: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (profileError) {
        throw new Error(`Failed to update profile for canceled subscription: ${profileError.message}`);
      }

      // Set tokens to 0
      try {
        await supabase
          .from('user_tokens')
          .update({
            tokens_remaining: 0,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);
      } catch (tokenError) {
        throw new Error(`Failed to reset tokens for canceled subscription: ${tokenError.message}`);
      }
    }

    return {
      user_id: userId,
      subscription_id: subscription.id,
      status: subscription.status,
      plan_type: planType || PLAN_TYPES.BASIC
    };
  } catch (error) {
    throw error;
  }
};



// Helper function to convert Unix timestamp to ISO date string
const unixTimestampToISOString = (timestamp: number | null): string | null => {
  if (!timestamp) return null;
  return new Date(timestamp * 1000).toISOString();
};

// Function to save subscription to backup table
const saveSubscriptionToBackup = async (subscription: any): Promise<boolean> => {
  try {
    console.log(`Starting to save subscription ${subscription.id} to backup table`);

    // Extract customer ID safely
    const customerId = getCustomerIdFromSubscription(subscription);
    if (!customerId) {
      console.error(`Failed to extract customer ID from subscription ${subscription.id}`);
      return false;
    }

    // Get price ID
    let priceId = null;
    try {
      priceId = subscription.items.data[0].price.id;
      console.log(`Found price ID: ${priceId} for subscription ${subscription.id}`);
    } catch (error) {
      console.error(`Error extracting price ID from subscription ${subscription.id}:`, error);
      priceId = 'unknown';
    }

    // Convert Unix timestamps to ISO strings
    let currentPeriodEnd = null;
    let cancelAt = null;
    try {
      currentPeriodEnd = unixTimestampToISOString(subscription.current_period_end);
      cancelAt = unixTimestampToISOString(subscription.cancel_at);
      console.log(`Converted timestamps for subscription ${subscription.id}: current_period_end=${currentPeriodEnd}, cancel_at=${cancelAt}`);
    } catch (error) {
      console.error(`Error converting timestamps for subscription ${subscription.id}:`, error);
    }

    // Prepare simplified subscription data for backup table
    const backupData = {
      stripe_subscription_id: subscription.id,
      stripe_customer_id: customerId,
      stripe_price_id: priceId,
      customer_email: null, // Simplified to avoid potential issues
      customer_name: null,  // Simplified to avoid potential issues
      customer_metadata: null, // Simplified to avoid potential issues
      status: subscription.status || 'unknown',
      current_period_end: currentPeriodEnd,
      cancel_at_period_end: !!subscription.cancel_at_period_end, // Convert to boolean
      cancel_at: cancelAt,
      updated_at: new Date().toISOString(),
      // Store a simplified version of the subscription data
      subscription_data: {
        id: subscription.id,
        customer: customerId,
        status: subscription.status,
        current_period_end: subscription.current_period_end,
        cancel_at_period_end: subscription.cancel_at_period_end,
        cancel_at: subscription.cancel_at
      }
    };

    console.log(`Prepared backup data for subscription ${subscription.id}`);

    // Insert into backup table
    console.log(`Attempting to insert subscription ${subscription.id} into backup table`);
    const { error } = await supabase
      .from('stripe_subscriptions_backup')
      .upsert(backupData, { onConflict: 'stripe_subscription_id' });

    if (error) {
      console.error(`Failed to save subscription ${subscription.id} to backup table:`, error);
      return false;
    }

    console.log(`Successfully saved subscription ${subscription.id} to backup table`);
    return true;
  } catch (error) {
    console.error(`Error in saveSubscriptionToBackup for ${subscription.id}:`, error);
    return false;
  }
};

// Fallback function to save minimal subscription data
const saveMinimalSubscriptionData = async (subscription: any): Promise<boolean> => {
  try {
    console.log(`Attempting minimal save for subscription ${subscription.id}`);

    // Extract only the ID and save minimal data
    const minimalData = {
      stripe_subscription_id: subscription.id,
      status: 'unknown',
      updated_at: new Date().toISOString(),
      subscription_data: { id: subscription.id }
    };

    const { error } = await supabase
      .from('stripe_subscriptions_backup')
      .upsert(minimalData, { onConflict: 'stripe_subscription_id' });

    if (error) {
      console.error(`Failed minimal save for subscription ${subscription.id}:`, error);
      return false;
    }

    console.log(`Minimal save successful for subscription ${subscription.id}`);
    return true;
  } catch (error) {
    console.error(`Error in minimal save for subscription ${subscription.id}:`, error);
    return false;
  }
};

// Function to fetch all subscriptions from Stripe and sync them to Supabase
const syncAllStripeSubscriptions = async () => {
  try {
    const results = {
      total: 0,
      success: 0,
      failed: 0,
      details: [] as any[]
    };

    let hasMore = true;
    let startingAfter: string | undefined = undefined;

    // Fetch all subscriptions from Stripe using pagination
    while (hasMore) {
      const params: Stripe.SubscriptionListParams = {
        limit: 100,
        expand: ['data.customer', 'data.items.data.price']
      };

      if (startingAfter) {
        params.starting_after = startingAfter;
      }

      console.log(`Fetching subscriptions from Stripe${startingAfter ? ` starting after ${startingAfter}` : ''}`);
      const subscriptions = await stripe.subscriptions.list(params);

      results.total += subscriptions.data.length;
      console.log(`Retrieved ${subscriptions.data.length} subscriptions from Stripe`);

      if (subscriptions.data.length === 0) {
        hasMore = false;
        break;
      }

      // Process each subscription
      for (const subscription of subscriptions.data) {
        try {
          console.log(`Processing subscription ${subscription.id}`);

          // Try to save to backup table first
          let backupSuccess = await saveSubscriptionToBackup(subscription);

          // If primary method fails, try minimal fallback method
          if (!backupSuccess) {
            console.log(`Primary save method failed for ${subscription.id}, trying minimal fallback`);
            backupSuccess = await saveMinimalSubscriptionData(subscription);

            if (!backupSuccess) {
              console.error(`All save methods failed for subscription ${subscription.id}`);
              results.failed++;
              results.details.push({
                subscription_id: subscription.id,
                status: 'error',
                message: 'Failed to save to backup table (all methods failed)'
              });
              continue;
            } else {
              console.log(`Minimal fallback save succeeded for subscription ${subscription.id}`);
            }
          }

          // Extract customer ID safely
          const customerId = getCustomerIdFromSubscription(subscription);

          if (!customerId) {
            results.failed++;
            results.details.push({
              subscription_id: subscription.id,
              status: 'error',
              message: 'Failed to extract customer ID from subscription'
            });
            continue;
          }

          // Try to find a user ID for this customer
          const userId = await getUserIdFromCustomer(customerId);

          // If we found a user ID, try to update the regular subscriptions table
          if (userId) {
            try {
              // Try to update the regular subscriptions table
              await updateSubscription(userId, subscription);
              results.success++;
              results.details.push({
                user_id: userId,
                subscription_id: subscription.id,
                status: 'success',
                note: 'Updated both backup and regular subscriptions tables'
              });
            } catch (error) {
              // If it fails, we still count it as a success since we saved to backup
              console.warn(`Could not update regular subscriptions table for ${subscription.id}:`, error.message);
              results.success++;
              results.details.push({
                subscription_id: subscription.id,
                customer_id: customerId,
                status: 'partial_success',
                note: 'Saved to backup table only',
                error: error.message
              });
            }
          } else {
            // No user ID found, but we saved to backup so count as success
            results.success++;
            results.details.push({
              subscription_id: subscription.id,
              customer_id: customerId,
              status: 'partial_success',
              note: 'Saved to backup table only, no user ID found'
            });
          }
        } catch (error) {
          console.error(`Error processing subscription ${subscription.id}:`, error);
          results.failed++;
          results.details.push({
            subscription_id: subscription.id,
            status: 'error',
            message: error.message
          });
        }
      }

      // Set up for next page
      if (subscriptions.has_more && subscriptions.data.length > 0) {
        startingAfter = subscriptions.data[subscriptions.data.length - 1].id;
        console.log(`More subscriptions available, continuing with next page starting after ${startingAfter}`);
      } else {
        hasMore = false;
        console.log('No more subscriptions to process');
      }
    }

    return results;
  } catch (error) {
    console.error('Error in syncAllStripeSubscriptions:', error);
    throw error;
  }
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('Starting emergency sync of all Stripe subscriptions to Supabase');

    // Sync all subscriptions from Stripe to Supabase
    const results = await syncAllStripeSubscriptions();

    console.log(`Sync completed. Processed ${results.total} subscriptions. Success: ${results.success}, Failed: ${results.failed}`);

    return new Response(JSON.stringify({
      message: 'Emergency sync completed',
      results
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error in emergency sync:', error);

    return new Response(JSON.stringify({
      error: error.message,
      stack: error.stack
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
