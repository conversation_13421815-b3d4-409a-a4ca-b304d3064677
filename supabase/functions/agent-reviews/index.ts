// Agent Reviews Edge Function
// This function handles agent reviews and ratings

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

interface CreateReviewRequest {
  publishedAgentId: string;
  rating: number;
  reviewText?: string;
}

interface UpdateReviewRequest {
  reviewId: string;
  rating: number;
  reviewText?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const url = new URL(req.url);
    const publishedAgentId = url.searchParams.get('publishedAgentId');

    // Handle GET requests - fetch reviews for an agent
    if (req.method === 'GET') {
      if (!publishedAgentId) {
        throw new Error('publishedAgentId parameter is required for GET requests');
      }

      const { data: reviews, error: reviewsError } = await supabase
        .from('agent_reviews')
        .select(`
          *,
          profiles:reviewer_id (
            full_name
          )
        `)
        .eq('published_agent_id', publishedAgentId)
        .order('created_at', { ascending: false });

      if (reviewsError) {
        throw new Error(`Failed to fetch reviews: ${reviewsError.message}`);
      }

      const formattedReviews = reviews?.map(review => ({
        id: review.id,
        rating: review.rating,
        reviewText: review.review_text,
        reviewerName: review.profiles?.full_name || 'Anonymous',
        reviewerId: review.reviewer_id,
        isVerified: review.is_verified,
        createdAt: review.created_at,
        updatedAt: review.updated_at
      })) || [];

      return new Response(
        JSON.stringify({
          success: true,
          reviews: formattedReviews
        }),
        {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // For POST, PUT, DELETE requests, verify user authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header provided');
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication token');
    }

    // Handle POST requests - create new review
    if (req.method === 'POST') {
      const requestData: CreateReviewRequest = await req.json();
      
      if (!requestData.publishedAgentId || !requestData.rating) {
        throw new Error('Missing required fields: publishedAgentId and rating are required');
      }

      if (requestData.rating < 1 || requestData.rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      // Verify the published agent exists
      const { data: publishedAgent, error: agentError } = await supabase
        .from('published_agents')
        .select('id')
        .eq('id', requestData.publishedAgentId)
        .eq('is_active', true)
        .single();

      if (agentError || !publishedAgent) {
        throw new Error('Published agent not found or is not active');
      }

      // Check if user has already reviewed this agent
      const { data: existingReview, error: existingError } = await supabase
        .from('agent_reviews')
        .select('id')
        .eq('published_agent_id', requestData.publishedAgentId)
        .eq('reviewer_id', user.id)
        .maybeSingle();

      if (existingError && existingError.code !== 'PGRST116') {
        throw new Error('Error checking existing review');
      }

      if (existingReview) {
        throw new Error('You have already reviewed this agent. Use PUT to update your review.');
      }

      // Create new review
      const { data: newReview, error: createError } = await supabase
        .from('agent_reviews')
        .insert({
          published_agent_id: requestData.publishedAgentId,
          reviewer_id: user.id,
          rating: requestData.rating,
          review_text: requestData.reviewText || null
        })
        .select('*')
        .single();

      if (createError) {
        throw new Error(`Failed to create review: ${createError.message}`);
      }

      return new Response(
        JSON.stringify({
          success: true,
          review: newReview,
          message: 'Review created successfully'
        }),
        {
          status: 201,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Handle PUT requests - update existing review
    if (req.method === 'PUT') {
      const requestData: UpdateReviewRequest = await req.json();
      
      if (!requestData.reviewId || !requestData.rating) {
        throw new Error('Missing required fields: reviewId and rating are required');
      }

      if (requestData.rating < 1 || requestData.rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      // Update review (RLS policy ensures user can only update their own reviews)
      const { data: updatedReview, error: updateError } = await supabase
        .from('agent_reviews')
        .update({
          rating: requestData.rating,
          review_text: requestData.reviewText || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestData.reviewId)
        .eq('reviewer_id', user.id)
        .select('*')
        .single();

      if (updateError) {
        throw new Error(`Failed to update review: ${updateError.message}`);
      }

      return new Response(
        JSON.stringify({
          success: true,
          review: updatedReview,
          message: 'Review updated successfully'
        }),
        {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Handle DELETE requests - delete review
    if (req.method === 'DELETE') {
      const reviewId = url.searchParams.get('reviewId');
      
      if (!reviewId) {
        throw new Error('reviewId parameter is required for DELETE requests');
      }

      // Delete review (RLS policy ensures user can only delete their own reviews)
      const { error: deleteError } = await supabase
        .from('agent_reviews')
        .delete()
        .eq('id', reviewId)
        .eq('reviewer_id', user.id);

      if (deleteError) {
        throw new Error(`Failed to delete review: ${deleteError.message}`);
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Review deleted successfully'
        }),
        {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    throw new Error('Method not allowed');

  } catch (error) {
    console.error('Error in agent-reviews function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
