import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Initialize Stripe with the secret key from environment variables
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Constants for plans and limits
const PLAN_TYPES = {
  BASIC: 'basic',  // The basic weekly plan - $5.99/week
  PRO: 'pro',      // The pro weekly plan - $9.99/week
};

// Price ID to plan mapping
const PRICE_ID_TO_PLAN = {
  // Basic Plan - $5.99/week
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.BASIC,
  // Pro Plan - $9.99/week
  'price_1ROYKjDebmd1GpTv5oYNMKMv': PLAN_TYPES.PRO,
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  try {
    // Parse the request body
    let body;
    try {
      body = await req.json()
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const { action, priceId, returnUrl, couponId, skipTrial } = body
    const authHeader = req.headers.get('Authorization')

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Get the authenticated user's ID
    const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Handle different actions
    switch (action) {
      case 'create-checkout-session': {
        if (!priceId) {
          return new Response(JSON.stringify({ error: 'Price ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Get or create customer
        const { data: customer } = await supabase
          .from('customers')
          .select('stripe_customer_id')
          .eq('user_id', user.id)
          .single()

        let customerId = customer?.stripe_customer_id

        if (!customerId) {
          const newCustomer = await stripe.customers.create({
            email: user.email,
            metadata: {
              user_id: user.id,
            },
          })
          customerId = newCustomer.id

          await supabase.from('customers').insert({
            user_id: user.id,
            stripe_customer_id: customerId,
          })
        }

        // Create checkout session
        const successUrl = returnUrl
          ? `${returnUrl}?session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(customerId)}`
          : `${req.headers.get('origin') || 'https://app.Osis.co'}?session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(customerId)}`;

        const cancelUrl = returnUrl
          ? `${returnUrl}?canceled=true`
          : `${req.headers.get('origin') || 'https://app.Osis.co'}?canceled=true`;

        // Check if request is from mobile device
        const userAgent = req.headers.get('user-agent') || '';
        const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

        // Get country from headers if available
        const acceptLanguage = req.headers.get('accept-language') || '';
        const countryCode = acceptLanguage.split(',')[0].split('-')[1]?.toUpperCase() || '';
        const isInternational = countryCode && countryCode !== 'US';

        const sessionConfig: Stripe.Checkout.SessionCreateParams = {
          customer: customerId,
          payment_method_types: ['card'],
          line_items: [
            {
              price: priceId,
              quantity: 1,
            },
          ],
          mode: 'subscription',
          success_url: successUrl,
          cancel_url: cancelUrl,
          allow_promotion_codes: true,
          metadata: {
            user_id: user.id,
            created_at: new Date().toISOString(),
            is_mobile: isMobile ? 'true' : 'false',
            country_code: countryCode || 'unknown',
          },
          client_reference_id: user.id,
          // Add support for international users
          billing_address_collection: 'auto', // Always use 'auto' for better international support
          locale: 'auto', // Automatically detect user's locale
          // Simplify the checkout for mobile and international users
          phone_number_collection: {
            enabled: false,
          },
        };

        // Only add trial period if not explicitly skipped
        if (!skipTrial) {
          sessionConfig.subscription_data = {
            // Add a 3-day free trial for subscriptions
            trial_period_days: 3,
          };
        }

        // Add coupon if provided
        if (couponId) {
          sessionConfig.discounts = [
            {
              coupon: couponId,
            },
          ];
        }

        const session = await stripe.checkout.sessions.create(sessionConfig);

        return new Response(JSON.stringify({
          sessionId: session.id,
          url: session.url
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'create-payment-link': {
        if (!priceId) {
          return new Response(JSON.stringify({ error: 'Price ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Create a payment link
        const paymentLink = await stripe.paymentLinks.create({
          line_items: [
            {
              price: priceId,
              quantity: 1,
            },
          ],
          allow_promotion_codes: true,
          after_completion: {
            type: 'redirect',
            redirect: {
              url: returnUrl || `${req.headers.get('origin') || 'https://app.Osis.co'}?payment_success=true`,
            },
          },
        });

        return new Response(JSON.stringify({ url: paymentLink.url }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
    }
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
