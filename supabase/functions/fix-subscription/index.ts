import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// Setup Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Message limits for each plan type - must match the frontend
const MESSAGE_LIMITS = {
  basic: 100,   // The basic paid plan - $5.99/week
  pro: 200,     // The pro paid plan - $9.99/week
  premium: 300  // The premium paid plan (legacy)
};

// Free trial message limits (3 messages per day)
const TRIAL_MESSAGE_LIMITS = {
  basic: 3,
  pro: 3,
  premium: 3
};

serve(async (req) => {
  // CORS headers for cross-origin requests
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Methods': '*'
  };

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Get user from authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    let body;
    try {
      body = await req.json();
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { planType } = body;
    if (!planType || !['basic', 'pro', 'premium'].includes(planType)) {
      return new Response(JSON.stringify({ error: 'Invalid plan type. Must be basic, pro, or premium' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Step 1: Update user's profile with correct subscription type
    const { error: profileUpdateError } = await supabase
      .from('profiles')
      .update({ subscription_type: planType })
      .eq('id', user.id);

    if (profileUpdateError) {
      return new Response(JSON.stringify({
        error: 'Failed to update profile',
        details: profileUpdateError.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Step 2: Get or determine price ID
    let priceId = '';
    switch (planType) {
      case 'premium':
        priceId = 'price_1R2FYvDebmd1GpTv3kJLnn8v'; // Legacy premium plan
        break;
      case 'pro':
        priceId = 'price_1ROYKjDebmd1GpTv5oYNMKMv'; // $9.99/week pro plan
        break;
      case 'basic':
        priceId = 'price_1ROYLKDebmd1GpTvct491Kw6'; // $5.99/week basic plan
        break;
      default:
        return new Response(
          JSON.stringify({
            error: 'Invalid plan type. Must be basic, pro, or premium.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
    }

    // Step 3: Update or create subscription record
    const { data: existingSubscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (existingSubscription) {
      // Update existing subscription
      const { error: subUpdateError } = await supabase
        .from('subscriptions')
        .update({
          status: planType === 'basic' ? 'canceled' : 'active',
          stripe_price_id: priceId,
          cancel_at_period_end: false,
          // Set period end to 30 days from now
          current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60)
        })
        .eq('id', existingSubscription.id);
    } else if (planType !== 'basic') {
      // Create new subscription if not basic
      const fakeSubscriptionId = `sub_manual_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      const { error: createSubError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          stripe_subscription_id: fakeSubscriptionId,
          stripe_price_id: priceId,
          status: 'active',
          cancel_at_period_end: false,
          current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60)
        });
    }

    // Step 4: Update user tokens
    if (!['basic', 'pro', 'premium'].includes(planType)) {
      return new Response(JSON.stringify({ error: 'Invalid plan type detected' }), { status: 400 });
    }

    // Check if user is on a free trial
    let isOnFreeTrial = false;
    if (existingSubscription) {
      isOnFreeTrial = existingSubscription.status === 'trialing';
    }

    // Determine message limit based on plan type and trial status
    let messageLimit;
    if (isOnFreeTrial) {
      messageLimit = TRIAL_MESSAGE_LIMITS[planType];
    } else {
      messageLimit = MESSAGE_LIMITS[planType];
    }

    // Check if user has tokens record
    const { data: tokensData } = await supabase
      .from('user_tokens')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (tokensData) {
      // Update existing tokens record
      const { error: tokensUpdateError } = await supabase
        .from('user_tokens')
        .update({
          tokens_remaining: messageLimit,
          last_reset: new Date().toISOString(),
          last_reset_date: new Date().toISOString()
        })
        .eq('id', tokensData.id);
    } else {
      // Create tokens record
      const { error: createTokensError } = await supabase
        .from('user_tokens')
        .insert({
          user_id: user.id,
          tokens_remaining: messageLimit,
          last_reset: new Date().toISOString(),
          last_reset_date: new Date().toISOString()
        });
    }

    // Step 5: Get updated profile and tokens for verification
    const { data: updatedProfile } = await supabase
      .from('profiles')
      .select('subscription_type')
      .eq('id', user.id)
      .single();

    const { data: updatedTokens } = await supabase
      .from('user_tokens')
      .select('tokens_remaining')
      .eq('user_id', user.id)
      .single();

    return new Response(JSON.stringify({
      success: true,
      message: `Successfully set user to ${planType} plan`,
      subscription_type: updatedProfile?.subscription_type,
      tokens_remaining: updatedTokens?.tokens_remaining,
      limit: messageLimit
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});