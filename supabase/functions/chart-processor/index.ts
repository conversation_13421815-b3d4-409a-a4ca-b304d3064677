import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Get Polygon API key from environment variables
const POLYGON_API_KEY = Deno.env.get('POLYGON_API_KEY') || '';

if (!POLYGON_API_KEY) {
  console.error('POLYGON_API_KEY is not set in environment variables');
}

// Helper function to format polygon symbol
const formatPolygonSymbol = async (symbol: string): Promise<string> => {
  // Check if it's a crypto symbol
  const isCrypto = /^[A-Z]{3,5}$/.test(symbol) &&
                  !symbol.includes('.') &&
                  ['BTC', 'ETH', 'SOL', 'XRP', 'ADA', 'DOGE', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI', 'SHIB'].includes(symbol);

  if (isCrypto) {
    return `X:${symbol}USD`;
  }

  return symbol;
};

// Function to get Polygon API parameters for different timeframes
function getPolygonTimeframeParams(timeframe: string): { from: string; to: string; multiplier: number; timespan: string } {
  // Get current date in US Eastern time (market timezone)
  const now = new Date();

  // Convert to US Eastern time (ET) string representation
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'America/New_York',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  };
  const etDateStr = now.toLocaleDateString('en-US', options);

  // Parse the ET date string back to a date object
  const [month, day, year] = etDateStr.split('/');
  const today = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

  let from: string;
  let multiplier: number;
  let timespan: string;

  switch (timeframe) {
    case '1D':
      // For 1 day, use minute data
      from = today;
      multiplier = 5;
      timespan = 'minute';
      break;
    case '1W':
      // For 1 week, use hourly data
      const oneWeekAgo = new Date(now);
      oneWeekAgo.setDate(now.getDate() - 7);
      from = oneWeekAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'hour';
      break;
    case '1M':
      // For 1 month, use daily data
      const oneMonthAgo = new Date(now);
      oneMonthAgo.setMonth(now.getMonth() - 1);
      from = oneMonthAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'day';
      break;
    case '3M':
      // For 3 months, use daily data
      const threeMonthsAgo = new Date(now);
      threeMonthsAgo.setMonth(now.getMonth() - 3);
      from = threeMonthsAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'day';
      break;
    case '6M':
      // For 6 months, use daily data
      const sixMonthsAgo = new Date(now);
      sixMonthsAgo.setMonth(now.getMonth() - 6);
      from = sixMonthsAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'day';
      break;
    case '1Y':
      // For 1 year, use daily data
      const oneYearAgo = new Date(now);
      oneYearAgo.setFullYear(now.getFullYear() - 1);
      from = oneYearAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'day';
      break;
    case '5Y':
      // For 5 years, use weekly data
      const fiveYearsAgo = new Date(now);
      fiveYearsAgo.setFullYear(now.getFullYear() - 5);
      from = fiveYearsAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'week';
      break;
    default:
      from = today;
      multiplier = 1;
      timespan = 'minute';
  }

  return {
    from,
    to: today,
    multiplier,
    timespan
  };
}

// Function to fetch chart data from Polygon
async function fetchPolygonChartData(params: { symbol: string, timeframe: string }) {
  const { symbol, timeframe } = params;

  // Format the symbol for Polygon API
  const formattedSymbol = await formatPolygonSymbol(symbol);

  // Get timeframe parameters
  const { from: startDate, to: endDate, multiplier, timespan } = getPolygonTimeframeParams(timeframe);

  try {
    // For 1D timeframe, use a more reliable approach similar to fetchLatestPriceData
    if (timeframe === '1D') {
      console.log(`[fetchPolygonChartData] Using special handling for 1D timeframe for ${symbol}`);

      // Try to get data using the regular approach first
      const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`;
      console.log(`[fetchPolygonChartData] Fetching data for ${symbol} from URL: ${url.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

      const response = await fetch(url);
      console.log(`[fetchPolygonChartData] Response status for ${symbol}: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        console.log(`[fetchPolygonChartData] Received data for ${symbol}: ${JSON.stringify(data).substring(0, 200)}...`);

        // If we have results, use them
        if (data.results && data.results.length > 0) {
          console.log(`[fetchPolygonChartData] Found ${data.results.length} results for ${symbol}`);

          // Transform the data into the required format
          const transformedData = data.results.map((item: any) => ({
            timestamp: item.t,
            open: item.o,
            high: item.h,
            low: item.l,
            close: item.c,
            volume: item.v
          }));

          return {
            data: transformedData,
            symbol: formattedSymbol,
            config: {
              timeframe,
              showVolume: true
            },
            previousClose: null,
            currentPrice: transformedData[transformedData.length - 1].close
          };
        } else {
          console.warn(`[fetchPolygonChartData] No results found for ${symbol} in regular 1D data. Trying previous close.`);
        }
      } else {
        console.error(`[fetchPolygonChartData] Error fetching regular 1D data for ${symbol}: ${response.status}`);
      }

      // If we didn't get results, try to get previous close data
      const prevCloseUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/prev?adjusted=true&apiKey=${POLYGON_API_KEY}`;
      console.log(`[fetchPolygonChartData] Fetching previous close data for ${symbol} from URL: ${prevCloseUrl.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

      const prevCloseResponse = await fetch(prevCloseUrl);
      console.log(`[fetchPolygonChartData] Previous close response status for ${symbol}: ${prevCloseResponse.status}`);

      if (prevCloseResponse.ok) {
        const prevCloseData = await prevCloseResponse.json();
        console.log(`[fetchPolygonChartData] Received previous close data for ${symbol}: ${JSON.stringify(prevCloseData).substring(0, 200)}...`);

        if (prevCloseData.results && prevCloseData.results.length > 0) {
          const prevClose = prevCloseData.results[0];
          console.log(`[fetchPolygonChartData] Using previous close data for ${symbol}`);

          // Create synthetic data points for the day
          const now = new Date();
          const marketOpen = new Date(now);
          marketOpen.setHours(9, 30, 0, 0); // 9:30 AM

          // Create synthetic data points for the day (market open to current time)
          const syntheticData = [];

          // Add market open point
          syntheticData.push({
            timestamp: marketOpen.getTime(),
            open: prevClose.c,
            high: prevClose.c,
            low: prevClose.c,
            close: prevClose.c,
            volume: 0
          });

          // Add current time point
          syntheticData.push({
            timestamp: now.getTime(),
            open: prevClose.c,
            high: prevClose.c,
            low: prevClose.c,
            close: prevClose.c,
            volume: 0
          });

          return {
            data: syntheticData,
            symbol: formattedSymbol,
            config: {
              timeframe,
              showVolume: true
            },
            previousClose: prevClose.c,
            currentPrice: prevClose.c
          };
        }
      }

      // If we still don't have data, try to get the latest quote
      const quoteUrl = `https://api.polygon.io/v2/last/trade/${formattedSymbol}?apiKey=${POLYGON_API_KEY}`;
      console.log(`[fetchPolygonChartData] Fetching latest quote for ${symbol} from URL: ${quoteUrl.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

      const quoteResponse = await fetch(quoteUrl);
      console.log(`[fetchPolygonChartData] Latest quote response status for ${symbol}: ${quoteResponse.status}`);

      if (quoteResponse.ok) {
        const quoteData = await quoteResponse.json();
        console.log(`[fetchPolygonChartData] Received latest quote data for ${symbol}: ${JSON.stringify(quoteData).substring(0, 200)}...`);

        if (quoteData.results) {
          console.log(`[fetchPolygonChartData] Using latest quote data for ${symbol}`);

          // Create synthetic data points for the day
          const now = new Date();
          const marketOpen = new Date(now);
          marketOpen.setHours(9, 30, 0, 0); // 9:30 AM

          // Create synthetic data points for the day (market open to current time)
          const syntheticData = [];

          // Add market open point
          syntheticData.push({
            timestamp: marketOpen.getTime(),
            open: quoteData.results.price,
            high: quoteData.results.price,
            low: quoteData.results.price,
            close: quoteData.results.price,
            volume: 0
          });

          // Add current time point
          syntheticData.push({
            timestamp: now.getTime(),
            open: quoteData.results.price,
            high: quoteData.results.price,
            low: quoteData.results.price,
            close: quoteData.results.price,
            volume: 0
          });

          return {
            data: syntheticData,
            symbol: formattedSymbol,
            config: {
              timeframe,
              showVolume: true
            },
            previousClose: null,
            currentPrice: quoteData.results.price
          };
        }
      }

      // If all else fails, return empty data with a specific error message
      console.warn(`[fetchPolygonChartData] Could not get any data for ${symbol} for 1D timeframe`);
      return {
        data: [],
        symbol: formattedSymbol,
        config: {
          timeframe,
          showVolume: true
        },
        previousClose: null,
        currentPrice: null,
        error: `No data available for ${symbol} for 1D timeframe`
      };
    }

    // For other timeframes, use the original approach
    const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`;

    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Polygon API error for ${formattedSymbol}: ${response.status} - ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.results || !Array.isArray(data.results)) {
      console.error(`Invalid data format for ${formattedSymbol}:`, JSON.stringify(data));
      throw new Error(`Invalid data format received from API: ${JSON.stringify(data)}`);
    }

    if (data.results.length === 0) {
      console.warn(`No data points found for ${formattedSymbol} in timeframe ${timeframe}`);
      // Return empty data instead of throwing an error
      return {
        data: [],
        symbol: formattedSymbol,
        config: {
          timeframe,
          showVolume: true
        },
        previousClose: null,
        currentPrice: null
      };
    }

    // Transform the data into the required format
    const transformedData = data.results.map((item: any) => ({
      timestamp: item.t,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v
    }));

    return {
      data: transformedData,
      symbol: formattedSymbol,
      config: {
        timeframe,
        showVolume: true
      },
      previousClose: null,
      currentPrice: transformedData[transformedData.length - 1].close
    };
  } catch (error) {
    console.error(`Error in fetchPolygonChartData for ${symbol}:`, error);
    // Return empty data instead of throwing
    return {
      data: [],
      symbol: formattedSymbol,
      config: {
        timeframe,
        showVolume: true
      },
      previousClose: null,
      currentPrice: null,
      error: error.message
    };
  }
}

// Function to check if the US stock market is currently open
function isMarketOpen(): boolean {
  // Get current date and time in US Eastern Time (ET)
  const now = new Date();
  const etOptions: Intl.DateTimeFormatOptions = { timeZone: 'America/New_York' };
  const etDate = new Date(now.toLocaleString('en-US', etOptions));

  // Get day of week (0 = Sunday, 6 = Saturday)
  const dayOfWeek = etDate.getDay();

  // Check if it's a weekend
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false;
  }

  // Get hours and minutes in ET
  const hours = etDate.getHours();
  const minutes = etDate.getMinutes();
  const currentTimeInMinutes = hours * 60 + minutes;

  // Regular market hours: 9:30 AM to 4:00 PM ET
  const marketOpenTimeInMinutes = 9 * 60 + 30;  // 9:30 AM
  const marketCloseTimeInMinutes = 16 * 60;     // 4:00 PM

  // Check if current time is within market hours
  const isOpen = currentTimeInMinutes >= marketOpenTimeInMinutes &&
                 currentTimeInMinutes < marketCloseTimeInMinutes;

  return isOpen;
}

// Function to get the most recent market day
function getMostRecentMarketDay(): string {
  const now = new Date();
  const etOptions: Intl.DateTimeFormatOptions = { timeZone: 'America/New_York' };
  let etDate = new Date(now.toLocaleString('en-US', etOptions));

  // Get day of week (0 = Sunday, 6 = Saturday)
  const dayOfWeek = etDate.getDay();

  // Adjust date to most recent market day
  if (dayOfWeek === 0) { // Sunday, go back to Friday
    etDate.setDate(etDate.getDate() - 2);
  } else if (dayOfWeek === 6) { // Saturday, go back to Friday
    etDate.setDate(etDate.getDate() - 1);
  } else {
    // For weekdays, if it's before market open, use previous day
    const hours = etDate.getHours();
    const minutes = etDate.getMinutes();
    const currentTimeInMinutes = hours * 60 + minutes;
    const marketOpenTimeInMinutes = 9 * 60 + 30; // 9:30 AM

    if (currentTimeInMinutes < marketOpenTimeInMinutes) {
      // Before market open, use previous day
      etDate.setDate(etDate.getDate() - 1);

      // If that takes us to a weekend, adjust further
      const prevDayOfWeek = etDate.getDay();
      if (prevDayOfWeek === 0) { // Sunday, go back to Friday
        etDate.setDate(etDate.getDate() - 2);
      } else if (prevDayOfWeek === 6) { // Saturday, go back to Friday
        etDate.setDate(etDate.getDate() - 1);
      }
    }
  }

  // Format as YYYY-MM-DD
  const year = etDate.getFullYear();
  const month = (etDate.getMonth() + 1).toString().padStart(2, '0');
  const day = etDate.getDate().toString().padStart(2, '0');

  const formattedDate = `${year}-${month}-${day}`;
  return formattedDate;
}

// Function to fetch latest price data
async function fetchLatestPriceData(symbols: string | string[], timeframe = '1D'): Promise<LatestPriceDataResult> {
  console.log(`[fetchLatestPriceData] Starting with symbols: ${JSON.stringify(symbols)}, timeframe: ${timeframe}`);

  // Convert single symbol to array
  const symbolsArray = Array.isArray(symbols) ? symbols : [symbols];
  console.log(`[fetchLatestPriceData] Processing ${symbolsArray.length} symbols: ${JSON.stringify(symbolsArray)}`);

  const results: PriceData[] = [];
  const requestedTimeframe = timeframe;

  // Check if market is currently open
  const marketOpen = isMarketOpen();
  console.log(`[fetchLatestPriceData] Market open status: ${marketOpen}`);

  for (const symbol of symbolsArray) {
    try {
      // Format symbol for Polygon API
      const formattedSymbol = await formatPolygonSymbol(symbol);

      // Check if it's a crypto symbol (handle differently)
      const isCrypto = formattedSymbol.startsWith('X:');

      // Calculate timestamps for the current day in US Eastern time (market timezone)
      const now = new Date();

      // Convert to US Eastern time (ET) string representation
      const options: Intl.DateTimeFormatOptions = {
        timeZone: 'America/New_York',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      };
      const etDateStr = now.toLocaleDateString('en-US', options);

      // Parse the ET date string back to a date object
      const [month, day, year] = etDateStr.split('/');
      const today = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

      // Determine which date to use based on market status
      let startDate: string;
      let endDate: string;

      if (isCrypto) {
        // For crypto, always use today's data regardless of market hours
        startDate = today;
        endDate = today;
      } else if (marketOpen) {
        // Market is open, use today's data up to 15 minutes ago
        startDate = today;
        endDate = today;
      } else {
        // Market is closed (after hours, overnight, weekend)
        // Use the most recent market day's data
        const mostRecentMarketDay = getMostRecentMarketDay();
        startDate = mostRecentMarketDay;
        endDate = mostRecentMarketDay;
      }

      // Special handling for 1D timeframe
      let data: any;
      let response: any;
      let attempts = 0;

      // For 1D timeframe, use a more reliable approach
      if (requestedTimeframe === '1D') {
        console.log(`[fetchLatestPriceData] Using special handling for 1D timeframe for ${symbol}`);

        // For stocks (not crypto), we want to get the absolute latest quote
        if (!isCrypto && marketOpen) {
          // Get the latest quote from Polygon
          const quoteUrl = `https://api.polygon.io/v2/last/trade/${formattedSymbol}?apiKey=${POLYGON_API_KEY}`;
          console.log(`[fetchLatestPriceData] Fetching latest quote for ${symbol} from URL: ${quoteUrl.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

          response = await fetch(quoteUrl);
          console.log(`[fetchLatestPriceData] Latest quote response status for ${symbol}: ${response.status}`);

          if (response.ok) {
            const quoteData = await response.json();
            console.log(`[fetchLatestPriceData] Received latest quote data for ${symbol}: ${JSON.stringify(quoteData).substring(0, 200)}...`);

            // If we have quote data, create a synthetic result
            if (quoteData.results) {
              console.log(`[fetchLatestPriceData] Using latest quote data for ${symbol}`);

              // Create a synthetic result using the quote data
              data = {
                ticker: formattedSymbol,
                queryCount: 1,
                resultsCount: 1,
                adjusted: true,
                results: [{
                  v: quoteData.results.size || 0, // Volume
                  o: quoteData.results.price || 0, // Open (using price as fallback)
                  c: quoteData.results.price || 0, // Close
                  h: quoteData.results.price || 0, // High (using price as fallback)
                  l: quoteData.results.price || 0, // Low (using price as fallback)
                  t: quoteData.results.timestamp || Date.now() // Timestamp
                }],
                status: "OK"
              };
            } else {
              console.warn(`[fetchLatestPriceData] No quote data found for ${symbol}. Falling back to previous close.`);
              data = null; // Reset data so we know we need to try another approach
            }
          } else {
            console.error(`[fetchLatestPriceData] Error fetching latest quote for ${symbol}: ${response.status}`);
            data = null; // Reset data so we know we need to try another approach
          }
        }

        // If we don't have data yet (either because it's crypto, market is closed, or the above failed)
        if (!data || !data.results || data.results.length === 0) {
          // Get the previous close data as a fallback
          const prevCloseUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/prev?adjusted=true&apiKey=${POLYGON_API_KEY}`;
          console.log(`[fetchLatestPriceData] Fetching previous close data for ${symbol} from URL: ${prevCloseUrl.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

          response = await fetch(prevCloseUrl);
          console.log(`[fetchLatestPriceData] Previous close response status for ${symbol}: ${response.status}`);

          if (response.ok) {
            data = await response.json();
            console.log(`[fetchLatestPriceData] Received previous close data for ${symbol}: ${JSON.stringify(data).substring(0, 200)}...`);

            // If we have previous close data, we'll use it
            if (data.results && data.results.length > 0) {
              console.log(`[fetchLatestPriceData] Using previous close data for ${symbol}`);
              // We'll process this data later
            } else {
              // If no previous close data, try to get the latest quote
              console.warn(`[fetchLatestPriceData] No previous close data found for ${symbol}. Trying to get latest quote.`);
              data = null; // Reset data so we know we need to try another approach
            }
          } else {
            console.error(`[fetchLatestPriceData] Error fetching previous close data for ${symbol}: ${response.status}`);
            data = null; // Reset data so we know we need to try another approach
          }

          // If we couldn't get previous close data, try to get the latest quote
          if (!data || !data.results || data.results.length === 0) {
            const quoteUrl = `https://api.polygon.io/v2/last/trade/${formattedSymbol}?apiKey=${POLYGON_API_KEY}`;
            console.log(`[fetchLatestPriceData] Fetching latest quote for ${symbol} from URL: ${quoteUrl.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

            response = await fetch(quoteUrl);
            console.log(`[fetchLatestPriceData] Latest quote response status for ${symbol}: ${response.status}`);

            if (response.ok) {
              const quoteData = await response.json();
              console.log(`[fetchLatestPriceData] Received latest quote data for ${symbol}: ${JSON.stringify(quoteData).substring(0, 200)}...`);

              // If we have quote data, create a synthetic result
              if (quoteData.results) {
                console.log(`[fetchLatestPriceData] Using latest quote data for ${symbol}`);

                // Create a synthetic result using the quote data
                data = {
                  ticker: formattedSymbol,
                  queryCount: 1,
                  resultsCount: 1,
                  adjusted: true,
                  results: [{
                    v: quoteData.results.size || 0, // Volume
                    o: quoteData.results.price || 0, // Open (using price as fallback)
                    c: quoteData.results.price || 0, // Close
                    h: quoteData.results.price || 0, // High (using price as fallback)
                    l: quoteData.results.price || 0, // Low (using price as fallback)
                    t: quoteData.results.timestamp || Date.now() // Timestamp
                  }],
                  status: "OK"
                };
              }
            } else {
              console.error(`[fetchLatestPriceData] Error fetching latest quote for ${symbol}: ${response.status}`);
            }
          }
        }
      } else {
        // For other timeframes, use the original approach with multiple attempts
        let attempts = 0;
        const maxAttempts = 3;

        // We'll try multiple date ranges if needed
        while (attempts < maxAttempts) {
          attempts++;

          // Adjust date range based on attempt number
          let queryStartDate = startDate;
          let queryEndDate = endDate;

          if (attempts > 1) {
            // For second attempt, try the previous market day
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() - (attempts - 1));
            const previousDay = currentDate.toISOString().split('T')[0];
            console.log(`[fetchLatestPriceData] Attempt ${attempts}: Trying previous day ${previousDay} for ${symbol}`);
            queryStartDate = previousDay;
            queryEndDate = previousDay;
          }

          const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/minute/${queryStartDate}/${queryEndDate}?adjusted=true&sort=desc&limit=15&apiKey=${POLYGON_API_KEY}`;
          console.log(`[fetchLatestPriceData] Attempt ${attempts}: Fetching data for ${symbol} from URL: ${url.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')}`);

          response = await fetch(url);
          console.log(`[fetchLatestPriceData] Attempt ${attempts}: Response status for ${symbol}: ${response.status}`);

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`Attempt ${attempts}: Error fetching data for ${symbol}: ${response.status} - ${errorText}`);
            continue; // Try next attempt
          }

          data = await response.json();
          console.log(`[fetchLatestPriceData] Attempt ${attempts}: Received data for ${symbol}: ${JSON.stringify(data).substring(0, 200)}...`);

          // If we got results, break out of the loop
          if (data.results && data.results.length > 0) {
            console.log(`[fetchLatestPriceData] Found ${data.results.length} results for ${symbol} on attempt ${attempts}`);
            break;
          } else {
            console.warn(`[fetchLatestPriceData] Attempt ${attempts}: No results found for ${symbol}. Trying again with different date range.`);
          }
        }
      }

      // While we're getting the 15-min data, also get the correct timeframe's percentage change
      let timeframePercentageChange = 0;
      try {
        const timeframeData = await fetchTimeframePercentageChange(formattedSymbol, requestedTimeframe);
        timeframePercentageChange = timeframeData.percentChange;
      } catch (error) {
        console.error(`Error fetching timeframe data for ${symbol}:`, error);
        // Continue with just the 15-min data if there's an error
      }

      // Process the results if we have data
      if (data && data.results && data.results.length > 0) {
        console.log(`[fetchLatestPriceData] Processing ${data.results.length} data points for ${symbol}`);

        // Get the latest result (first in the array since it's sorted desc)
        const latestResult = data.results[0];

        // Get the earliest result in our 15-min window
        const earliestResult = data.results[data.results.length - 1];

        // Calculate the price change over the 15-min period
        const recentPrice = latestResult.c;
        const previousPrice = earliestResult.o;
        const change = recentPrice - previousPrice;
        const percentChange = (change / previousPrice) * 100;

        // Calculate total volume
        const totalVolume = data.results.reduce((sum: number, bar: any) => sum + bar.v, 0);

        const priceData: PriceData = {
          symbol,
          formattedSymbol,
          price: recentPrice,
          change,
          percentChange,
          volume: totalVolume,
          timestamp: latestResult.t,
          dataType: isCrypto ? 'crypto_live' : (marketOpen ? 'live' : 'last_market_close'),
          dataPoints: data.results.length,
          open: earliestResult.o,
          timeframe: requestedTimeframe,
          timeframePercentChange: timeframePercentageChange,
          marketOpen: marketOpen || isCrypto // Consider crypto markets always open
        };

        console.log(`[fetchLatestPriceData] Created price data for ${symbol}: ${JSON.stringify(priceData)}`);
        results.push(priceData);
      } else {
        console.warn(`[fetchLatestPriceData] No results found for ${symbol} after ${attempts} attempts. Using fallback data.`);

        // Create fallback data with the last known price or a default
        // Try to get the last known price from Polygon's previous close endpoint
        try {
          console.log(`[fetchLatestPriceData] Fetching previous close data for ${symbol}`);
          const prevCloseUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/prev?adjusted=true&apiKey=${POLYGON_API_KEY}`;
          const prevCloseResponse = await fetch(prevCloseUrl);

          if (prevCloseResponse.ok) {
            const prevCloseData = await prevCloseResponse.json();
            console.log(`[fetchLatestPriceData] Previous close data: ${JSON.stringify(prevCloseData).substring(0, 200)}...`);

            if (prevCloseData.results && prevCloseData.results.length > 0) {
              const prevClose = prevCloseData.results[0];

              // Create price data with previous close
              const fallbackPriceData: PriceData = {
                symbol,
                formattedSymbol,
                price: prevClose.c,
                change: 0,
                percentChange: 0,
                volume: prevClose.v,
                timestamp: prevClose.t,
                dataType: 'previous_close',
                dataPoints: 1,
                open: prevClose.o,
                timeframe: requestedTimeframe,
                timeframePercentChange: 0,
                marketOpen: false
              };

              console.log(`[fetchLatestPriceData] Created fallback price data for ${symbol}: ${JSON.stringify(fallbackPriceData)}`);
              results.push(fallbackPriceData);
              continue; // Skip to the next symbol in the loop
            }
          }

          // If we get here, the previous close endpoint failed or returned no data
          console.warn(`[fetchLatestPriceData] Could not get previous close data for ${symbol}. Using default values.`);
        } catch (error) {
          console.error(`[fetchLatestPriceData] Error fetching previous close data for ${symbol}:`, error);
        }

        // If all else fails, use a default placeholder
        const defaultPriceData: PriceData = {
          symbol,
          formattedSymbol,
          price: 0, // We don't know the price
          change: 0,
          percentChange: 0,
          volume: 0,
          timestamp: Date.now(),
          dataType: 'no_data',
          dataPoints: 0,
          open: 0,
          timeframe: requestedTimeframe,
          timeframePercentChange: 0,
          marketOpen: false,
          error: 'No data available for this symbol'
        };

        console.log(`[fetchLatestPriceData] Created default price data for ${symbol}: ${JSON.stringify(defaultPriceData)}`);
        results.push(defaultPriceData);
      }
    } catch (error) {
      console.error(`Error processing ${symbol}:`, error);
    }
  }

  return results;
}

// Function to fetch percentage change for specific timeframe
async function fetchTimeframePercentageChange(formattedSymbol: string, timeframe: string): Promise<{percentChange: number}> {
  // Check if it's a crypto symbol
  const isCrypto = formattedSymbol.startsWith('X:');

  // For stocks, check market status and adjust date range if needed
  let { multiplier, timespan, from, to } = getPolygonTimeframeParams(timeframe);

  // If it's not crypto and market is closed, make sure we're using the most recent market day
  if (!isCrypto && !isMarketOpen() && timeframe === '1D') {
    // For 1D timeframe when market is closed, use the most recent market day
    const mostRecentMarketDay = getMostRecentMarketDay();
    to = mostRecentMarketDay;
    from = mostRecentMarketDay;
  }

  const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/${multiplier}/${timespan}/${from}/${to}?adjusted=true&sort=asc&apiKey=${POLYGON_API_KEY}`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.results || !Array.isArray(data.results) || data.results.length === 0) {
      return { percentChange: 0 };
    }

    // Get first and last price points
    const firstBar = data.results[0];
    const lastBar = data.results[data.results.length - 1];

    // Calculate percentage change
    const startPrice = firstBar.o;
    const endPrice = lastBar.c;
    const percentChange = ((endPrice - startPrice) / startPrice) * 100;

    return { percentChange };
  } catch (error) {
    console.error(`Error fetching timeframe data for ${formattedSymbol}:`, error);
    return { percentChange: 0 };
  }
}

// Define interface for price data to fix TypeScript errors
interface PriceData {
  symbol: string;
  formattedSymbol: string;
  price: number;
  change: number;
  percentChange: number;
  volume: number;
  timestamp: number;
  dataType: string;
  dataPoints: number;
  open: number;
  timeframe: string;
  timeframePercentChange: number;
  marketOpen?: boolean;
  error?: string; // Optional error message
}

// Define the return type for fetchLatestPriceData
type LatestPriceDataResult = PriceData[];

// Function to fetch multiple timeframe price data
async function fetchMultiTimeframePriceData(symbol: string, timeframes: string[] = ['1D', '1W', '1M', '1Y', '5Y']): Promise<Record<string, PriceData>> {
  const results: Record<string, PriceData> = {};

  try {
    // Format symbol for Polygon API
    const formattedSymbol = await formatPolygonSymbol(symbol);

    // Check if it's a crypto symbol
    const isCrypto = formattedSymbol.startsWith('X:');

    // Check if market is open
    const marketOpen = isMarketOpen() || isCrypto; // Consider crypto markets always open

    // Fetch latest data first for the current price
    const latestData = await fetchLatestPriceData(symbol, '1D');

    if (!latestData || latestData.length === 0) {
      return {}; // Return empty if we can't even get basic data
    }

    // Get the base data from the latest data fetch
    const baseLatestData = latestData[0];

    // Process each timeframe in parallel
    const promises = timeframes.map(async (timeframe) => {
      try {
        // Use the dedicated percentage change function for consistency
        const { percentChange } = await fetchTimeframePercentageChange(formattedSymbol, timeframe);

        // Create a base object with all the properties we need
        const baseData: PriceData = {
          symbol: baseLatestData.symbol,
          formattedSymbol: baseLatestData.formattedSymbol,
          price: baseLatestData.price,
          change: baseLatestData.change,
          percentChange: percentChange, // Use the timeframe-specific percentage
          volume: baseLatestData.volume,
          timestamp: baseLatestData.timestamp,
          dataType: isCrypto ? 'crypto_live' : (marketOpen && timeframe === '1D' ? 'live' : 'last_market_close'),
          dataPoints: baseLatestData.dataPoints,
          open: baseLatestData.open,
          timeframe: timeframe,
          timeframePercentChange: percentChange,
          marketOpen: marketOpen
        };

        // Store results with the timeframe as key
        results[timeframe] = baseData;
      } catch (error) {
        console.error(`Error processing timeframe ${timeframe} for ${symbol}:`, error);
        // Use default data for this timeframe if there's an error
        if (latestData && latestData.length > 0) {
          results[timeframe] = {
            symbol: baseLatestData.symbol,
            formattedSymbol: baseLatestData.formattedSymbol,
            price: baseLatestData.price,
            change: baseLatestData.change,
            percentChange: 0,
            volume: baseLatestData.volume,
            timestamp: baseLatestData.timestamp,
            dataType: isCrypto ? 'crypto_live' : (marketOpen && timeframe === '1D' ? 'live' : 'last_market_close'),
            dataPoints: baseLatestData.dataPoints,
            open: baseLatestData.open,
            timeframe: timeframe,
            timeframePercentChange: 0,
            marketOpen: marketOpen
          };
        }
      }
    });

    // Wait for all timeframes to be processed
    await Promise.all(promises);

    return results;
  } catch (error) {
    console.error(`Error in fetchMultiTimeframePriceData for ${symbol}:`, error);
    return {};
  }
}

// Define request type for Deno
interface RequestWithJson extends Request {
  json(): Promise<any>;
}

// Main serve function
serve(async (req: RequestWithJson) => {
  console.log(`[chart-processor] Received request: ${req.method} ${req.url}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('[chart-processor] Handling CORS preflight request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body
    let body: { action: string; [key: string]: any };
    try {
      body = await req.json();
      console.log(`[chart-processor] Request body: ${JSON.stringify(body)}`);
    } catch (e) {
      console.error(`[chart-processor] Error parsing request body: ${e.message}`);
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { action, ...params } = body;
    console.log(`[chart-processor] Action: ${action}, Params: ${JSON.stringify(params)}`);

    // Get the authenticated user's ID
    const authHeader = req.headers.get('Authorization');
    console.log(`[chart-processor] Authorization header present: ${!!authHeader}`);

    if (!authHeader) {
      console.error('[chart-processor] No authorization header provided');
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const token = authHeader.replace('Bearer ', '');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    // Check if this is a service role request (from another edge function)
    if (token === serviceRoleKey) {
      console.log('[chart-processor] Service role authentication detected');
      console.log(`[chart-processor] Processing action: ${action}...`);
    } else {
      // Regular user authentication
      console.log('[chart-processor] Getting user from auth token');
      const { data: { user }, error: userError } = await supabase.auth.getUser(token);

      if (userError || !user) {
        console.error(`[chart-processor] Authentication error: ${userError?.message || 'No user found'}`);
        return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      console.log(`[chart-processor] Authenticated user: ${user.id}`);
      console.log(`[chart-processor] Processing action: ${action}...`);
    }

    // Handle different actions
    switch (action) {
      case 'chart-data': {
        const { symbol, timeframe } = params;

        if (!symbol) {
          return new Response(JSON.stringify({ error: 'Symbol is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        const data = await fetchPolygonChartData({
          symbol,
          timeframe: timeframe || '1D'
        });

        return new Response(JSON.stringify(data), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'latest-price': {
        console.log(`[latest-price] Action handler called with params: ${JSON.stringify(params)}`);
        const { symbol, timeframe } = params;

        if (!symbol) {
          console.error('[latest-price] Symbol is required but not provided');
          return new Response(JSON.stringify({ error: 'Symbol is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        console.log(`[latest-price] Calling fetchLatestPriceData with symbol: ${symbol}, timeframe: ${timeframe || '1D'}`);
        const data = await fetchLatestPriceData(symbol, timeframe || '1D');
        console.log(`[latest-price] Received data from fetchLatestPriceData: ${JSON.stringify(data)}`);

        return new Response(JSON.stringify(data), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'multi-timeframe': {
        const { symbol, timeframes } = params;

        if (!symbol) {
          return new Response(JSON.stringify({ error: 'Symbol is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        const data = await fetchMultiTimeframePriceData(
          symbol,
          timeframes || ['1D', '1W', '1M', '1Y', '5Y']
        );

        return new Response(JSON.stringify(data), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
    }
  } catch (error: any) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
