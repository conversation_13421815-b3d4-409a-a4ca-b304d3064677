import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Constants for plans and limits
const PLAN_TYPES = {
  BASIC: 'basic',  // The basic weekly plan - $5.99/week
  PRO: 'pro',      // The pro weekly plan - $9.99/week
};

const PRICE_ID_TO_PLAN = {
  // Basic Plan - $5.99/week
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.BASIC,
  // Pro Plan - $9.99/week
  'price_1ROYKjDebmd1GpTv5oYNMKMv': PLAN_TYPES.PRO,
};

// Message limits by plan
const MESSAGE_LIMITS = {
  [PLAN_TYPES.BASIC]: 100, // 100 messages for basic plan
  [PLAN_TYPES.PRO]: 200    // 200 messages for pro plan
};

// Free trial message limits (3 messages per day)
const TRIAL_MESSAGE_LIMITS = {
  [PLAN_TYPES.BASIC]: 3,
  [PLAN_TYPES.PRO]: 3
};

// Use the live key for production
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16', // Use stable version instead of future date
  httpClient: Stripe.createFetchHttpClient(),
});

const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Helper function to update user tokens based on plan
const updateUserTokens = async (userId: string, planType: string, isTrialing: boolean = false, revokeAccess: boolean = false) => {
  try {
    if (!PLAN_TYPES[planType.toUpperCase() as keyof typeof PLAN_TYPES]) {
      throw new Error(`Invalid plan type: ${planType}`);
    }

    // Determine message limit based on plan type and trial status
    let messageLimit;

    if (revokeAccess) {
      // If access is being revoked, set message limit to 0
      messageLimit = 0;
    } else if (isTrialing) {
      // Trial users get 3 messages per day for 3 days
      messageLimit = TRIAL_MESSAGE_LIMITS[planType.toUpperCase() as keyof typeof TRIAL_MESSAGE_LIMITS] || 3;
    } else {
      // Paid users get their full message allowance immediately (100 for Basic, 200 for Pro)
      messageLimit = MESSAGE_LIMITS[planType.toUpperCase() as keyof typeof MESSAGE_LIMITS];
    }

    // Check if user has a tokens record
    const { data: existingTokens, error: tokensError } = await supabase
      .from('user_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokensError && tokensError.code !== 'PGRST116') {
      return;
    }

    const tokenData = {
      tokens_remaining: messageLimit,
      last_reset: new Date().toISOString(),
      last_reset_date: new Date().toISOString()
    };

    if (!existingTokens) {
      // Create new tokens record
      const { error: insertError } = await supabase
        .from('user_tokens')
        .insert({
          user_id: userId,
          ...tokenData
        });

      if (insertError) {
        // As a last resort, try to upsert instead
        const { error: upsertError } = await supabase
          .from('user_tokens')
          .upsert({
            user_id: userId,
            ...tokenData
          });
      }
    } else {
      // Update existing tokens
      const { error: updateError } = await supabase
        .from('user_tokens')
        .update(tokenData)
        .eq('user_id', userId);

      if (updateError) {
        // If the update failed using user_id, try using the record ID
        if (existingTokens.id) {
          const { error: idUpdateError } = await supabase
            .from('user_tokens')
            .update(tokenData)
            .eq('id', existingTokens.id);
        }
      }
    }
  } catch (err) {
    // Error handling without logging
  }
};

// Function to revoke user access
const revokeUserAccess = async (userId: string) => {
  try {
    // 1. Update profile to remove subscription type
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        subscription_type: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      throw new Error(`Failed to update profile when revoking access: ${profileError.message}`);
    }

    // 2. Set user tokens to 0
    await updateUserTokens(userId, PLAN_TYPES.BASIC, false, true);

    return true;
  } catch (error) {
    throw error;
  }
};

// Function to fix any existing canceled trials
const fixCanceledTrials = async () => {
  try {
    // Find all users with trialing status and cancel_at_period_end = true
    const { data: canceledTrials, error: queryError } = await supabase
      .from('subscriptions')
      .select('user_id')
      .eq('status', 'trialing')
      .eq('cancel_at_period_end', true);

    if (queryError) {
      throw new Error(`Failed to query canceled trials: ${queryError.message}`);
    }

    if (!canceledTrials || canceledTrials.length === 0) {
      return { processed: 0, results: [] };
    }

    // Process each canceled trial
    const results: Array<{user_id: string, status: string, message?: string}> = [];
    for (const subscription of canceledTrials) {
      try {
        await revokeUserAccess(subscription.user_id);
        results.push({
          user_id: subscription.user_id,
          status: 'access_revoked'
        });
      } catch (error) {
        results.push({
          user_id: subscription.user_id,
          status: 'error',
          message: error.message
        });
      }
    }

    return { processed: results.length, results };
  } catch (error) {
    throw error;
  }
};

serve(async (req) => {
  // CORS headers for cross-origin requests
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Methods': '*'
  }

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  try {
    // Parse the request body
    let body;
    try {
      body = await req.json()
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const { action, ...data } = body
    const authHeader = req.headers.get('Authorization')

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Get the authenticated user's ID
    const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // First, fix any existing canceled trials
    try {
      await fixCanceledTrials();
    } catch (error) {
      // Log but continue processing the request
      console.error('Error fixing canceled trials:', error);
    }

    switch (action) {
      case 'create-customer': {
        // Check if customer already exists
        const { data: existingCustomer } = await supabase
          .from('customers')
          .select('stripe_customer_id')
          .eq('user_id', user.id)
          .single()

        if (existingCustomer?.stripe_customer_id) {
          return new Response(JSON.stringify({ customerId: existingCustomer.stripe_customer_id }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Create new customer
        const customer = await stripe.customers.create({
          email: user.email,
          metadata: {
            user_id: user.id,
          },
        })

        // Store customer ID in database
        await supabase.from('customers').insert({
          user_id: user.id,
          stripe_customer_id: customer.id,
        })

        return new Response(JSON.stringify({ customerId: customer.id }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      case 'create-subscription': {
        const { priceId } = data

        // Get or create customer
        const { data: customer } = await supabase
          .from('customers')
          .select('stripe_customer_id')
          .eq('user_id', user.id)
          .single()

        let customerId = customer?.stripe_customer_id

        if (!customerId) {
          const newCustomer = await stripe.customers.create({
            email: user.email,
            metadata: {
              user_id: user.id,
            },
          })
          customerId = newCustomer.id

          await supabase.from('customers').insert({
            user_id: user.id,
            stripe_customer_id: customerId,
          })
        }

        // Create checkout session for subscription
        try {
          // Update redirect URLs to the specified domain
          const baseRedirectUrl = "https://app.Osis.co";
          const successUrl = `${baseRedirectUrl}?session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(customerId)}`;
          const cancelUrl = `${baseRedirectUrl}?canceled=true`;

          // Check if request is from mobile device
          const userAgent = req.headers.get('user-agent') || '';
          const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

          // Get country from headers if available
          const acceptLanguage = req.headers.get('accept-language') || '';
          const countryCode = acceptLanguage.split(',')[0].split('-')[1]?.toUpperCase() || '';
          const isInternational = countryCode && countryCode !== 'US';

          const session = await stripe.checkout.sessions.create({
            customer: customerId,
            payment_method_types: ['card'],
            line_items: [
              {
                price: priceId,
                quantity: 1,
              },
            ],
            mode: 'subscription',
            success_url: successUrl,
            cancel_url: cancelUrl,
            allow_promotion_codes: true,
            metadata: {
              user_id: user.id,
              created_at: new Date().toISOString(),
              is_mobile: isMobile ? 'true' : 'false',
              country_code: countryCode || 'unknown',
            },
            client_reference_id: user.id,
            // Add support for international users
            billing_address_collection: 'auto', // Always use 'auto' for better international support
            locale: 'auto', // Automatically detect user's locale
            // Simplify the checkout for mobile and international users
            phone_number_collection: {
              enabled: false,
            },
          });


          return new Response(JSON.stringify({ sessionId: session.id }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      case 'update-subscription': {
        const { priceId } = data

        if (!priceId) {
          return new Response(JSON.stringify({ error: 'Price ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Get the user's current subscription
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (!subscription) {
          return new Response(JSON.stringify({ error: 'No active subscription found' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        try {
          // UPDATED APPROACH: Instead of directly updating the subscription,
          // we'll create a checkout session to handle the upgrade

          // Update redirect URLs to the specified domain
          const baseRedirectUrl = "https://app.Osis.co";
          const successUrl = `${baseRedirectUrl}?session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(subscription.stripe_customer_id)}`;
          const cancelUrl = `${baseRedirectUrl}?canceled=true`;

          // Check if request is from mobile device
          const userAgent = req.headers.get('user-agent') || '';
          const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

          // Get country from headers if available
          const acceptLanguage = req.headers.get('accept-language') || '';
          const countryCode = acceptLanguage.split(',')[0].split('-')[1]?.toUpperCase() || '';
          const isInternational = countryCode && countryCode !== 'US';

          // Create a checkout session for the subscription update
          const session = await stripe.checkout.sessions.create({
            customer: subscription.stripe_customer_id,
            payment_method_types: ['card'],
            line_items: [
              {
                price: priceId,
                quantity: 1,
              },
            ],
            mode: 'subscription',
            success_url: successUrl,
            cancel_url: cancelUrl,
            allow_promotion_codes: true,
            metadata: {
              user_id: user.id,
              created_at: new Date().toISOString(),
              update_type: 'plan_change',
              previous_subscription_id: subscription.stripe_subscription_id,
              previous_price_id: subscription.stripe_price_id,
              is_mobile: isMobile ? 'true' : 'false',
              country_code: countryCode || 'unknown',
            },
            client_reference_id: user.id,
            // Add support for international users
            billing_address_collection: 'auto', // Always use 'auto' for better international support
            locale: 'auto', // Automatically detect user's locale
            // Simplify the checkout for mobile and international users
            phone_number_collection: {
              enabled: false,
            },
            // This will cancel the old subscription when the new one is created
            subscription_data: {
              // Transfer metadata from old subscription
              metadata: {
                user_id: user.id,
                supabase_user_id: user.id
              }
            }
          });

          return new Response(JSON.stringify({ sessionId: session.id }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      case 'get-subscription': {
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (!subscription) {
          return new Response(JSON.stringify({ subscription: null }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Get the latest subscription data from Stripe
        let stripeSubscription;
        try {
          stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripe_subscription_id
          );
        } catch (error) {
          // If we can't get the subscription from Stripe, use the data we have
          stripeSubscription = null;
        }

        // Determine if we need to update our subscription data based on Stripe's data
        if (stripeSubscription) {
          const updates: Record<string, any> = {};
          let needsUpdate = false;

          // Check if the status changed
          if (stripeSubscription.status !== subscription.status) {
            updates.status = stripeSubscription.status;
            updates.current_period_end = stripeSubscription.current_period_end;
            updates.cancel_at_period_end = stripeSubscription.cancel_at_period_end;
            needsUpdate = true;
          }

          // Check if the price changed
          if (stripeSubscription.items?.data?.length > 0) {
            const stripePriceId = stripeSubscription.items.data[0].price.id;
            if (stripePriceId !== subscription.stripe_price_id) {
              updates.stripe_price_id = stripePriceId;
              needsUpdate = true;
            }
          }

          // Update the database if needed
          if (needsUpdate) {
            await supabase.from('subscriptions').update(updates)
              .eq('stripe_subscription_id', subscription.stripe_subscription_id);

            // Update our local copy
            Object.assign(subscription, updates);
          }
        }

        // Determine the subscription type based on the price ID - REGARDLESS of status
        // This ensures consistent plan type throughout the system
        let subscription_type = PLAN_TYPES.BASIC;
        if (subscription.stripe_price_id) {
          // IMPORTANT: Consider any subscription with a valid price ID as active,
          // even if its status is 'incomplete' or 'past_due'
          const validStatuses = ['active', 'trialing', 'incomplete', 'past_due'];

          // Only use the subscription price ID if the status is valid
          // or if it's a new incomplete subscription
          if (validStatuses.includes(subscription.status)) {
            subscription_type = PRICE_ID_TO_PLAN[subscription.stripe_price_id] || PLAN_TYPES.BASIC;
          }
        }

        // Make sure the user's profile has the correct subscription type - ALWAYS update
        const { data: profile } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        // If the profile's subscription_type doesn't match, update it
        if (profile && profile.subscription_type !== subscription_type) {
          await supabase
            .from('profiles')
            .update({ subscription_type })
            .eq('id', user.id);

          // Also update user tokens to match the plan and trial status
          const isTrialing = subscription.status === 'trialing';
          await updateUserTokens(user.id, subscription_type, isTrialing);
        }

        return new Response(JSON.stringify({
          subscription: {
            ...subscription,
            stripe_subscription: stripeSubscription,
            subscription_type
          },
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      case 'cancel-subscription': {
        // Get the user's current subscription
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (!subscription) {
          return new Response(JSON.stringify({ error: 'No active subscription found' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Cancel the subscription at period end
        const canceledSubscription = await stripe.subscriptions.update(
          subscription.stripe_subscription_id,
          { cancel_at_period_end: true }
        )

        // Update the subscription in the database
        await supabase.from('subscriptions').update({
          cancel_at_period_end: true
        }).eq('stripe_subscription_id', subscription.stripe_subscription_id)

        // Check if this is a trial cancellation
        const isTrialCancellation = subscription.status === 'trialing';

        if (isTrialCancellation) {
          // Immediately revoke access for trial cancellations
          await revokeUserAccess(user.id);
        }

        // Trigger a sync with Stripe to ensure our database is up-to-date
        try {
          await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/sync-stripe-subscriptions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
            },
            body: JSON.stringify({ trialing_only: true })
          });
        } catch (syncError) {
          // Log but don't fail if sync fails
          console.error('Error syncing with Stripe:', syncError);
        }

        return new Response(JSON.stringify({
          subscription: canceledSubscription,
          accessRevoked: isTrialCancellation
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      case 'handle-checkout-redirect': {
        const { sessionId, customerId } = data;

        if (!sessionId) {
          return new Response(JSON.stringify({ error: 'No session ID provided' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        try {
          // Retrieve checkout session
          const session = await stripe.checkout.sessions.retrieve(sessionId, {
            expand: ['customer', 'subscription']
          });

          // Make sure we have a user_id associated with this customer
          let userId = user.id; // Default to the authenticated user

          // Use the provided customerId if available, otherwise use the one from the session
          const customerIdToUse = customerId || (typeof session.customer === 'string' ? session.customer : session.customer?.id);

          if (customerIdToUse) {
            // Check if customer metadata has user_id
            const customer = await stripe.customers.retrieve(customerIdToUse);

            // If customer doesn't have metadata.user_id, update it
            if (!customer.metadata?.user_id) {
              await stripe.customers.update(customer.id, {
                metadata: {
                  ...customer.metadata,
                  user_id: userId
                }
              });
            }

            // Make sure we have a customer record in database
            const { data: existingCustomer } = await supabase
              .from('customers')
              .select('*')
              .eq('stripe_customer_id', customer.id)
              .single();

            if (!existingCustomer) {
              await supabase
                .from('customers')
                .insert({
                  user_id: userId,
                  stripe_customer_id: customer.id,
                  created_at: new Date().toISOString()
                });
            }

            // If there's a subscription in the session, store it
            if (session.subscription) {
              const subscription = typeof session.subscription === 'string'
                ? await stripe.subscriptions.retrieve(session.subscription)
                : session.subscription;

              // Determine the plan type from the price ID
              const items = subscription.items.data;
              if (items && items.length > 0) {
                const priceId = items[0].price.id;
                const planType = PRICE_ID_TO_PLAN[priceId] || PLAN_TYPES.BASIC;

                // Store subscription in database
                await supabase.from('subscriptions').upsert({
                  user_id: userId,
                  stripe_subscription_id: subscription.id,
                  stripe_customer_id: customer.id,
                  stripe_price_id: priceId,
                  status: subscription.status,
                  current_period_end: subscription.current_period_end,
                  cancel_at_period_end: subscription.cancel_at_period_end,
                });

                // Update profile with new subscription type REGARDLESS of status
                await supabase
                  .from('profiles')
                  .update({ subscription_type: planType })
                  .eq('id', userId);

                // Update user tokens based on plan and trial status
                const isTrialing = subscription.status === 'trialing';
                await updateUserTokens(userId, planType, isTrialing);
              }
            }
          }

          return new Response(JSON.stringify({
            session: {
              id: session.id,
              customer: typeof session.customer === 'string' ? session.customer : session.customer?.id,
              subscription: typeof session.subscription === 'string' ? session.subscription : session.subscription?.id,
              status: session.status
            }
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
