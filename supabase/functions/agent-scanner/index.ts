import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { SP500_STOCKS } from "../aura/data/sp500-stocks.ts"
import { RUSSELL2000_STOCKS } from "../aura/data/russell2000-stocks.ts"
import { NASDAQ_STOCKS } from "../aura/data/nasdaq-stocks.ts"
import { NASDAQ100_STOCKS } from "../aura/data/nasdaq100-stocks.ts"
import { ALL_STOCKS } from "../aura/data/all-stocks.ts"
import { executeAgent } from "../agent-runner/agent-executor.ts"
import { fetchHistoricalData } from "../aura/technical-analysis.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Generate mock historical data for testing when API is unavailable
function generateMockHistoricalData(symbol: string) {
  const data = [];
  const basePrice = 100 + (symbol.charCodeAt(0) % 50); // Different base price per symbol
  const days = 100;

  for (let i = 0; i < days; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (days - i));

    // Generate realistic price movement
    const randomChange = (Math.random() - 0.5) * 0.1; // ±5% daily change
    const price = basePrice * (1 + randomChange * i / days);
    const volume = Math.floor(Math.random() * 1000000) + 100000;

    data.push({
      date: date.toISOString().split('T')[0],
      open: price * (1 + (Math.random() - 0.5) * 0.02),
      high: price * (1 + Math.random() * 0.03),
      low: price * (1 - Math.random() * 0.03),
      close: price,
      volume: volume
    });
  }

  return data;
}

interface ScanRequest {
  agentId: string;
  marketIndex: string;
  userId: string;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { agentId, marketIndex, userId }: ScanRequest = await req.json();

    // Validate input
    if (!agentId || !marketIndex || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get the agent configuration
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .eq('user_id', userId)
      .single();

    if (agentError || !agent) {
      return new Response(
        JSON.stringify({ error: 'Agent not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`Agent found: ${agent.name}`);
    console.log(`Agent configuration:`, {
      hasConfiguration: !!agent.configuration,
      blockCount: agent.configuration?.blocks?.length || 0,
      entryBlockId: agent.configuration?.entryBlockId,
      blockTypes: agent.configuration?.blocks?.map(b => b.type) || []
    });

    // Get stock list based on market index
    let stockList: string[] = [];
    switch (marketIndex) {
      case 'sp500':
        stockList = SP500_STOCKS;
        break;
      case 'nasdaq':
        stockList = NASDAQ_STOCKS;
        break;
      case 'nasdaq100':
        stockList = NASDAQ100_STOCKS;
        break;
      case 'russell2000':
        stockList = RUSSELL2000_STOCKS;
        break;
      case 'all':
        stockList = ALL_STOCKS;
        break;
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid market index' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
    }

    console.log(`Scanning ${stockList.length} stocks with agent: ${agent.name}`);

    // Get Polygon API key for efficient bulk scanning
    const polygonApiKey = Deno.env.get('POLYGON_API_KEY') ?? '';

    console.log(`Using ${polygonApiKey ? 'live' : 'mock'} data for scanning`);

    // Calculate date range for historical data
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // First, test the agent with a single stock to see if it works at all
    console.log(`Testing agent with AAPL first...`);
    try {
      let testDataResponse;
      try {
        testDataResponse = await fetchHistoricalData('AAPL', startDate, endDate, polygonApiKey);
      } catch (error) {
        console.warn(`Error fetching AAPL data, using mock data:`, error.message);
        testDataResponse = { symbol: 'AAPL', data: generateMockHistoricalData('AAPL') };
      }

      const testData = testDataResponse.data;
      if (testData && testData.length > 0) {
        // Create polygon data structure
        const polygonData = {
          price: {
            current: testData[testData.length - 1].close,
            open: testData[testData.length - 1].open,
            high: testData[testData.length - 1].high,
            low: testData[testData.length - 1].low,
            close: testData[testData.length - 1].close,
            volume: testData[testData.length - 1].volume,
            timestamp: testData[testData.length - 1].timestamp
          },
          historical: {
            open: testData.map(d => d.open),
            high: testData.map(d => d.high),
            low: testData.map(d => d.low),
            close: testData.map(d => d.close),
            volume: testData.map(d => d.volume),
            timestamp: testData.map(d => d.timestamp)
          },
          indicators: {},
          fundamentals: {}
        };

        const testResult = executeAgent(agent.configuration, polygonData);
        console.log(`Test result for AAPL:`, {
          signal: testResult?.signal,
          confidence: testResult?.confidence,
          hasResult: !!testResult
        });
      }
    } catch (error) {
      console.error(`Test failed for AAPL:`, error);
    }

    const results: ScanResult[] = [];
    const batchSize = 10; // Process stocks in efficient batches
    let totalProcessed = 0;
    let totalBullish = 0;
    let totalBearish = 0;
    let totalNeutral = 0;
    let totalErrors = 0;

    // Process all stocks in the selected market index
    const scanLimit = stockList.length;
    console.log(`Processing ${scanLimit} stocks in batches of ${batchSize}`);

    // Process stocks in batches
    for (let i = 0; i < scanLimit; i += batchSize) {
      const batch = stockList.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}: ${batch.join(', ')}`);

      const batchPromises = batch.map(async (symbol) => {
        try {
          totalProcessed++;

          // Fetch historical data efficiently with fallback to mock data
          let historicalData;
          try {
            const dataResponse = await fetchHistoricalData(symbol, startDate, endDate, polygonApiKey);
            historicalData = dataResponse.data;
          } catch (error) {
            console.warn(`Error fetching data for ${symbol}, using mock data:`, error.message);
            // Generate mock data as fallback
            historicalData = generateMockHistoricalData(symbol);
          }

          if (!historicalData || historicalData.length === 0) {
            console.warn(`No historical data for ${symbol}`);
            return null;
          }

          // Create polygon data structure from historical data
          const latestData = historicalData[historicalData.length - 1];
          const polygonData = {
            price: {
              current: latestData.close,
              open: latestData.open,
              high: latestData.high,
              low: latestData.low,
              close: latestData.close,
              volume: latestData.volume,
              timestamp: new Date(latestData.date).getTime()
            },
            historical: {
              open: historicalData.map(d => d.open),
              high: historicalData.map(d => d.high),
              low: historicalData.map(d => d.low),
              close: historicalData.map(d => d.close),
              volume: historicalData.map(d => d.volume),
              timestamp: historicalData.map(d => new Date(d.date).getTime())
            },
            indicators: {},
            fundamentals: {}
          };

          // Calculate required indicators
          const { calculateIndicators } = await import("../agent-runner/indicators.ts");

          // Determine which indicators are needed
          const requiredIndicators = new Set<string>();
          for (const block of agent.configuration.blocks) {
            if (block.type === 'INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'MOMENTUM_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'TREND_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'VOLUME_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'VOLATILITY_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            }
          }

          // Calculate indicators if needed
          if (requiredIndicators.size > 0) {
            for (const indicator of requiredIndicators) {
              if (indicator === 'support' || indicator === 'resistance' || indicator === 'candle_pattern' || indicator === 'rsi' || indicator === 'macd' || indicator === 'vwap') {
                // These are calculated in the executor
                polygonData.indicators[indicator] = 'calculated_in_executor';
              } else {
                try {
                  const calculatedIndicators = calculateIndicators(polygonData.historical, [indicator]);
                  Object.assign(polygonData.indicators, calculatedIndicators);
                } catch (error) {
                  console.error(`Error calculating ${indicator} for ${symbol}:`, error);
                  polygonData.indicators[indicator] = [];
                }
              }
            }
          }

          // Execute the agent directly (much faster than edge function calls)
          const agentResult = executeAgent(agent.configuration, polygonData);

          // Count all signals for debugging
          if (agentResult?.signal === 'bullish') {
            totalBullish++;
          } else if (agentResult?.signal === 'bearish') {
            totalBearish++;
          } else if (agentResult?.signal === 'neutral') {
            totalNeutral++;
          }

          console.log(`${symbol}: ${agentResult.signal} (${agentResult.confidence}%)`);

          if (agentResult && agentResult.signal === 'bullish' && agentResult.confidence > 50) {
            const previousData = historicalData[historicalData.length - 2];
            const change = previousData ? latestData.close - previousData.close : 0;
            const percentChange = previousData ? ((latestData.close - previousData.close) / previousData.close) * 100 : 0;

            return {
              symbol: symbol,
              signal: agentResult.signal,
              confidence: agentResult.confidence,
              price: latestData.close,
              change: change,
              percentChange: percentChange
            };
          }

          return null;
        } catch (error) {
          totalErrors++;
          console.error(`Error processing ${symbol}:`, error);
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      const validResults = batchResults.filter(result => result !== null) as ScanResult[];
      results.push(...validResults);

      // Add a small delay between batches to avoid overwhelming the API
      if (i + batchSize < scanLimit) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Sort results by confidence (highest first)
    results.sort((a, b) => b.confidence - a.confidence);

    // Return all results (no artificial limit)
    const limitedResults = results;

    console.log(`Scanning Summary:
      - Total Processed: ${totalProcessed}
      - Total Bullish: ${totalBullish}
      - Total Bearish: ${totalBearish}
      - Total Neutral: ${totalNeutral}
      - Total Errors: ${totalErrors}
      - Final Results: ${limitedResults.length}
    `);

    return new Response(
      JSON.stringify({
        results: limitedResults,
        totalScanned: totalProcessed,
        marketIndex: marketIndex,
        agentName: agent.name
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Agent scanner error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
