// Discover Agents Edge Function
// This function handles agent discovery with search and filtering

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

interface DiscoverAgentsRequest {
  search?: string;
  category?: string;
  tags?: string[];
  sortBy?: 'newest' | 'popular' | 'rating' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  featured?: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse request parameters
    let requestData: DiscoverAgentsRequest = {};
    
    if (req.method === 'GET') {
      const url = new URL(req.url);
      requestData = {
        search: url.searchParams.get('search') || undefined,
        category: url.searchParams.get('category') || undefined,
        tags: url.searchParams.get('tags')?.split(',') || undefined,
        sortBy: (url.searchParams.get('sortBy') as any) || 'newest',
        sortOrder: (url.searchParams.get('sortOrder') as any) || 'desc',
        limit: parseInt(url.searchParams.get('limit') || '20'),
        offset: parseInt(url.searchParams.get('offset') || '0'),
        featured: url.searchParams.get('featured') === 'true'
      };
    } else if (req.method === 'POST') {
      requestData = await req.json();
    }

    console.log('Discovering agents with filters:', requestData);

    // Build the query - simplified without joins for now
    let query = supabase
      .from('published_agents')
      .select('*')
      .eq('is_active', true);

    // Apply filters
    if (requestData.search) {
      query = query.or(`name.ilike.%${requestData.search}%,description.ilike.%${requestData.search}%`);
    }

    if (requestData.category) {
      query = query.eq('category', requestData.category);
    }

    if (requestData.tags && requestData.tags.length > 0) {
      query = query.overlaps('tags', requestData.tags);
    }

    if (requestData.featured) {
      query = query.eq('is_featured', true);
    }

    // Apply sorting
    switch (requestData.sortBy) {
      case 'popular':
        query = query.order('download_count', { ascending: requestData.sortOrder === 'asc' });
        break;
      case 'rating':
        query = query.order('average_rating', { ascending: requestData.sortOrder === 'asc' });
        break;
      case 'downloads':
        query = query.order('download_count', { ascending: requestData.sortOrder === 'asc' });
        break;
      case 'newest':
      default:
        query = query.order('created_at', { ascending: requestData.sortOrder === 'asc' });
        break;
    }

    // Apply pagination
    const limit = Math.min(requestData.limit || 20, 100); // Max 100 items per request
    const offset = requestData.offset || 0;
    query = query.range(offset, offset + limit - 1);

    // Execute query
    const { data: agents, error: agentsError } = await query;

    if (agentsError) {
      throw new Error(`Failed to fetch agents: ${agentsError.message}`);
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('published_agents')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    if (countError) {
      console.warn('Failed to get total count:', countError.message);
    }

    // Get categories for filtering
    const { data: categories, error: categoriesError } = await supabase
      .from('agent_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (categoriesError) {
      console.warn('Failed to fetch categories:', categoriesError.message);
    }

    // Format response
    const formattedAgents = agents?.map(agent => ({
      id: agent.id,
      agentId: agent.agent_id,
      name: agent.name,
      description: agent.description,
      category: agent.category,
      tags: agent.tags,
      publisherName: 'Anonymous', // Will be populated later when we fix the joins
      publisherId: agent.publisher_id,
      downloadCount: agent.download_count,
      averageRating: parseFloat(agent.average_rating) || 0,
      totalReviews: agent.total_reviews,
      isFeatured: agent.is_featured,
      createdAt: agent.created_at,
      updatedAt: agent.updated_at,
      configuration: null // Will be populated later when we fix the joins
    })) || [];

    console.log(`Found ${formattedAgents.length} agents`);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        agents: formattedAgents,
        categories: categories || [],
        pagination: {
          total: count || 0,
          limit: limit,
          offset: offset,
          hasMore: (count || 0) > offset + limit
        }
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error in discover-agents function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
