// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Find all users with trialing status and cancel_at_period_end = true
    const { data: canceledTrials, error: queryError } = await supabase
      .from('subscriptions')
      .select('user_id')
      .eq('status', 'trialing')
      .eq('cancel_at_period_end', true);

    if (queryError) {
      throw new Error(`Failed to query canceled trials: ${queryError.message}`);
    }

    if (!canceledTrials || canceledTrials.length === 0) {
      return new Response(JSON.stringify({ 
        message: 'No canceled trials found that need fixing' 
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Process each canceled trial
    const results = [];
    for (const subscription of canceledTrials) {
      try {
        // 1. Update profile to remove subscription type
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            subscription_type: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', subscription.user_id);

        // 2. Set user tokens to 0
        const { error: tokensError } = await supabase
          .from('user_tokens')
          .update({
            tokens_remaining: 0,
            last_reset: new Date().toISOString(),
            last_reset_date: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', subscription.user_id);

        results.push({
          user_id: subscription.user_id,
          status: 'access_revoked',
          profile_update: profileError ? 'error' : 'success',
          tokens_update: tokensError ? 'error' : 'success'
        });
      } catch (error) {
        results.push({
          user_id: subscription.user_id,
          status: 'error',
          message: error.message
        });
      }
    }

    return new Response(JSON.stringify({ 
      processed: results.length,
      results 
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
