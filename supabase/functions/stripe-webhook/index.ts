// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Constants for plans and limits
const PLAN_TYPES = {
  BASIC: 'basic',  // The basic weekly plan - $5.99/week
  PRO: 'pro',      // The pro weekly plan - $9.99/week
};

// Message limits by plan
const MESSAGE_LIMITS = {
  [PLAN_TYPES.BASIC]: 100, // 100 messages for basic plan
  [PLAN_TYPES.PRO]: 200    // 200 messages for pro plan
};

// Free trial message limits (3 messages per day)
const TRIAL_MESSAGE_LIMITS = {
  [PLAN_TYPES.BASIC]: 3,
  [PLAN_TYPES.PRO]: 3
};

// Price ID to plan mapping
const PRICE_ID_TO_PLAN = {
  // Basic Plan - $5.99/week
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.BASIC,
  // Pro Plan - $9.99/week
  'price_1ROYKjDebmd1GpTv5oYNMKMv': PLAN_TYPES.PRO,
};

// Initialize Stripe
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// Initialize crypto provider for Stripe
const cryptoProvider = Stripe.createSubtleCryptoProvider();

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET') || '';
const BYPASS_SIGNATURE_VERIFICATION = true;

// Update user tokens based on subscription plan
const updateUserTokens = async (userId: string, planType: string, isTrialing: boolean = false, revokeAccess: boolean = false) => {
  try {
    // Normalize plan type to lowercase for consistent handling
    const normalizedPlanType = planType.toLowerCase();
    if (!Object.values(PLAN_TYPES).includes(normalizedPlanType)) {
      throw new Error(`Invalid plan type: ${planType}`);
    }

    // Determine message limit based on subscription status
    let messageLimit;

    if (revokeAccess) {
      // If access is being revoked, set message limit to 0
      messageLimit = 0;
    } else if (isTrialing) {
      // Trial users get 3 messages per day for 3 days
      messageLimit = TRIAL_MESSAGE_LIMITS[normalizedPlanType];
      if (messageLimit === undefined) {
        messageLimit = TRIAL_MESSAGE_LIMITS[PLAN_TYPES.BASIC]; // Default to basic trial limit
      }
    } else {
      // Paid users get their full message allowance immediately
      messageLimit = MESSAGE_LIMITS[normalizedPlanType];
      if (messageLimit === undefined) {
        throw new Error(`No message limit defined for plan type: ${planType}`);
      }
    }

    // Check if user has a tokens record
    const { data: existingTokens, error: tokensError } = await supabase
      .from('user_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    const tokenData = {
      tokens_remaining: messageLimit,
      last_reset: new Date().toISOString(),
      last_reset_date: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    if (!existingTokens || (tokensError && tokensError.code === 'PGRST116')) {
      // Create new tokens record
      const { error: insertError } = await supabase
        .from('user_tokens')
        .insert({
          user_id: userId,
          ...tokenData
        });

      if (insertError) {
        // If insert fails, try upsert as fallback
        const { error: upsertError } = await supabase
          .from('user_tokens')
          .upsert({
            user_id: userId,
            ...tokenData
          });

        if (upsertError) {
          throw new Error(`Failed to create tokens record: ${upsertError.message}`);
        }
      }
    } else {
      // Update existing tokens
      const { error: updateError } = await supabase
        .from('user_tokens')
        .update(tokenData)
        .eq('user_id', userId);

      if (updateError) {
        throw new Error(`Failed to update tokens: ${updateError.message}`);
      }
    }

    // Verify the update was successful
    const { data: verifyTokens, error: verifyError } = await supabase
      .from('user_tokens')
      .select('tokens_remaining')
      .eq('user_id', userId)
      .single();

    if (verifyError || !verifyTokens || verifyTokens.tokens_remaining !== messageLimit) {
      throw new Error('Token update verification failed');
    }

    return true;
  } catch (err) {
    throw err;
  }
};

// Get user ID from customer ID, with fallback methods
const getUserIdFromCustomer = async (customerId: string): Promise<string | null> => {
  // First try to get from our database
  const { data: customerData } = await supabase
    .from('customers')
    .select('user_id')
    .eq('stripe_customer_id', customerId)
    .single();

  if (customerData?.user_id) {
    return customerData.user_id;
  }

  // Try to get from Stripe customer metadata
  try {
    const stripeCustomer = await stripe.customers.retrieve(customerId);
    const userId = stripeCustomer.metadata?.user_id || stripeCustomer.metadata?.supabase_user_id;

    if (userId) {
      // Store the customer-user association for future
      await supabase
        .from('customers')
        .insert({
          user_id: userId,
          stripe_customer_id: customerId,
          created_at: new Date().toISOString()
        });
      return userId;
    }

    // If we can't find the user by metadata, try to find by email
    if (stripeCustomer.email) {
      // Find user with this email in auth.users
      const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

      if (!userError && userData?.users) {
        // Find the user with matching email
        const matchedUser = userData.users.find(u => u.email?.toLowerCase() === stripeCustomer.email?.toLowerCase());

        if (matchedUser) {
          // Store this association for future lookups
          await supabase
            .from('customers')
            .insert({
              user_id: matchedUser.id,
              stripe_customer_id: customerId,
              created_at: new Date().toISOString()
            });

          // Also update the Stripe customer metadata for future
          await stripe.customers.update(customerId, {
            metadata: {
              user_id: matchedUser.id
            }
          });

          return matchedUser.id;
        }
      }
    }
  } catch (error) {
    // Error handling without console.error
  }

  // Last resort - check if there's a payment_link in the session and try to find any user
  // with a recent login that might match this customer

  try {
    // Get the most recent active user as a fallback
    const { data: recentUsers } = await supabase
      .from('profiles')
      .select('id')
      .order('updated_at', { ascending: false })
      .limit(10);

    if (recentUsers && recentUsers.length > 0) {
      const fallbackUserId = recentUsers[0].id;

      // Store this association for future lookups
      await supabase
        .from('customers')
        .insert({
          user_id: fallbackUserId,
          stripe_customer_id: customerId,
          created_at: new Date().toISOString()
        });

      // Update Stripe customer with this user ID
      await stripe.customers.update(customerId, {
        metadata: {
          user_id: fallbackUserId
        }
      });

      return fallbackUserId;
    }
  } catch (error) {
    // Error handling without console.error
  }

  return null;
};

// Function to revoke user access
const revokeUserAccess = async (userId: string) => {
  try {
    // 1. Update profile to remove subscription type
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        subscription_type: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      throw new Error(`Failed to update profile when revoking access: ${profileError.message}`);
    }

    // 2. Set user tokens to 0
    await updateUserTokens(userId, PLAN_TYPES.BASIC, false, true);

    return true;
  } catch (error) {
    throw error;
  }
};

// Function to update user's subscription
const updateSubscription = async (userId: string, subscription: any) => {
  try {
    // Get price ID and determine plan type
    const priceId = subscription.items.data[0].price.id;
    const planType = PRICE_ID_TO_PLAN[priceId];

    if (!planType) {
      throw new Error(`Invalid price ID: ${priceId}`);
    }

    // First check if subscription record exists
    const { data: existingSubscription, error: checkError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw new Error(`Failed to check existing subscription: ${checkError.message}`);
    }

    // Update or insert subscription record based on existence
    const subscriptionData = {
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer,
      stripe_price_id: priceId,
      status: subscription.status,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
    };

    // First update/insert subscription record
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'user_id' });

    if (subscriptionError) {
      throw new Error(`Failed to update subscription record: ${subscriptionError.message}`);
    }

    const validStatuses = ['active', 'trialing', 'incomplete', 'past_due'];

    if (validStatuses.includes(subscription.status)) {
      // Update profile with new subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          subscription_type: planType,
          has_seen_onboarding: true,
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Update user tokens
      try {
        const isTrialing = subscription.status === 'trialing';
        await updateUserTokens(userId, planType, isTrialing);
      } catch (tokenError) {
        throw new Error(`Failed to update user tokens: ${tokenError.message}`);
      }

      // Add delay before verification to allow for database consistency
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verify the update
      const { data: verifyProfile, error: verifyError } = await supabase
        .from('profiles')
        .select('subscription_type, has_seen_onboarding')
        .eq('id', userId)
        .single();

      if (verifyError) {
        throw new Error(`Profile verification query failed: ${verifyError.message}`);
      }

      if (!verifyProfile) {
        throw new Error('Profile verification failed: Profile not found after update');
      }

      if (verifyProfile.subscription_type !== planType || verifyProfile.has_seen_onboarding !== true) {
        // One more attempt to update the profile
        const { error: retryError } = await supabase
          .from('profiles')
          .update({
            subscription_type: planType,
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (retryError) {
          throw new Error(`Profile verification failed after retry: ${retryError.message}`);
        }

        // Final verification
        const { data: finalVerify } = await supabase
          .from('profiles')
          .select('subscription_type, has_seen_onboarding')
          .eq('id', userId)
          .single();

        if (!finalVerify || finalVerify.subscription_type !== planType || finalVerify.has_seen_onboarding !== true) {
          throw new Error(`Profile verification failed after retry`);
        }
      }

      return planType;
    } else {
      // Invalid status, removing subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ subscription_type: null })
        .eq('id', userId);

      if (profileError) {
        throw new Error(`Failed to update profile to null: ${profileError.message}`);
      }

      return null;
    }
  } catch (error) {
    throw error;
  }
};

// Function to fix any existing canceled trials
const fixCanceledTrials = async () => {
  try {
    // Find all users with trialing status and cancel_at_period_end = true
    const { data: canceledTrials, error: queryError } = await supabase
      .from('subscriptions')
      .select('user_id')
      .eq('status', 'trialing')
      .eq('cancel_at_period_end', true);

    if (queryError) {
      throw new Error(`Failed to query canceled trials: ${queryError.message}`);
    }

    if (!canceledTrials || canceledTrials.length === 0) {
      return { processed: 0, results: [] };
    }

    // Process each canceled trial
    const results = [];
    for (const subscription of canceledTrials) {
      try {
        await revokeUserAccess(subscription.user_id);
        results.push({
          user_id: subscription.user_id,
          status: 'access_revoked'
        });
      } catch (error) {
        results.push({
          user_id: subscription.user_id,
          status: 'error',
          message: error.message
        });
      }
    }

    return { processed: results.length, results };
  } catch (error) {
    throw error;
  }
};

serve(async (req) => {
  // CORS headers for cross-origin requests
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Methods': '*'
  };

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await req.text();

    let event;

    // Parse and verify the event
    if (!BYPASS_SIGNATURE_VERIFICATION) {
      const signature = req.headers.get('stripe-signature');
      if (!signature) {
        return new Response(JSON.stringify({ error: 'No signature provided' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      try {
        event = await stripe.webhooks.constructEventAsync(
          body,
          signature,
          webhookSecret,
          undefined,
          cryptoProvider
        );
      } catch (err) {
        return new Response(JSON.stringify({ error: `Webhook signature verification failed` }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    } else {
      // BYPASS: Parse the event directly
      event = JSON.parse(body);
    }

    // First, fix any existing canceled trials
    try {
      await fixCanceledTrials();
    } catch (error) {
      // Log but continue processing the webhook
      console.error('Error fixing canceled trials:', error);
    }

    // Handle specific events
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        const customerId = session.customer as string;

        if (!customerId) {
          break;
        }

        // Try to get user ID from various sources
        let userId = session.metadata?.user_id || session.client_reference_id;

        // If we still don't have a user ID, try our customer lookup
        if (!userId) {
          userId = await getUserIdFromCustomer(customerId);

          if (!userId) {
            break;
          }
        }

        // Store customer info if not already stored
        await supabase
          .from('customers')
          .upsert({
            user_id: userId,
            stripe_customer_id: customerId,
            created_at: new Date().toISOString()
          });

        // If there's a subscription ID in the session, retrieve and store it
        if (session.subscription) {
          try {
            const subscription = await stripe.subscriptions.retrieve(session.subscription);
            await updateSubscription(userId, subscription);
          } catch (error) {
            throw error;
          }
        } else {
          // For one-time payments, still mark has_seen_onboarding as true
          try {
            const { error } = await supabase
              .from('profiles')
              .upsert({
                id: userId,
                has_seen_onboarding: true,
                updated_at: new Date().toISOString()
              });

            if (error) {
              console.error('Error updating profile on checkout completion:', error);
            }
          } catch (error) {
            console.error('Error processing checkout completion:', error);
          }
        }

        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;

        if (!customerId) {
          break;
        }

        // Get user ID from customer ID
        const userId = await getUserIdFromCustomer(customerId);

        if (!userId) {
          break;
        }

        // Check if this is a trial cancellation
        const isTrialCancellation =
          subscription.status === 'trialing' &&
          subscription.cancel_at_period_end === true;

        if (isTrialCancellation) {
          // Immediately revoke access for trial cancellations
          await revokeUserAccess(userId);
        } else {
          // Normal subscription update
          await updateSubscription(userId, subscription);
        }

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;

        if (!customerId) {
          break;
        }

        // Get user ID from customer ID
        const userId = await getUserIdFromCustomer(customerId);

        if (!userId) {
          break;
        }

        // Immediately revoke access when subscription is deleted
        await revokeUserAccess(userId);

        break;
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});