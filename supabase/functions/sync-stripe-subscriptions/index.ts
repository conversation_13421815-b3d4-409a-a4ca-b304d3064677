// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Constants for plans and limits
const PLAN_TYPES = {
  BASIC: 'basic',  // The basic weekly plan - $5.99/week
  PRO: 'pro',      // The pro weekly plan - $9.99/week
};

// Initialize Stripe
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Function to revoke user access
const revokeUserAccess = async (userId: string) => {
  try {
    console.log(`Revoking access for user: ${userId}`);
    
    // 1. Update profile to remove subscription type
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        subscription_type: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      throw new Error(`Failed to update profile when revoking access: ${profileError.message}`);
    }

    // 2. Set user tokens to 0
    const { error: tokensError } = await supabase
      .from('user_tokens')
      .update({
        tokens_remaining: 0,
        last_reset: new Date().toISOString(),
        last_reset_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (tokensError && tokensError.code !== 'PGRST116') {
      throw new Error(`Failed to update tokens when revoking access: ${tokensError.message}`);
    }

    return true;
  } catch (error) {
    console.error(`Error revoking access for user ${userId}:`, error);
    throw error;
  }
};

// Function to sync a subscription with Stripe
const syncSubscriptionWithStripe = async (subscription: any) => {
  try {
    // Fetch the subscription from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(
      subscription.stripe_subscription_id
    );

    // Check if the subscription data needs to be updated
    const updates: Record<string, any> = {};
    let needsUpdate = false;

    // Update status if different
    if (stripeSubscription.status !== subscription.status) {
      updates.status = stripeSubscription.status;
      needsUpdate = true;
    }

    // Update cancel_at_period_end if different
    if (stripeSubscription.cancel_at_period_end !== subscription.cancel_at_period_end) {
      updates.cancel_at_period_end = stripeSubscription.cancel_at_period_end;
      needsUpdate = true;
    }

    // Update current_period_end if different
    if (stripeSubscription.current_period_end !== subscription.current_period_end) {
      updates.current_period_end = stripeSubscription.current_period_end;
      needsUpdate = true;
    }

    // Update cancel_at if different
    if (
      (stripeSubscription.cancel_at === null && subscription.cancel_at !== null) ||
      (stripeSubscription.cancel_at !== null && subscription.cancel_at === null) ||
      (stripeSubscription.cancel_at !== null && subscription.cancel_at !== null && 
       stripeSubscription.cancel_at !== subscription.cancel_at)
    ) {
      updates.cancel_at = stripeSubscription.cancel_at;
      needsUpdate = true;
    }

    // If updates are needed, update the subscription in Supabase
    if (needsUpdate) {
      const { error } = await supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', subscription.id);

      if (error) {
        throw new Error(`Failed to update subscription: ${error.message}`);
      }
    }

    // Check if this is a canceled trial
    const isCanceledTrial = 
      stripeSubscription.status === 'trialing' && 
      stripeSubscription.cancel_at_period_end === true;

    // If it's a canceled trial, revoke access
    if (isCanceledTrial) {
      await revokeUserAccess(subscription.user_id);
      return {
        subscription_id: subscription.stripe_subscription_id,
        user_id: subscription.user_id,
        status: 'access_revoked',
        updates: needsUpdate ? updates : 'none'
      };
    }

    return {
      subscription_id: subscription.stripe_subscription_id,
      user_id: subscription.user_id,
      status: needsUpdate ? 'updated' : 'no_changes',
      updates: needsUpdate ? updates : 'none'
    };
  } catch (error) {
    return {
      subscription_id: subscription.stripe_subscription_id,
      user_id: subscription.user_id,
      status: 'error',
      message: error.message
    };
  }
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse request body if any
    let body = {};
    try {
      body = await req.json();
    } catch (e) {
      // No body or invalid JSON, continue with default behavior
    }

    // Check if we should only process trialing subscriptions
    const trialingOnly = body.trialing_only === true;
    
    // Fetch subscriptions from Supabase
    const query = supabase
      .from('subscriptions')
      .select('*');
    
    // Filter by status if requested
    if (trialingOnly) {
      query.eq('status', 'trialing');
    }
    
    const { data: subscriptions, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch subscriptions: ${error.message}`);
    }

    if (!subscriptions || subscriptions.length === 0) {
      return new Response(JSON.stringify({ 
        message: 'No subscriptions found to sync' 
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Process each subscription
    const results = [];
    for (const subscription of subscriptions) {
      try {
        const result = await syncSubscriptionWithStripe(subscription);
        results.push(result);
      } catch (error) {
        results.push({
          subscription_id: subscription.stripe_subscription_id,
          user_id: subscription.user_id,
          status: 'error',
          message: error.message
        });
      }
    }

    return new Response(JSON.stringify({ 
      processed: results.length,
      results 
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
