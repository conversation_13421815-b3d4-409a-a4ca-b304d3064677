import type { NextApiRequest, NextApiResponse } from "next";
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2022-11-15",
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { priceId, couponId } = req.body;

    // Make sure priceId is provided
    if (!priceId) {
      return res.status(400).json({ message: "Missing priceId" });
    }

    // Create a Checkout Session
    const session = await stripe.checkout.sessions.create({
      mode: "subscription",
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      discounts: couponId
        ? [
            {
              coupon: couponId,
            },
          ]
        : [],
      success_url: `${process.env.NEXTAUTH_URL}/success`,
      cancel_url: `${process.env.NEXTAUTH_URL}/cancel`,
      // You can adjust other parameters as needed
    });

    return res.status(200).json({ url: session.url });
  } catch (error: any) {
    console.error("Checkout Session Error:", error);
    return res.status(500).json({ message: error.message });
  }
} 