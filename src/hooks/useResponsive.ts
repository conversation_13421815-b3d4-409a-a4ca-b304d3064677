import { useMobileDetection } from './useMobileDetection';

export const useResponsive = () => {
  const { isMobile, isTablet, isDesktop, screenWidth } = useMobileDetection();

  // Responsive classes for common UI patterns
  const getResponsiveClasses = () => ({
    // Container classes
    container: isMobile 
      ? 'px-4 py-4' 
      : isTablet 
      ? 'px-6 py-6' 
      : 'px-8 py-8',
    
    // Grid classes
    grid: isMobile 
      ? 'grid-cols-1' 
      : isTablet 
      ? 'grid-cols-2' 
      : 'grid-cols-3',
    
    // Text sizes
    title: isMobile 
      ? 'text-xl' 
      : isTablet 
      ? 'text-2xl' 
      : 'text-3xl',
    
    subtitle: isMobile 
      ? 'text-sm' 
      : isTablet 
      ? 'text-base' 
      : 'text-lg',
    
    // Spacing
    spacing: isMobile 
      ? 'space-y-4' 
      : isTablet 
      ? 'space-y-6' 
      : 'space-y-8',
    
    // Card padding
    cardPadding: isMobile 
      ? 'p-4' 
      : isTablet 
      ? 'p-5' 
      : 'p-6',
    
    // Button sizes
    button: isMobile 
      ? 'px-4 py-2.5 text-sm' 
      : isTablet 
      ? 'px-5 py-3 text-base' 
      : 'px-6 py-3 text-base',
    
    // Modal sizes
    modal: isMobile 
      ? 'max-w-sm mx-4' 
      : isTablet 
      ? 'max-w-md mx-6' 
      : 'max-w-lg mx-8',
    
    // Chart heights
    chartHeight: isMobile 
      ? 'h-48' 
      : isTablet 
      ? 'h-64' 
      : 'h-80',
    
    // Table responsive
    table: isMobile 
      ? 'text-xs' 
      : isTablet 
      ? 'text-sm' 
      : 'text-base',
  });

  // Responsive values for dynamic sizing
  const getResponsiveValue = <T>(mobile: T, tablet: T, desktop: T): T => {
    if (isMobile) return mobile;
    if (isTablet) return tablet;
    return desktop;
  };

  return {
    isMobile,
    isTablet,
    isDesktop,
    screenWidth,
    classes: getResponsiveClasses(),
    getValue: getResponsiveValue
  };
};
