import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

// Define locked pages for basic plan users
const LOCKED_PAGES_FOR_BASIC = [
  '/agent-builder',
  '/discover',
  '/stock-search',
  '/agent-scanner'
];

export const usePageLock = () => {
  const [subscriptionType, setSubscriptionType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch subscription type directly from profiles table
  useEffect(() => {
    const fetchSubscriptionType = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          setSubscriptionType('basic'); // Default to basic if no user
          setIsLoading(false);
          return;
        }

        const { data: profile, error } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching subscription type:', error);
          setSubscriptionType('basic'); // Default to basic on error
        } else {
          setSubscriptionType(profile?.subscription_type || 'basic');
        }
      } catch (error) {
        console.error('Error in fetchSubscriptionType:', error);
        setSubscriptionType('basic'); // Default to basic on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscriptionType();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      fetchSubscriptionType();
    });

    return () => subscription.unsubscribe();
  }, []);

  // Check if a specific page is locked for the current user
  const isPageLocked = (path: string): boolean => {
    if (isLoading) return false; // Don't lock while loading

    // When subscription_type is 'basic', lock the pages
    // When subscription_type is 'pro' or 'premium', unlock them
    if (subscriptionType === 'pro' || subscriptionType === 'premium') return false;
    if (subscriptionType !== 'basic') return false;

    // Check if the path is in the locked pages list
    return LOCKED_PAGES_FOR_BASIC.some(lockedPath =>
      path === lockedPath || path.startsWith(lockedPath + '/')
    );
  };

  // Check if current user is on basic plan
  const isBasicPlan = (): boolean => {
    return subscriptionType === 'basic';
  };

  // Get list of locked pages for current user
  const getLockedPages = (): string[] => {
    if (subscriptionType !== 'basic') return [];
    return LOCKED_PAGES_FOR_BASIC;
  };

  // Function to refresh subscription type (useful after plan changes)
  const refreshSubscriptionType = async () => {
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        setSubscriptionType('basic');
        return;
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('subscription_type')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error refreshing subscription type:', error);
        setSubscriptionType('basic');
      } else {
        setSubscriptionType(profile?.subscription_type || 'basic');
      }
    } catch (error) {
      console.error('Error in refreshSubscriptionType:', error);
      setSubscriptionType('basic');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isPageLocked,
    isBasicPlan,
    getLockedPages,
    refreshSubscriptionType,
    planType: subscriptionType,
    isLoading
  };
};
