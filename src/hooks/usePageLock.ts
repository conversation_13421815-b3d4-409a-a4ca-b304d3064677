import { useUserLimits, PLAN_TYPES, LOCKED_PAGES_FOR_BASIC } from './useUserLimits';

export const usePageLock = () => {
  const { planType, isLoading } = useUserLimits();

  // Check if a specific page is locked for the current user
  const isPageLocked = (path: string): boolean => {
    if (isLoading) return false; // Don't lock while loading
    
    // Only basic plan users have locked pages
    if (planType !== PLAN_TYPES.basic) return false;
    
    // Check if the path is in the locked pages list
    return LOCKED_PAGES_FOR_BASIC.some(lockedPath => 
      path === lockedPath || path.startsWith(lockedPath + '/')
    );
  };

  // Check if current user is on basic plan
  const isBasicPlan = (): boolean => {
    return planType === PLAN_TYPES.basic;
  };

  // Get list of locked pages for current user
  const getLockedPages = (): string[] => {
    if (planType !== PLAN_TYPES.basic) return [];
    return LOCKED_PAGES_FOR_BASIC;
  };

  return {
    isPageLocked,
    isBasicPlan,
    getLockedPages,
    planType,
    isLoading
  };
};
