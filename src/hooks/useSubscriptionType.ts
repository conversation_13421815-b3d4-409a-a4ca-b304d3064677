import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useSubscriptionType = () => {
  const [subscriptionType, setSubscriptionType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch subscription type directly from profiles table
  useEffect(() => {
    const fetchSubscriptionType = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setSubscriptionType('basic'); // Default to basic if no user
          setIsLoading(false);
          return;
        }

        const { data: profile, error } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching subscription type:', error);
          setSubscriptionType('basic'); // Default to basic on error
        } else {
          setSubscriptionType(profile?.subscription_type || 'basic');
        }
      } catch (error) {
        console.error('Error in fetchSubscriptionType:', error);
        setSubscriptionType('basic'); // Default to basic on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscriptionType();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      fetchSubscriptionType();
    });

    return () => subscription.unsubscribe();
  }, []);

  // Function to refresh subscription type (useful after plan changes)
  const refreshSubscriptionType = async () => {
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setSubscriptionType('basic');
        return;
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('subscription_type')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error refreshing subscription type:', error);
        setSubscriptionType('basic');
      } else {
        setSubscriptionType(profile?.subscription_type || 'basic');
      }
    } catch (error) {
      console.error('Error in refreshSubscriptionType:', error);
      setSubscriptionType('basic');
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user can upgrade (only basic users can upgrade)
  const canUpgrade = (): boolean => {
    return subscriptionType === 'basic';
  };

  // Check if user is on basic plan
  const isBasicPlan = (): boolean => {
    return subscriptionType === 'basic';
  };

  // Check if user is on pro plan
  const isProPlan = (): boolean => {
    return subscriptionType === 'pro';
  };

  // Check if user is on premium plan
  const isPremiumPlan = (): boolean => {
    return subscriptionType === 'premium';
  };

  return {
    subscriptionType,
    isLoading,
    refreshSubscriptionType,
    canUpgrade,
    isBasicPlan,
    isProPlan,
    isPremiumPlan
  };
};
