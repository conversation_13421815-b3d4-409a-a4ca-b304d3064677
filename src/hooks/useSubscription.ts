import { useQuery, useMutation } from '@tanstack/react-query'
import { supabase } from '@/integrations/supabase/client'
import { useEffect, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query';

export const SUBSCRIPTION_TYPES = {
  basic: 'basic',
  pro: 'pro',
  premium: 'premium'
} as const;

export const PRICE_IDS = {
  BASIC: 'price_1R2FYoDebmd1GpTvc8N1ctlW',
  PRO: 'price_1R2FYuDebmd1GpTv1kJLnn8u',
  PREMIUM: 'price_1R2FYvDebmd1GpTv3kJLnn8v'
};

const priceToType = {
  // Basic Plans
  'price_1R6LbqDebmd1GpTvkP4EXccx': SUBSCRIPTION_TYPES.basic,
  'price_1R6LivDebmd1GpTvMMAnjTFo': SUBSCRIPTION_TYPES.basic,
  'price_1R6LcLDebmd1GpTv1orxNWsv': SUBSCRIPTION_TYPES.basic,
  'price_1R6LkMDebmd1GpTvzpfm5qwJ': SUBSCRIPTION_TYPES.basic,
  'price_1R6LclDebmd1GpTvAIesiTY0': SUBSCRIPTION_TYPES.basic,
  'price_1R6LkXDebmd1GpTvOz2OEbUh': SUBSCRIPTION_TYPES.basic,

  // Pro Plans
  'price_1R6LdFDebmd1GpTvURHjz9F0': SUBSCRIPTION_TYPES.pro,
  'price_1R6LknDebmd1GpTvDnBj2Em1': SUBSCRIPTION_TYPES.pro,
  'price_1R6LdZDebmd1GpTvb0gRgBEV': SUBSCRIPTION_TYPES.pro,
  'price_1R6LlQDebmd1GpTvum6KZMSn': SUBSCRIPTION_TYPES.pro,
  'price_1R6LeNDebmd1GpTvs5Q8ps55': SUBSCRIPTION_TYPES.pro,
  'price_1R6LmSDebmd1GpTvvurNQOYM': SUBSCRIPTION_TYPES.pro,

  // Premium Plans
  'price_1R6LekDebmd1GpTvaH38JNpc': SUBSCRIPTION_TYPES.premium,
  'price_1R6LmsDebmd1GpTvnTJli7Qm': SUBSCRIPTION_TYPES.premium,
  'price_1R6LfCDebmd1GpTvnRMTfHyR': SUBSCRIPTION_TYPES.premium,
  'price_1R6LnVDebmd1GpTvVObrSaaQ': SUBSCRIPTION_TYPES.premium,
  'price_1R6LfQDebmd1GpTvJGpE8rIX': SUBSCRIPTION_TYPES.premium,
  'price_1R6LnjDebmd1GpTvckCrIH71': SUBSCRIPTION_TYPES.premium
};

let subscriptionType = SUBSCRIPTION_TYPES.basic;

export const useSubscription = () => {
  const [session, setSession] = useState<any>(null)
  const queryClient = useQueryClient();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
    })

    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      setSession(session)
    })

    return () => {
      authListener?.subscription.unsubscribe()
    }
  }, [])

  const { data: subscription, isLoading: isLoadingSubscription, refetch } = useQuery({
    queryKey: ['subscription'],
    queryFn: async () => {
      if (!session?.user?.id) {
        return null;
      }

      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', session.user.id)
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        throw subscriptionError;
      }

      if (subscriptionData) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', session.user.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          throw profileError;
        }

        return {
          ...subscriptionData,
          subscription_type: profileData?.subscription_type || null
        };
      }

      const { data: profileOnlyData, error: profileOnlyError } = await supabase
        .from('profiles')
        .select('subscription_type')
        .eq('id', session.user.id)
        .single();

      if (profileOnlyError && profileOnlyError.code !== 'PGRST116') {
        throw profileOnlyError;
      }

      return {
        subscription_type: profileOnlyData?.subscription_type || null,
        status: null,
        current_period_end: null
      };
    },
    staleTime: 30000,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !!session?.user?.id
  });

  const createSubscription = useMutation({
    mutationFn: async ({ priceId }: { priceId: string }) => {
      if (!session?.user?.id) {
        throw new Error('You must be logged in to create a subscription');
      }

      // Use the Supabase edge function to create a checkout session
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'create-subscription',
          priceId
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      queryClient.invalidateQueries({ queryKey: ['user-limits'] });

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      queryClient.invalidateQueries({ queryKey: ['user-limits'] });
    }
  });

  const cancelSubscription = useMutation({
    mutationFn: async () => {
      if (!session?.user?.id) {
        throw new Error('You must be logged in to cancel a subscription');
      }

      // Use the Supabase edge function to cancel the subscription
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'cancel-subscription'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      queryClient.invalidateQueries({ queryKey: ['user-limits'] });

      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      queryClient.invalidateQueries({ queryKey: ['user-limits'] });
    }
  });

  const updateSubscriptionType = async (type: string) => {
    if (!session?.user?.id) {
      throw new Error('You must be logged in to update your subscription');
    }

    const priceId = Object.keys(priceToType).find(id => priceToType[id] === type);

    if (!priceId) {
      throw new Error('Invalid subscription type');
    }

    // Use the Supabase edge function to update the subscription
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'update-subscription',
        priceId
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Server returned ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    queryClient.invalidateQueries({ queryKey: ['subscription'] });
    queryClient.invalidateQueries({ queryKey: ['user-limits'] });

    // Refresh subscription data
    await refetch();

    // Get the updated subscription
    const { data: updatedSubscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', session.user.id)
      .single();

    return updatedSubscription;
  };

  return {
    subscription,
    isLoadingSubscription,
    createSubscription,
    cancelSubscription,
    updateSubscriptionType,
    refetch
  };
};
