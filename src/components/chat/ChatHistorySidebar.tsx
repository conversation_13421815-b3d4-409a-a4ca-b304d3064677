import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Clock, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface ChatHistoryItem {
  id: string;
  title: string;
  timestamp: string;
  preview: string;
}

interface ChatHistorySidebarProps {
  onNewChat: () => void;
  onSelectChat: (chatId: string) => void;
  currentChatId?: string;
  refreshTrigger?: number;
}

const ChatHistorySidebar: React.FC<ChatHistorySidebarProps> = ({
  onNewChat,
  onSelectChat,
  currentChatId,
  refreshTrigger
}) => {
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Load chat history
  const loadChatHistory = async () => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data: chats, error } = await supabase
        .from('chats')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          messages (
            content,
            created_at,
            role
          )
        `)
        .eq('user_id', user.id)
        .eq('chat_type', 'ai')
        .order('updated_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      const formattedChats = chats?.map(chat => ({
        id: chat.id,
        title: chat.title,
        timestamp: formatTimestamp(chat.updated_at || chat.created_at),
        preview: getFirstUserMessage(chat.messages)
      })) || [];

      setChatHistory(formattedChats);
    } catch (error) {
      console.error('Error loading chat history:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 48) return 'Yesterday';
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)} days ago`;
    return `${Math.floor(diffInHours / 168)} weeks ago`;
  };

  // Helper function to get first user message as preview
  const getFirstUserMessage = (messages: any[]) => {
    const firstUserMessage = messages?.find(msg => msg.role === 'user' && msg.content?.text);
    return firstUserMessage?.content?.text?.substring(0, 50) + '...' || 'No messages yet';
  };

  // Delete chat function
  const deleteChat = async (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent chat selection
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Delete chat messages first
      await supabase
        .from('messages')
        .delete()
        .eq('chat_id', chatId);

      // Delete the chat
      await supabase
        .from('chats')
        .delete()
        .eq('id', chatId)
        .eq('user_id', user.id);

      // Reload chat history
      await loadChatHistory();
    } catch (error) {
      console.error('Error deleting chat:', error);
    }
  };

  // Load chats on component mount and when currentChatId changes
  useEffect(() => {
    loadChatHistory();
  }, []);

  // Refresh chat history when currentChatId changes (new chat created)
  useEffect(() => {
    if (currentChatId && currentChatId !== 'new') {
      // Small delay to ensure the chat is saved to database
      const timer = setTimeout(() => {
        loadChatHistory();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [currentChatId]);

  // Refresh when refreshTrigger changes (new messages saved)
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      // Small delay to ensure the message is saved to database
      const timer = setTimeout(() => {
        loadChatHistory();
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [refreshTrigger]);

  return (
    <div className="w-80 border-l border-white/[0.08] bg-[#0A0A0A] flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-white/[0.08]">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
            Osis History
          </h3>
          <button 
            onClick={onNewChat}
            className="w-8 h-8 rounded-lg bg-white/[0.06] hover:bg-white/[0.1] border border-white/[0.08] hover:border-white/[0.12] flex items-center justify-center transition-all duration-200"
          >
            <Plus className="w-4 h-4 text-white/70" />
          </button>
        </div>
        <p className="text-white/50 text-sm" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
          Your recent conversations
        </p>
      </div>

      {/* Chat History List */}
      <div className="flex-1 overflow-y-auto p-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 text-white/60 animate-spin" />
          </div>
        ) : chatHistory.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-white/40 text-sm mb-2">No conversations yet</div>
            <div className="text-white/30 text-xs">Start a new chat to begin</div>
          </div>
        ) : (
          <div className="space-y-3">
            {chatHistory.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onSelectChat(chat.id)}
                className={`group p-3 rounded-lg transition-all duration-200 cursor-pointer ${
                  currentChatId === chat.id
                    ? 'bg-white/[0.08] border border-white/[0.12]'
                    : 'bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.06] hover:border-white/[0.08]'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="text-white/80 text-sm font-medium mb-1 truncate" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      {chat.title}
                    </div>
                    <div className="text-white/50 text-xs mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      {chat.timestamp}
                    </div>
                    <div className="text-white/40 text-xs line-clamp-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      {chat.preview}
                    </div>
                  </div>
                  <button 
                    onClick={(e) => deleteChat(chat.id, e)}
                    className="opacity-0 group-hover:opacity-100 ml-2 w-6 h-6 rounded-md bg-white/[0.06] hover:bg-red-500/20 border border-white/[0.08] hover:border-red-500/30 flex items-center justify-center transition-all duration-200"
                  >
                    <Trash2 className="w-3 h-3 text-white/60 hover:text-red-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-white/[0.08]">
        <div className="text-center text-white/40 text-xs">
          {chatHistory.length} conversations
        </div>
      </div>
    </div>
  );
};

export default ChatHistorySidebar;
