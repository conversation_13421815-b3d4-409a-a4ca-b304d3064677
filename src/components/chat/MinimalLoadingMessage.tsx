import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface MinimalLoadingMessageProps {
  userQuery: string;
  isComplete?: boolean;
  symbols?: string[];
  finalContent?: string;
  marketData?: any;
  isLoading?: boolean;
  symbolTypes?: { [key: string]: string };
  isGeneralMessage?: boolean;
  onRegenerate?: () => Promise<void>;
}

const MinimalLoadingMessage: React.FC<MinimalLoadingMessageProps> = ({
  userQuery,
  isComplete = false,
  symbols = [],
  finalContent,
  marketData,
  isLoading = false,
  symbolTypes = {},
  isGeneralMessage = false,
  onRegenerate
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  // Simple typing effect for the response
  useEffect(() => {
    if (isComplete && finalContent) {
      const text = finalContent;
      if (currentIndex < text.length) {
        const timer = setTimeout(() => {
          setDisplayText(text.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        }, 20); // Typing speed
        return () => clearTimeout(timer);
      }
    }
  }, [isComplete, finalContent, currentIndex]);

  // Reset when new content arrives
  useEffect(() => {
    if (finalContent) {
      setCurrentIndex(0);
      setDisplayText('');
    }
  }, [finalContent]);

  if (isLoading) {
    return (
      <div className="text-white/60 leading-relaxed py-2">
        <div className="flex items-center gap-3">
          <div className="flex gap-1">
            <div className="w-1.5 h-1.5 bg-white/50 rounded-full animate-pulse"></div>
            <div className="w-1.5 h-1.5 bg-white/50 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-1.5 h-1.5 bg-white/50 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
          <span className="text-sm">Processing market data...</span>
        </div>
      </div>
    );
  }

  // Render market data if available
  const renderMarketData = () => {
    if (!marketData || isGeneralMessage) return null;

    return (
      <div className="space-y-4 mt-4">
        {symbols.map((symbol, index) => {
          const data = marketData[symbol];
          if (!data) return null;

          const price = data.price || data.regularMarketPrice || 0;
          const change = data.change || data.regularMarketChange || 0;
          const changePercent = data.changePercent || data.regularMarketChangePercent || 0;

          const isPositive = change >= 0;
          const isNeutral = change === 0;

          return (
            <div key={symbol} className="border-l-2 border-white/20 pl-4">
              <div className="flex items-center gap-3 mb-2">
                <span className="text-white font-semibold text-lg">{symbol}</span>
                <span className="text-white/60 text-sm">
                  {symbolTypes[symbol] === 'CRYPTO' ? 'Cryptocurrency' :
                   symbolTypes[symbol] === 'MARKET' ? 'Market Index' : 'Stock'}
                </span>
              </div>

              <div className="flex items-center gap-4 mb-2">
                <span className="text-white text-xl font-bold">
                  ${typeof price === 'number' ? price.toFixed(2) : price}
                </span>

                <div className={`flex items-center gap-1 ${
                  isNeutral ? 'text-white/60' :
                  isPositive ? 'text-emerald-400' : 'text-red-400'
                }`}>
                  {isNeutral ? (
                    <Minus className="h-4 w-4" />
                  ) : isPositive ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  <span className="font-medium">
                    {isPositive && change > 0 ? '+' : ''}
                    {typeof change === 'number' ? change.toFixed(2) : change}
                    {' '}({isPositive && changePercent > 0 ? '+' : ''}
                    {typeof changePercent === 'number' ? changePercent.toFixed(2) : changePercent}%)
                  </span>
                </div>
              </div>

              {/* Simple analysis text */}
              {data.analysis && (
                <p className="text-white/80 leading-relaxed text-sm">
                  {data.analysis}
                </p>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="text-white/80 leading-relaxed space-y-4">
      {/* General message content */}
      {isGeneralMessage && finalContent && (
        <div className="prose prose-invert max-w-none">
          <p className="whitespace-pre-wrap">{displayText}</p>
          {currentIndex < finalContent.length && (
            <span className="inline-block w-2 h-5 bg-white/60 animate-pulse ml-1"></span>
          )}
        </div>
      )}

      {/* Market data */}
      {renderMarketData()}

      {/* AI-generated analysis */}
      {!isGeneralMessage && finalContent && (
        <div className="mt-4 pt-4 border-t border-white/10">
          <h4 className="text-white font-semibold mb-2">Analysis</h4>
          <p className="whitespace-pre-wrap">{displayText}</p>
          {currentIndex < finalContent.length && (
            <span className="inline-block w-2 h-5 bg-white/60 animate-pulse ml-1"></span>
          )}
        </div>
      )}

      {/* Regenerate button */}
      {isComplete && onRegenerate && (
        <div className="mt-6 pt-4 border-t border-white/10">
          <button
            onClick={onRegenerate}
            className="text-white/60 hover:text-white text-sm underline transition-colors"
          >
            Regenerate response
          </button>
        </div>
      )}

      {/* Disclaimer */}
      {!isGeneralMessage && symbols.length > 0 && (
        <div className="mt-6 pt-4 border-t border-white/10">
          <p className="text-white/40 text-xs leading-relaxed">
            This analysis is AI-generated and for educational purposes only.
            Not financial advice. Always do your own research before making investment decisions.
          </p>
        </div>
      )}
    </div>
  );
};

export default MinimalLoadingMessage;
