import React, { useMemo } from 'react';
import { cn } from '@/lib/utils';

interface MessageCounterProps {
  tokensRemaining: number;
  maxTokens: number;
  subscriptionPlan?: string;
}

const MessageCounter: React.FC<MessageCounterProps> = ({
  tokensRemaining,
  maxTokens,
  subscriptionPlan
}) => {
  // Use useMemo for color and label calculations
  const dotColorClass = useMemo(() => {
    const percentage = (tokensRemaining / maxTokens) * 100;
    if (tokensRemaining <= 0) return 'bg-red-500';
    if (percentage <= 10) return 'bg-orange-500'; // Low warning
    return 'bg-emerald-500'; // Default green
  }, [tokensRemaining, maxTokens]);

  const resetLabel = useMemo(() => {
    // Check if user is on a free trial (3 messages per day)
    const isOnFreeTrial = maxTokens === 3;

    if (isOnFreeTrial) {
      return 'Daily'; // Free trial resets daily
    } else if (!subscriptionPlan || subscriptionPlan === 'free') {
      return 'Daily';
    } else if (subscriptionPlan === 'basic' || subscriptionPlan === 'pro' || subscriptionPlan === 'premium') {
      return 'Monthly';
    }
    return '';
  }, [subscriptionPlan, maxTokens]);

  const { planText, planColor } = useMemo(() => {
    let text = '';
    let color = '';

    // Check if user is on a free trial (3 messages per day)
    const isOnFreeTrial = maxTokens === 3;

    if (isOnFreeTrial) {
      // Show which plan's free trial they're on
      if (subscriptionPlan === 'basic') {
        text = 'Basic Trial';
        color = 'text-green-400';
      } else if (subscriptionPlan === 'pro') {
        text = 'Pro Trial';
        color = 'text-blue-400';
      } else {
        text = 'Free Trial';
        color = 'text-yellow-400';
      }
    } else if (subscriptionPlan === 'free') {
      text = 'Free Plan';
      color = 'text-gray-400';
    } else if (subscriptionPlan === 'basic') {
      text = 'Basic Plan';
      color = 'text-green-400';
    } else if (subscriptionPlan === 'pro') {
      text = 'Pro Plan';
      color = 'text-blue-400';
    } else if (subscriptionPlan === 'premium') {
      text = 'Premium Plan';
      color = 'text-purple-400';
    }

    return { planText: text, planColor: color };
  }, [subscriptionPlan, maxTokens]);

  return (
    <div className="flex items-center gap-2 px-3 py-2 bg-[#1A1A1A] backdrop-blur-sm rounded-lg border border-white/[0.06]">
      <div data-testid="status-dot" className={cn("w-1.5 h-1.5 rounded-full", dotColorClass)} />
      <div className="flex flex-col">
        <div className="flex items-center gap-1.5">
          <span className="text-sm font-medium text-white/90">
            {tokensRemaining} {tokensRemaining === 1 ? 'Message' : 'Messages'}
          </span>
          <span className={`text-xs ${planColor}`}>
            ({planText || 'Weekly Plan'})
          </span>
        </div>
        {resetLabel && (
          <span className="text-[11px] text-white/50">
            {resetLabel} reset
          </span>
        )}
      </div>
    </div>
  );
};

export default React.memo(MessageCounter);