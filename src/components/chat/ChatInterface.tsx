import * as React from "react";
import { useState, useEffect, useRef } from "react";
import axios from "axios";
import { Paperclip, Globe, Send, Loader2, MessageSquare, BarChart3, Code2, Layout, HelpCircle, Search, Brain, User, TrendingUp, Bitcoin, DollarSign, Newspaper, BookOpen, PieChart, ArrowUp, X, Bot, Settings, ChevronDown, Plus, Trash2, Clock } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate, useParams } from "react-router-dom";
import ModelSelector from "./ModelSelector";
import MessageCard from './MessageCard';
import TypingEffect from "@/components/ui/typing-effect";
import { AnalysisState } from './types';
import { cn } from "@/lib/utils";
// Removed Popover - using custom dropdown instead
import { Button } from "@/components/ui/button";
import { WelcomeHeading } from "@/components/ui/WelcomeHeading";
import MessageCounter from './MessageCounter';
import LoadingMessage from './LoadingMessage';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useAuth } from '@/contexts/AuthContext';
import { getAgents } from '@/services/agentService';
import { useResponsive } from '@/hooks/useResponsive';
import MobileChatHistory from '@/components/mobile/MobileChatHistory';
import ChatHistorySidebar from '@/components/chat/ChatHistorySidebar';

// =============================================
// TYPE DEFINITIONS
// =============================================
interface Message {
  role: string;
  content: {
    text: string | null;
    marketData?: any;
    webResults?: any[];
    symbol?: string | null;
    searchQueries?: string[];
    evaAnalysis?: any;
    symbols?: string[];
    isLoading?: boolean;
    analysisProgress?: any;
    aiAnalysis?: string;
    symbolTypes?: any;
    loadingPlaceholder?: boolean;
    isGeneralMessage?: boolean;
    agentResults?: any;
    selectedAgent?: any;
    isSingleAgentAnalysis?: boolean;
  };
}

interface UserTokens {
  user_id: string;
  tokens_remaining: number;
  tokens_used: number;
  reset_date: string;
}

interface MarketData {
  price?: number;
  change?: number;
  changePercent?: number;
  volume?: number;
  // Add other market data fields as needed
}

// Update ChatAIResponse interface
interface ChatAIResponse {
  symbols: string[];
  symbolTypes: { [key: string]: 'STOCK' | 'CRYPTO' | 'MARKET' };
  marketData: any;
  isGeneralMessage: boolean;
  agentResults?: AgentExecutionResult[];
  isSingleAgentAnalysis?: boolean;
}

// Simple chat response interface for text content only
interface AgentExecutionResult {
  agentName: string;
  result: {
    signal: string;
    confidence: number;
    reasoning: string;
  };
  symbol: string;
}

interface SimpleChatResponse {
  response: string;
  symbols: string[];
  agentResults: AgentExecutionResult[];
  hasAgentAnalysis: boolean;
}

// =============================================
// CONSTANTS
// =============================================
const QUICK_PROMPTS = [
  {
    text: "Is NVDA overvalued?",
    category: "Finance",
    icon: <TrendingUp className="w-4 h-4 text-emerald-400" />
  },
  {
    text: "Would Cathie Wood double down on TSLA today?",
    category: "Crypto",
    icon: <Bitcoin className="w-4 h-4 text-orange-400" />
  },
  {
    text: "Should I long or short PLTR?",
    category: "News",
    icon: <Newspaper className="w-4 h-4 text-blue-400" />
  },
  {
    text: "Would Warren Buffett buy AAPL?",
    category: "Education",
    icon: <BookOpen className="w-4 h-4 text-purple-400" />
  },
  {
    text: "Teach me how to trade options",
    category: "Investment",
    icon: <PieChart className="w-4 h-4 text-cyan-400" />
  }
];

// =============================================
// DYNAMIC LOADING MESSAGE COMPONENT
// =============================================
const DynamicLoadingMessage: React.FC = () => {
  const [currentMessage, setCurrentMessage] = useState(0);

  const messages = [
    "Analyzing your request...",
    "Connecting to market data...",
    "Processing financial indicators...",
    "Gathering latest news...",
    "Consulting trading experts...",
    "Generating insights..."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % messages.length);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="text-white/70 flex items-center gap-2">
      <div className="h-4 w-4 rounded-full border-2 border-white/10 border-t-white/30 animate-spin" />
      <span className="transition-opacity duration-300">
        {messages[currentMessage]}
      </span>
    </div>
  );
};

// =============================================
// MAIN CHAT INTERFACE COMPONENT
// =============================================
const ChatInterface: React.FC = () => {
  // Define animations for smooth transitions
  const animationStyles = `
    @keyframes reveal {
      0% {
        opacity: 0;
        transform: scale(0.98);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    .animate-reveal {
      animation: reveal 400ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
    }

    @keyframes popIn {
      0% {
        opacity: 0;
        transform: scale(0.96);
      }
      70% {
        opacity: 1;
        transform: scale(1.02);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    .animate-pop-in {
      animation: popIn 400ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
    }

    /* Staggered animations for search results */
    .search-result-item:nth-child(1) {
      animation-delay: 100ms;
    }

    .search-result-item:nth-child(2) {
      animation-delay: 200ms;
    }

    .search-result-item:nth-child(3) {
      animation-delay: 300ms;
    }

    /* Animation for response content sections */
    .response-section {
      opacity: 0;
      animation: sectionFadeIn 500ms ease forwards;
    }

    @keyframes sectionFadeIn {
      0% {
        opacity: 0;
        transform: translateY(8px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Staggered animations for different sections */
    .response-section:nth-child(1) {
      animation-delay: 150ms;
    }

    .response-section:nth-child(2) {
      animation-delay: 300ms;
    }

    .response-section:nth-child(3) {
      animation-delay: 450ms;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    .animate-fade-in {
      animation: fadeIn 300ms ease forwards;
    }

    @keyframes gradient {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }

    .gradient-text {
      background: linear-gradient(90deg, rgba(255,255,255,0.95), rgba(200,200,200,0.8), rgba(170,170,170,0.9));
      background-size: 200% auto;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: gradient 8s ease infinite;
    }
  `;

  // =============================================
  // STATE VARIABLES
  // =============================================
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [slidePosition, setSlidePosition] = useState(0);
  const [userTokens, setUserTokens] = useState<UserTokens | null>(null);

  // Update these loading state variables to default to false
  const [isAuthLoading, setIsAuthLoading] = useState(false);

  // Analysis loading states for different data types
  const [analysisState, setAnalysisState] = useState<AnalysisState>({
    isLoadingMarketData: false,
    isLoadingNews: false,
    isLoadingAnalysis: false,
    isLoadingAI: false
  });

  // Skip welcome screen by default
  const [shouldSkipWelcome, setShouldSkipWelcome] = useState(true);

  // Get authentication state from useAuth hook
  const { isAuthenticated, isNewUser } = useAuth();

  // Add useUserLimits hook
  const {
    messagesRemaining,
    messagesLimit,
    incrementMessagesUsed,
    isLoading: isLoadingLimits,
    planType,
    subscription,
    refreshLimits,
    hasReachedLimit
  } = useUserLimits();

  // Set this to false to avoid loading screen
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(false);

  // Add a new state to store the user's name
  const [userName, setUserName] = useState<string>("");

  // Add selectedAgent state
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);

  // Add state for agent dropdown visibility
  const [showAgentDropdown, setShowAgentDropdown] = useState(false);

  // Add state to store selected agent name for display
  const [selectedAgentName, setSelectedAgentName] = useState<string | null>(null);

  // Add state for available agents
  const [availableAgents, setAvailableAgents] = useState<any[]>([]);

  // Add responsive hook
  const { isMobile, classes } = useResponsive();

  // Mobile chat history state
  const [showMobileChatHistory, setShowMobileChatHistory] = useState(false);

  // State to trigger sidebar refresh
  const [sidebarRefreshTrigger, setSidebarRefreshTrigger] = useState(0);

  // =============================================
  // REFS, HOOKS, AND NAVIGATION
  // =============================================
  const fileInputRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id: currentChatId } = useParams();

  // Reset state for new chats
  useEffect(() => {
    if (!currentChatId) {
      // This is a new chat, reset all states
      setMessages([]);
      setIsLoading(false);
    }
  }, [currentChatId]);

  // Update the auth check effect
  useEffect(() => {
    const checkAuth = async () => {
      try {
      } catch (error) {
      } finally {
        setIsAuthLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Restore the chat loading effect
  useEffect(() => {
    if (!currentChatId || !isAuthenticated) {
      setMessages([]);
      return;
    }

    const loadChat = async () => {
      try {
        const { data: chatMessages, error } = await supabase
          .from('messages')
          .select('*')
          .eq('chat_id', currentChatId)
          .order('created_at', { ascending: true });

        if (error) {
          throw error;
        }

        if (chatMessages) {
          const formattedMessages = chatMessages.map(msg => ({
            role: msg.role,
            content: typeof msg.content === 'string' ? JSON.parse(msg.content) : msg.content
          }));
          setMessages(formattedMessages);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load chat messages. Please try refreshing the page.",
          variant: "destructive"
        });
      }
    };

    loadChat();
  }, [currentChatId, isAuthenticated]);

  // Update the welcome screen check effect
  useEffect(() => {
    setIsCheckingOnboarding(true);
    // Check URL parameters and hash for auth tokens
    const skipWelcome = new URLSearchParams(window.location.search).get('skipWelcome') === 'true';
    const hasAuthToken = window.location.hash.includes('access_token=');

    // Force show welcome screen when new empty chat, unless explicitly skipped
    const shouldShowWelcome = (!currentChatId || currentChatId === 'new') && !(skipWelcome || hasAuthToken);

    setShouldSkipWelcome(!shouldShowWelcome);
    setIsCheckingOnboarding(false);

    // Clean up hash if it contains auth tokens
    if (hasAuthToken) {
      // Remove the hash without causing a page reload
      const cleanUrl = window.location.href.split('#')[0];
      window.history.replaceState({}, document.title, cleanUrl);
    }
  }, [currentChatId, isAuthenticated]);

  // =============================================
  // QUICK PROMPTS ANIMATION EFFECT
  // =============================================
  const [carouselWidth, setCarouselWidth] = useState(0);

  // Update the carousel animation effect
  useEffect(() => {
    let animationInterval: NodeJS.Timeout;

    const initializeCarousel = () => {
      const carousel = document.querySelector('.quick-prompts-carousel');
      if (carousel) {
        const totalWidth = carousel.scrollWidth / 2; // Divide by 2 because we duplicated the items
        setCarouselWidth(totalWidth);

        // Start the animation with a faster speed
        const animationSpeed = 15; // Lower = faster
        animationInterval = setInterval(() => {
          setSlidePosition(prev => {
            const newPosition = prev - 1;
            return newPosition <= -totalWidth ? 0 : newPosition;
          });
        }, animationSpeed);
      }
    };

    // Initialize carousel
    initializeCarousel();

    // Set up a mutation observer to watch for changes to the carousel
    const observer = new MutationObserver(() => {
      // Clear existing interval
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      // Reinitialize carousel
      initializeCarousel();
    });

    const carouselElement = document.querySelector('.quick-prompts-carousel');
    if (carouselElement) {
      observer.observe(carouselElement, {
        childList: true,
        subtree: true,
        attributes: true
      });
    }

    // Cleanup function
    return () => {
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      observer.disconnect();
    };
  }, [shouldSkipWelcome, isAuthenticated]); // Add dependencies to ensure animation restarts properly

  // =============================================
  // TOKEN MANAGEMENT EFFECTS
  // =============================================
  useEffect(() => {
    const checkInitialTokens = async () => {
      // Let's remove this check since it could be causing resets
      // We'll rely on the useUserLimits hook for this
      // const { data: { session } } = await supabase.auth.getSession();
      // if (session?.user?.id) {
      //   await checkAndUpdateTokens(session.user.id);
      // }
    };
    checkInitialTokens();
  }, []);

  // =============================================
  // SESSION MANAGEMENT EFFECT
  // =============================================
  useEffect(() => {
    const initializeSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.refresh_token) {
        // Update the access token
        return session.access_token;
      }
    };

    initializeSession();
  }, []);

  // =============================================
  // TOKEN MANAGEMENT FUNCTIONS
  // =============================================
  const checkAndUpdateTokens = async (userId: string) => {
    // This function is now replaced by the useUserLimits hook
    return await hasReachedLimit() === false;
  };

  const consumeToken = async (userId: string) => {
    // This function is now replaced by the useUserLimits hook's incrementMessagesUsed function
    const result = await incrementMessagesUsed();
    return result.success;
  };

  // =============================================
  // CHAT MANAGEMENT FUNCTIONS
  // =============================================
  const createNewChat = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data: newChat, error } = await supabase
        .from('chats')
        .insert({
          user_id: user.id,
          title: 'New Chat',
          chat_type: 'ai'
        })
        .select()
        .single();

      if (error) throw error;
      return newChat;
    } catch (error) {
      console.error('Error creating new chat:', error);
      toast({
        title: "Error",
        description: "Failed to create new chat",
        variant: "destructive"
      });
      return null;
    }
  };

  // Save message to database
  const saveMessageToDatabase = async (chatId: string, role: 'user' | 'assistant', content: any) => {
    try {
      const { data: savedMessage, error } = await supabase
        .from('messages')
        .insert({
          chat_id: chatId,
          role: role,
          content: content
        })
        .select()
        .single();

      if (error) throw error;
      return savedMessage;
    } catch (error) {
      console.error('Error saving message:', error);
      return null;
    }
  };

  // Update chat title based on first message
  const updateChatTitle = async (chatId: string, userMessage: string) => {
    try {
      // Generate a title from the first few words of the message
      const title = userMessage.length > 50
        ? userMessage.substring(0, 50) + '...'
        : userMessage;

      const { error } = await supabase
        .from('chats')
        .update({ title: title })
        .eq('id', chatId);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating chat title:', error);
    }
  };







  // =============================================
  // AGENT LOADING AND MANAGEMENT
  // =============================================
  useEffect(() => {
    const loadAgents = async () => {
      try {
        const agents = await getAgents();
        setAvailableAgents(agents.map(agent => ({
          id: agent.id,
          name: agent.name,
          description: agent.description || 'Trading agent'
        })));
      } catch (error) {
        console.error('Error loading agents:', error);
      }
    };

    if (isAuthenticated) {
      loadAgents();
    }
  }, [isAuthenticated]);

  // =============================================
  // MESSAGE SUBMISSION HANDLER
  // =============================================
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('[ChatInterface] handleSubmit called');


    if (!message.trim() || isLoading) {
      console.log('[ChatInterface] Empty message or already loading, returning');
      return;
    }

    // MANDATORY AGENT SELECTION - Block if no agent selected
    if (!selectedAgent) {
      toast({
        title: "Agent Required",
        description: "Please select a trading agent before sending your message.",
        variant: "destructive"
      });
      setShowAgentDropdown(true); // Open agent dropdown
      return;
    }

    console.log('[ChatInterface] Starting message submission process');

    try {
      console.log('[ChatInterface] Setting loading state to true');
      setIsLoading(true);

      // Add user message to the chat immediately for better UX
      const userMessage: Message = {
        role: 'user',
        content: {
          text: message
        }
      };

      // Add a loading message
      const loadingMessage: Message = {
        role: 'assistant',
        content: {
          text: null,
          loadingPlaceholder: true,
          isLoading: true
        }
      };

      // Store user input and clear the input field
      setMessages(prev => [...prev, userMessage, loadingMessage]);
      const userInput = message;
      setMessage('');

      // Decrement the counter using the hook - this now handles all database updates
      const result = await incrementMessagesUsed();

      if (!result?.success) {
        toast({
          title: "Message limit error",
          description: result?.message || "Failed to process your message. Please try again.",
          variant: "destructive"
        });
        setIsLoading(false);

        // Remove the loading message
        setMessages(prev => prev.filter(msg => !msg.content.loadingPlaceholder));
        return;
      }

      // Show warning if this was their last message
      if (result.isWarning && result.message) {
        toast({
          title: "Message limit warning",
          description: result.message,
          variant: "default"
        });
      }

      // NEW SIMPLIFIED CHAT FLOW
      try {
        console.log('ChatInterface - Starting new simplified chat flow with agent:', selectedAgent);

        // Step 1: Extract tickers from user message
        const { data: symbolsResponse, error: symbolsError } = await supabase.functions.invoke('extract-symbols', {
          body: { message: userInput }
        });

        if (symbolsError) {
          throw new Error(`Symbol extraction error: ${symbolsError.message}`);
        }

        const symbols = symbolsResponse?.symbols || [];
        console.log('ChatInterface - Extracted symbols:', symbols);

        // Step 2: Run selected agent on all extracted symbols
        let agentResults: any[] = [];
        if (symbols.length > 0) {
          console.log('ChatInterface - Running agent on symbols...');

          for (const symbol of symbols) {
            try {
              const { data: agentResponse, error: agentError } = await supabase.functions.invoke('agent-runner', {
                body: {
                  agentId: selectedAgent,
                  symbol: symbol,
                  saveRun: false // Don't save to database for chat
                }
              });

              if (!agentError && agentResponse) {
                agentResults.push({
                  symbol: symbol,
                  agentId: selectedAgent,
                  agentName: selectedAgentName,
                  ...agentResponse
                });
              }
            } catch (error) {
              console.error(`Error running agent for ${symbol}:`, error);
            }
          }
        }

        console.log('ChatInterface - Agent results:', agentResults.length);

        // Step 2.5: Get market data for charts (like the old system)
        let marketData = {};
        let symbolTypes = {};

        if (symbols.length > 0) {
          try {
            const { data: chartResponse, error: chartError } = await supabase.functions.invoke('chat-ai', {
              body: {
                messages: [{
                  role: 'user',
                  content: userInput
                }]
              }
            });

            if (!chartError && chartResponse) {
              marketData = chartResponse.marketData || {};
              symbolTypes = chartResponse.symbolTypes || {};
              console.log('ChatInterface - Got market data for charts');
            }
          } catch (error) {
            console.error('Error fetching market data:', error);
          }
        }

        // Step 3: Send agent results to Gemini with enhanced conversational prompt
        const geminiPrompt = `You are an experienced trading analyst having a conversation with a trader. The user asked: "${userInput}"

I've run my custom trading agent analysis on the mentioned stocks, and here's what I found:

${agentResults.map(result => `
**${result.symbol} Analysis:**
- Signal: ${result.signal.toUpperCase()}
- Confidence Level: ${result.confidence}%
- Agent Reasoning: ${result.reasoning}
- Agent Name: ${result.agentName}
`).join('\n')}

Please provide a detailed, conversational response as if you're an experienced trader sharing insights. Include:
1. Your interpretation of the analysis
2. What the signal means in practical terms
3. Key factors to consider
4. Risk management thoughts
5. A conversational, engaging tone (like you're talking to a fellow trader)

Make it sound natural and insightful, not robotic. Share your perspective on what this analysis means for the trader.`;

        const { data: geminiResponse, error: geminiError } = await supabase.functions.invoke('gemini-api', {
          body: {
            prompt: geminiPrompt,
            temperature: 0.7,
            maxTokens: 1000
          }
        });

        console.log('ChatInterface - Gemini response:', geminiResponse);
        console.log('ChatInterface - Gemini error:', geminiError);

        if (geminiError) {
          throw new Error(`Gemini response error: ${geminiError.message}`);
        }

        // Extract text from Gemini response format
        const geminiText = geminiResponse?.data?.candidates?.[0]?.content?.parts?.[0]?.text ||
                          geminiResponse?.response ||
                          geminiResponse?.text ||
                          'Sorry, I could not process your request.';

        console.log('ChatInterface - Extracted Gemini text:', geminiText);

        // Step 4: Prepare response for UI (keeping existing format for compatibility)
        const assistantContent = {
          text: geminiText,
          marketData: marketData,
          symbols: symbols,
          symbolTypes: symbolTypes,
          agentResults: agentResults,
          selectedAgent: selectedAgent,
          isSingleAgentAnalysis: true,
          isLoading: false,
          loadingPlaceholder: false,
          isGeneralMessage: symbols.length === 0
        };

        // Update the message with new data
        setMessages(prev => {
          const updated = [...prev];
          const lastMessage = updated[updated.length - 1];
          if (lastMessage.role === 'assistant') {
            lastMessage.content = assistantContent;
          }
          return updated;
        });

        // Create new chat if needed and navigate to it
        let activeChatId = currentChatId;
        if (!currentChatId || currentChatId === 'new') {
          const newChat = await createNewChat();
          if (newChat) {
            activeChatId = newChat.id;
            // Navigate to the new chat URL
            navigate(`/chat/${newChat.id}`, { replace: true });
          }
        }

        // Save messages to database if we have a valid chat ID
        if (activeChatId && activeChatId !== 'new') {
          // Save user message
          await saveMessageToDatabase(activeChatId, 'user', { text: userInput });

          // Save assistant message
          await saveMessageToDatabase(activeChatId, 'assistant', assistantContent);

          // Update chat title if this is the first message
          if (!currentChatId || currentChatId === 'new') {
            await updateChatTitle(activeChatId, userInput);
          }

          // Trigger sidebar refresh
          setSidebarRefreshTrigger(prev => prev + 1);
        }
      } catch (error) {
        console.error('[ChatInterface] API Error:', error);
        // Show error but keep the message to avoid losing it
        toast({
          title: 'API Error',
          description: error instanceof Error ? error.message : 'Failed to get a response. Please try again.',
          variant: 'destructive'
        });

        // Update messages to show the error instead of removing the message completely
        setMessages(prev => {
          const updated = [...prev];
          const lastMessage = updated[updated.length - 1];
          if (lastMessage.role === 'assistant') {
            lastMessage.content = {
              text: 'Sorry, I encountered an error processing your request. Please try again.',
              isLoading: false,
              loadingPlaceholder: false,
              isGeneralMessage: true
            };
          }
          return updated;
        });
      }

      console.log('[ChatInterface] Setting loading state to false');
      setIsLoading(false);
    } catch (error) {
      console.error('[ChatInterface] Outer catch error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to get a response. Please try again.',
        variant: 'destructive'
      });
      setIsLoading(false);

      // Remove the loading message on error
      setMessages(prev => prev.filter(msg => !msg.content.loadingPlaceholder));
    }
  };

  // =============================================
  // FILE UPLOAD HANDLER
  // =============================================
  // Removed file upload handler
  // const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   if (e.target.files && e.target.files.length > 0) {
  //     const file = e.target.files[0];
  //     const reader = new FileReader();
  //     reader.onload = (event) => {
  //       // setUploadedImage(event.target?.result as string);
  //     };
  //     reader.readAsDataURL(file);
  //   }
  // };

  // =============================================
  // EFFECTS
  // =============================================
  // Add animation styles effect
  useEffect(() => {
    // Add animation styles to document
    const styleEl = document.createElement('style');
    styleEl.textContent = animationStyles;
    document.head.appendChild(styleEl);

    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  // Add an effect to get user's name from profile when component mounts
  useEffect(() => {
    const getUserName = async () => {
      try {
        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.id) {
          // First try to get the name from the profiles table
          const { data: profile } = await supabase
            .from('profiles')
            .select('full_name')
            .eq('id', user.id)
            .single();

          let firstName = '';

          if (profile?.full_name) {
            // Extract first name from full name
            firstName = profile.full_name.split(' ')[0];
          } else if (user.user_metadata?.full_name) {
            // Try to get from user metadata
            firstName = user.user_metadata.full_name.split(' ')[0];
          } else if (user.email) {
            // Fallback to email username
            firstName = user.email.split('@')[0];
            // Capitalize first letter
            firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
          }

          // Only set if we have a valid name (at least 2 chars)
          if (firstName && firstName.length >= 2) {
            setUserName(firstName);
          }
        }
      } catch (error) {
        console.error("Error getting user's name:", error);
      }
    };

    getUserName();
  }, []);

  // Load messages for a specific chat
  const loadChatMessages = async (chatId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data: messages, error } = await supabase
        .from('messages')
        .select('*')
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Convert database messages to UI message format
      return messages?.map(msg => ({
        role: msg.role,
        content: msg.content
      })) || [];
    } catch (error) {
      console.error('Error loading chat messages:', error);
      return [];
    }
  };

  // Load messages when chat ID changes
  useEffect(() => {
    const loadMessages = async () => {
      if (currentChatId && currentChatId !== 'new') {
        const chatMessages = await loadChatMessages(currentChatId);
        setMessages(chatMessages);
      } else {
        setMessages([]);
      }
    };

    loadMessages();
  }, [currentChatId]);

  // =============================================
  // RENDER UI
  // =============================================
  return (
    <div className={`flex h-full bg-[#0A0A0A] ${isMobile ? 'flex-col' : ''}`}>
      <style>{animationStyles}</style>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header with Agent Selection - Responsive */}
        <div className={`${isMobile ? 'px-3 py-3' : classes.container} bg-[#0A0A0A]`}>
          <div className="flex justify-center items-center relative">
            {/* Mobile Chat History Button - Centered */}
            {isMobile && (
              <button
                onClick={() => setShowMobileChatHistory(true)}
                className="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-1.5 px-2.5 py-1.5 bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.08] hover:border-white/[0.12] rounded-lg text-white/80 hover:text-white transition-all duration-200"
              >
                <Clock className="w-3.5 h-3.5 text-white/60" />
                <span className="text-xs">History</span>
              </button>
            )}

            {/* Agent Selection Button - Desktop Only */}
            {!isMobile && (
              <button
                onClick={() => setShowAgentDropdown(true)}
                className="flex items-center gap-2 px-4 py-2 bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.08] hover:border-white/[0.12] rounded-lg text-white/80 hover:text-white transition-all duration-200"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif"' }}
              >
                <Bot className="w-4 h-4 text-white/60" />
                <span>{selectedAgentName || 'Select Agent'}</span>
                <ChevronDown className="w-3 h-3 text-white/60" />
              </button>
            )}
          </div>
        </div>

        {/* Rest of the chat interface */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 flex flex-col overflow-hidden">
          {((!currentChatId || currentChatId === 'new') && !isLoading && messages.length === 0 && !shouldSkipWelcome) ? (
            <div className={`flex-1 flex items-center justify-center ${classes.container} overflow-hidden`}>
              <div className={`${isMobile ? 'max-w-sm' : 'max-w-xl'} w-full`}>
                {/* Clean Welcome Header */}
                <div className={`space-y-4 text-center ${isMobile ? 'mb-8' : 'mb-12'}`}>
                  <WelcomeHeading
                    text="What are we analyzing today?"
                    className={`gradient-text ${classes.title} font-medium`}
                    speed={80}
                  />
                </div>

                {/* Agent selection moved to header */}

                {/* Input Card - Updated with exact styling from the image */}
                <Card
                  className="bg-[#141414]/40 backdrop-filter backdrop-blur-xl border-[0.5px] border-[#303035]/60 rounded-xl shadow-[0_8px_16px_rgba(0,0,0,0.2)] relative p-2 cursor-text"
                  onClick={() => {
                    const textareaElement = document.querySelector('textarea[placeholder="Ask away..."]') as HTMLTextAreaElement;
                    if (textareaElement) textareaElement.focus();
                  }}
                >
                  <div className="flex flex-col min-h-[36px]">
                    <form onSubmit={handleSubmit} className="relative flex flex-col">
                      {/* Textarea that grows with content - Now taller by default */}
                      <div className="relative">
                        <textarea
                          value={message}
                          onChange={(e) => {
                            setMessage(e.target.value);
                            // Auto-resize the textarea
                            e.target.style.height = '40px';
                            e.target.style.height = `${Math.min(120, Math.max(40, e.target.scrollHeight))}px`;
                          }}
                          placeholder="Ask away..."
                          className={`bg-transparent border-0 outline-none text-white placeholder:text-white/40 w-full resize-none overflow-hidden pl-3 pt-2.5 pb-16 pr-[100px] focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${isMobile ? 'text-base' : 'text-[1rem]'}`}
                          rows={2}
                          style={{ height: isMobile ? '80px' : '100px' }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSubmit(e as any);
                            }
                          }}
                        />
                      </div>
                      {/* Right Controls - Submit button fixed to the bottom right */}
                      <div className="button-container welcome-button">
                        <button
                          type="submit"
                          disabled={isLoading || !message.trim() || !selectedAgent}
                          className={`shadow-[0_0_15px_rgba(255,255,255,0.2)] rounded-lg px-6 py-2 h-10 text-sm font-medium flex items-center justify-center overflow-hidden transition-all duration-200 ${
                            !selectedAgent || isLoading || !message.trim() 
                              ? 'bg-white text-black cursor-not-allowed shadow-[0_0_15px_rgba(255,255,255,0.2)]'
                              : 'bg-[#FFFFFF] text-black hover:bg-gray-100'
                          }`}
                          style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif"' }}
                        >
                          {isLoading ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              <span>Processing...</span>
                            </>
                          ) : (
                            <>
                              {/* Removed Bot icon */}
                              <ArrowUp className="w-4 h-4 mr-2" />
                              <span>{selectedAgent ? 'Generate' : 'Generate'}</span>
                            </>
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                </Card>

                {/* Quick Prompts Carousel - Enhanced with sophisticated styling */}
                <div className="relative mt-4">
                  {/* Gradient underlays for depth */}
                  <div className="absolute inset-x-0 h-12 top-1/2 -translate-y-1/2 opacity-30">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-purple-500/5 to-blue-500/5"></div>
                    <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-white/[0.03] via-transparent to-transparent"></div>
                  </div>

                  <div className="flex overflow-hidden py-1.5">
                    <div
                      className="flex gap-2 quick-prompts-carousel px-1"
                      style={{
                        transform: `translateX(${slidePosition}px)`,
                        transition: slidePosition === 0 ? 'none' : 'transform 0.1s linear',
                        willChange: 'transform'
                      }}
                    >
                      {[...QUICK_PROMPTS, ...QUICK_PROMPTS].map((prompt, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            setMessage(prompt.text);
                            const syntheticEvent = new Event('submit', {
                              bubbles: true,
                              cancelable: true,
                            }) as unknown as React.FormEvent;
                            handleSubmit(syntheticEvent);
                          }}
                          className="group relative flex items-center gap-2 px-3 py-1.5 rounded-lg bg-[#141414]/40 hover:bg-[#1A1A1A]/60 transition-all duration-300 border border-white/[0.02] hover:border-white/[0.05] shadow-[0_0_1px_rgba(0,0,0,0.1)] backdrop-blur-sm whitespace-nowrap"
                        >
                          {/* Hover effect overlay */}
                          <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="absolute inset-0 bg-gradient-to-r from-white/[0.02] to-transparent"></div>
                          </div>

                          {/* Category-specific styling */}
                          {prompt.category === "Finance" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-emerald-950/30 group-hover:bg-emerald-900/40 transition-colors duration-300"></div>
                              <TrendingUp className="w-3 h-3 text-emerald-300/70 group-hover:text-emerald-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "Crypto" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-orange-950/30 group-hover:bg-orange-900/40 transition-colors duration-300"></div>
                              <Bitcoin className="w-3 h-3 text-orange-300/70 group-hover:text-orange-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "News" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-blue-950/30 group-hover:bg-blue-900/40 transition-colors duration-300"></div>
                              <Newspaper className="w-3 h-3 text-blue-300/70 group-hover:text-blue-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "Education" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-purple-950/30 group-hover:bg-purple-900/40 transition-colors duration-300"></div>
                              <BookOpen className="w-3 h-3 text-purple-300/70 group-hover:text-purple-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "Investment" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-cyan-950/30 group-hover:bg-cyan-900/40 transition-colors duration-300"></div>
                              <PieChart className="w-3 h-3 text-cyan-300/70 group-hover:text-cyan-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {/* Text with gradient underline effect */}
                          <span className="relative text-[13px] font-medium text-white/60 group-hover:text-white/90 transition-colors duration-300">
                            {prompt.text}
                            <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-gradient-to-r from-white/20 to-transparent group-hover:w-full transition-all duration-500"></span>
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced fade-out effect */}
                  <div className="absolute inset-y-0 right-0 w-32 pointer-events-none">
                    <div className="absolute inset-0 bg-gradient-to-l from-[#0A0A0A] via-[#0A0A0A]/95 to-transparent"></div>
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-transparent via-[#0A0A0A]/20 to-transparent opacity-50"></div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Chat Message Display - shown when messages exist */
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Message Area with Scrollbar */}
              <ScrollArea className="flex-1 px-4 py-4">
                <div className="max-w-4xl mx-auto space-y-6">
                  {messages.map((msg, index) => (
                    <div
                      key={index}
                      className="animate-pop-in"
                      style={{
                        animationDelay: `${index * 100}ms`,
                        animationFillMode: 'both',
                        marginBottom: '-20px'
                      }}
                    >
                      {msg.role === 'user' ? (
                        <div className={`${isMobile ? 'text-base' : 'text-lg'} font-bold text-white/95 mb-1`}>
                          {msg.content.text}
                        </div>
                      ) : msg.content.loadingPlaceholder ? (
                        <div className="mt-6 space-y-4">
                          <div className="flex flex-col space-y-4 pb-4">
                            <div className="text-white/70 flex items-center gap-2">
                              <div className="h-4 w-4 rounded-full border-2 border-white/10 border-t-white/30 animate-spin" />
                              Analyzing your request...
                            </div>
                          </div>
                        </div>
                      ) : (
                        <LoadingMessage
                          userQuery={messages[index - 1]?.content?.text || ""}
                          isComplete={!msg.content.isLoading}
                          symbols={msg.content.symbols}
                          finalContent={msg.content.aiAnalysis || msg.content.text}
                          marketData={msg.content.marketData}
                          isLoading={msg.content.isLoading}
                          symbolTypes={msg.content.symbolTypes}
                          isGeneralMessage={msg.content.isGeneralMessage}
                          onRegenerate={async () => {
                            // Get the user query from the previous message
                            const userQuery = messages[index - 1]?.content?.text || "";
                            if (!userQuery) return;

                            // Check if agent is selected
                            if (!selectedAgent) {
                              toast({
                                title: "Agent Required",
                                description: "Please select a trading agent before regenerating.",
                                variant: "destructive"
                              });
                              return;
                            }

                            // Decrement the counter using the hook - this counts as a message
                            const result = await incrementMessagesUsed();

                            if (!result?.success) {
                              toast({
                                title: "Message limit error",
                                description: result?.message || "Failed to regenerate. Please try again.",
                                variant: "destructive"
                              });
                              return;
                            }

                            // Use the NEW SIMPLIFIED FLOW for regeneration
                            try {
                              console.log('ChatInterface - Regenerating with new flow, agent:', selectedAgent);

                              // Step 1: Extract tickers from user message
                              const { data: symbolsResponse, error: symbolsError } = await supabase.functions.invoke('extract-symbols', {
                                body: { message: userQuery }
                              });

                              if (symbolsError) {
                                throw new Error(`Symbol extraction error: ${symbolsError.message}`);
                              }

                              const symbols = symbolsResponse?.symbols || [];
                              console.log('ChatInterface - Regenerate extracted symbols:', symbols);

                              // Step 2: Run selected agent on all extracted symbols
                              let agentResults: any[] = [];
                              if (symbols.length > 0) {
                                for (const symbol of symbols) {
                                  try {
                                    const { data: agentResponse, error: agentError } = await supabase.functions.invoke('agent-runner', {
                                      body: {
                                        agentId: selectedAgent,
                                        symbol: symbol,
                                        saveRun: false
                                      }
                                    });

                                    if (!agentError && agentResponse) {
                                      agentResults.push({
                                        symbol: symbol,
                                        agentId: selectedAgent,
                                        agentName: selectedAgentName,
                                        ...agentResponse
                                      });
                                    }
                                  } catch (error) {
                                    console.error(`Error running agent for ${symbol}:`, error);
                                  }
                                }
                              }

                              // Step 2.5: Get market data for charts
                              let marketData = {};
                              let symbolTypes = {};

                              if (symbols.length > 0) {
                                try {
                                  const { data: chartResponse, error: chartError } = await supabase.functions.invoke('chat-ai', {
                                    body: {
                                      messages: [{
                                        role: 'user',
                                        content: userQuery
                                      }]
                                    }
                                  });

                                  if (!chartError && chartResponse) {
                                    marketData = chartResponse.marketData || {};
                                    symbolTypes = chartResponse.symbolTypes || {};
                                  }
                                } catch (error) {
                                  console.error('Error fetching market data:', error);
                                }
                              }

                              // Step 3: Send to Gemini with enhanced prompt
                              const geminiPrompt = `You are an experienced trading analyst having a conversation with a trader. The user asked: "${userQuery}"

I've run my custom trading agent analysis on the mentioned stocks, and here's what I found:

${agentResults.map(result => `
**${result.symbol} Analysis:**
- Signal: ${result.signal.toUpperCase()}
- Confidence Level: ${result.confidence}%
- Agent Reasoning: ${result.reasoning}
- Agent Name: ${result.agentName}
`).join('\n')}

Please provide a detailed, conversational response as if you're an experienced trader sharing insights. Include:
1. Your interpretation of the analysis
2. What the signal means in practical terms
3. Key factors to consider
4. Risk management thoughts
5. A conversational, engaging tone (like you're talking to a fellow trader)

Make it sound natural and insightful, not robotic. Share your perspective on what this analysis means for the trader.`;

                              const { data: geminiResponse, error: geminiError } = await supabase.functions.invoke('gemini-api', {
                                body: {
                                  prompt: geminiPrompt,
                                  temperature: 0.7,
                                  maxTokens: 1000
                                }
                              });

                              if (geminiError) {
                                throw new Error(`Gemini response error: ${geminiError.message}`);
                              }

                              // Extract text from Gemini response
                              const geminiText = geminiResponse?.data?.candidates?.[0]?.content?.parts?.[0]?.text ||
                                                geminiResponse?.response ||
                                                geminiResponse?.text ||
                                                'Sorry, I could not process your request.';

                              // Update the assistant message with new data
                              const assistantContent = {
                                text: geminiText,
                                marketData: marketData,
                                symbols: symbols,
                                symbolTypes: symbolTypes,
                                agentResults: agentResults,
                                selectedAgent: selectedAgent,
                                isSingleAgentAnalysis: true,
                                isLoading: false,
                                loadingPlaceholder: false,
                                isGeneralMessage: symbols.length === 0
                              };

                              // Update the current message
                              setMessages(prev => {
                                const updated = [...prev];
                                updated[index] = {
                                  ...updated[index],
                                  content: assistantContent
                                };
                                return updated;
                              });

                            } catch (error) {
                              console.error('Regenerate error:', error);
                              toast({
                                title: 'Regeneration Error',
                                description: error instanceof Error ? error.message : 'Failed to regenerate response. Please try again.',
                                variant: 'destructive'
                              });
                            }
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {/* Chat Input at Bottom - Desktop Only */}
              {!isMobile && (
                <div className="border-t border-white/[0.08] bg-[#0A0A0A] p-4">
                  <div className="max-w-4xl mx-auto">
                    <form onSubmit={handleSubmit} className="relative">
                      <div className="relative bg-[#141414]/40 backdrop-filter backdrop-blur-xl border-[0.5px] border-[#303035]/60 rounded-xl shadow-[0_8px_16px_rgba(0,0,0,0.2)]">
                        <textarea
                          ref={fileInputRef}
                          className="w-full min-h-[60px] text-white placeholder:text-white/40 resize-none p-4 pb-8 pr-[100px] focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent border-none text-sm"
                          value={message}
                          onChange={(e) => {
                            setMessage(e.target.value);
                            // Auto-resize the textarea
                            e.target.style.height = '40px';
                            const minHeight = 60;
                            const maxHeight = 120;
                            e.target.style.height = `${Math.min(maxHeight, Math.max(minHeight, e.target.scrollHeight))}px`;
                          }}
                          placeholder="Ask away..."
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSubmit(e as any);
                            }
                          }}
                          rows={1}
                          style={{ minHeight: '60px' }}
                        />
                        <div className="button-container">
                          <button
                            type="submit"
                            disabled={isLoading || !message.trim() || !selectedAgent}
                            className={`rounded-lg px-6 py-3 text-sm font-semibold flex items-center justify-center transition-all duration-200 ${
                              !selectedAgent || isLoading || !message.trim()
                                ? 'bg-white text-black cursor-not-allowed shadow-[0_0_15px_rgba(255,255,255,0.2)]'
                                : 'bg-white text-black shadow-[0_0_30px_rgba(255,255,255,0.6)] hover:shadow-[0_0_40px_rgba(255,255,255,0.8)] hover:bg-white border border-white/20'
                            }`}
                            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif"' }}
                          >
                            {isLoading ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                <span>Processing...</span>
                              </>
                            ) : (
                              <>
                                <ArrowUp className="w-4 h-4 mr-2" />
                                <span>Generate</span>
                              </>
                            )}
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              )}

            </div>
          )}
        </div>
      </div>
      </div>

      {/* Mobile Bottom Chat Input - Fixed Position */}
      {isMobile && messages.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-[#0A0A0A] via-[#0A0A0A]/95 to-transparent pb-4 pt-4">
          <div className="px-3">
            <form onSubmit={handleSubmit} className="relative">
              <div className="relative bg-[#141414]/40 backdrop-filter backdrop-blur-xl border-[0.5px] border-[#303035]/60 rounded-xl shadow-[0_8px_16px_rgba(0,0,0,0.2)]">
                <textarea
                  className="w-full min-h-[60px] text-white placeholder:text-white/40 resize-none p-4 pb-8 pr-[100px] focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent border-none text-base"
                  value={message}
                  onChange={(e) => {
                    setMessage(e.target.value);
                    // Auto-resize the textarea
                    e.target.style.height = '40px';
                    const minHeight = 60;
                    const maxHeight = 100;
                    e.target.style.height = `${Math.min(maxHeight, Math.max(minHeight, e.target.scrollHeight))}px`;
                  }}
                  placeholder="Ask away..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e as any);
                    }
                  }}
                  rows={1}
                  style={{ minHeight: '60px' }}
                />
                <div className="button-container">
                  <button
                    type="submit"
                    disabled={isLoading || !message.trim() || !selectedAgent}
                    className={`rounded-lg px-4 py-2 text-sm font-semibold flex items-center justify-center transition-all duration-200 ${
                      !selectedAgent || isLoading || !message.trim()
                        ? 'bg-white text-black cursor-not-allowed shadow-[0_0_15px_rgba(255,255,255,0.2)]'
                        : 'bg-white text-black shadow-[0_0_30px_rgba(255,255,255,0.6)] hover:shadow-[0_0_40px_rgba(255,255,255,0.8)] hover:bg-white border border-white/20'
                    }`}
                    style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif"' }}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        <span>Processing...</span>
                      </>
                    ) : (
                      <>
                        <ArrowUp className="w-4 h-4 mr-2" />
                        <span>Generate</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>

          {/* Mobile Agent Selection */}
          <div className="px-3 pt-2">
            <button
              onClick={() => setShowAgentDropdown(true)}
              className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.08] hover:border-white/[0.12] rounded-lg text-white/80 hover:text-white transition-all duration-200"
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif"' }}
            >
              <Bot className="w-3.5 h-3.5 text-white/60" />
              <span className="text-sm">{selectedAgentName || 'Select Agent'}</span>
              <ChevronDown className="w-3 h-3 text-white/60" />
            </button>
          </div>
        </div>
      )}


      <style>
        {`
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          /* Add subtle transition effects */
          textarea, button {
            transition: all 0.2s ease;
          }

          /* Ensure button container stays within the input box */
          .button-container {
            position: absolute;
            bottom: 12px;
            right: 8px;
            z-index: 10;
            max-width: calc(100% - 16px);
            max-height: calc(100% - 24px);
          }

          /* Additional spacing for welcome screen button */
          .welcome-button {
            bottom: 7px;
          }

          /* Ensure button text doesn't overflow */
          .button-container button {
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            height: 40px;
            padding-top: 8px;
            padding-bottom: 8px;
          }

          /* Mobile specific adjustments */
          @media (max-width: 640px) {
            .button-container {
              bottom: 14px;
            }

            .welcome-button {
              bottom: 0px;
            }

            .button-container button {
              padding-left: 8px;
              padding-right: 8px;
            }

            .button-container button span {
              font-size: 9px;
            }
          }

          /* Darker placeholder text on focus */
          textarea:focus::placeholder {
            color: rgba(255, 255, 255, 0.2);
          }
        `}
      </style>

      {/* Agent Dropdown - Clean and integrated */}
      {showAgentDropdown && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="w-full max-w-md mx-4">
            <div className="bg-[#1A1A1A] border border-white/[0.08] rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.4)] overflow-hidden">
              <div className="p-6 border-b border-white/[0.06]">
                <h3 className="text-lg font-medium text-white font-sans">Select Trading Agent</h3>
                <p className="text-white/60 text-sm font-sans mt-1">Choose an agent to analyze with</p>
              </div>
              <div className="max-h-64 overflow-y-auto p-4">
                {availableAgents.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-white/60 text-sm font-sans">No agents available</p>
                    <p className="text-white/40 text-xs font-sans mt-1">Create an agent in the Agent Builder</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {availableAgents.map((agent) => (
                      <button
                        key={agent.id}
                        onClick={() => {
                          setSelectedAgent(agent.id);
                          setSelectedAgentName(agent.name);
                          setShowAgentDropdown(false);
                        }}
                        className={`w-full text-left p-4 rounded-xl transition-all duration-200 ${
                          selectedAgent === agent.id
                            ? 'bg-green-500/10 border border-green-500/20 text-green-400'
                            : 'bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.06] hover:border-white/[0.08] text-white/80 hover:text-white'
                        }`}
                      >
                        <div className="font-medium text-sm font-sans">{agent.name}</div>
                        <div className="text-xs text-white/60 font-sans mt-1">{agent.description || 'Trading agent'}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              <div className="p-4 border-t border-white/[0.06] flex justify-end">
                <button
                  onClick={() => setShowAgentDropdown(false)}
                  className="px-4 py-2 text-white/60 hover:text-white transition-colors text-sm font-sans"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chat History Sidebar - Desktop */}
      {!isMobile && (
        <ChatHistorySidebar
          onNewChat={() => navigate('/chat/new')}
          onSelectChat={(chatId: string) => navigate(`/chat/${chatId}`)}
          currentChatId={currentChatId}
          refreshTrigger={sidebarRefreshTrigger}
        />
      )}

      {/* Mobile Chat History */}
      <MobileChatHistory
        isOpen={showMobileChatHistory}
        onClose={() => setShowMobileChatHistory(false)}
        onNewChat={() => {
          setShowMobileChatHistory(false);
          navigate('/chat/new');
        }}
        onSelectChat={(chatId: string) => {
          setShowMobileChatHistory(false);
          navigate(`/chat/${chatId}`);
        }}
      />
    </div>
  );
};



export default ChatInterface;