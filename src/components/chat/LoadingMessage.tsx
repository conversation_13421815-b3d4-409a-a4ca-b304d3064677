import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import {
  ChevronRight,
  Globe,
  BarChart3,
  Search,
  Database,
  Sparkles,
  FileText,
  Clock,
  TrendingDown,
  TrendingUp,
  LineChart,
  AlertTriangle,
  Target,
  Lightbulb,
  Calendar,
  Info,
  BarChart2,
  ArrowUp,
  ArrowDown,
  Minus,
  AlertCircle,
  Activity,
  BrainCircuit,
  X,
  ArrowUpDown,
  CandlestickChart
} from 'lucide-react';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';
import WaveText from '@/components/common/WaveText';
import { cn } from '@/lib/utils';
import axios from 'axios';
// Import the new component
import StockPriceDisplay from '@/components/StockPriceDisplay';
import html2canvas from 'html2canvas'; // Import html2canvas
import domtoimage from 'dom-to-image'; // Import domtoimage
import { Card } from '@/components/ui/card';
import { extractTradingStrategy } from '@/lib/parseStrategy';
import { callGeminiAPI } from '@/utils/geminiUtils';
import { callAuraAPI } from '@/utils/auraUtils';
import { callOsisAPI } from '@/utils/apiUtils';
import AgentResultsCard from './AgentResultsCard';
import SelectedAgentCard from './SelectedAgentCard';

// Add at the top of the file with other interfaces
type TimeframeOption = '1W' | '1M' | '3M' | '6M' | '1Y';

interface LoadingMessageProps {
  userQuery: string;
  isComplete?: boolean;
  onAnimationComplete?: () => void;
  selectedResponseTypes?: string[];
  symbols?: string[];
  finalContent?: string;
  marketData?: any;
  isLoading?: boolean;
  EXAAnalysis?: any; // Add EXA analysis data
  symbolTypes?: { [key: string]: string }; // Add this prop to receive symbol types from chat-ai
  isGeneralMessage?: boolean; // Add this prop
  onRegenerate?: () => Promise<void>; // Add this line
  userEmail?: string; // Add this line
  selectedAgent?: any; // Add this prop for selected agent
  agentResults?: any; // Add this prop for agent results
}

interface TradingSignalCardProps {
  signal: {
    price: string;
    change: string;
    volume: string;
    marketCap: string;
    peRatio: string;
    revenueGrowth: string;
    support: string[];
    resistance: string[];
    strategies: {
      conservative: { entry: string; exit: string; stop: string };
      moderate: { entry: string; exit: string; stop: string };
      aggressive: { entry: string; exit: string; stop: string };
    };
  };
  isLoading: boolean;
}

// Update the TickerDisplay component styling
const TickerDisplay = ({ symbol, type }: { symbol: string, type: 'STOCK' | 'CRYPTO' | 'MARKET' }) => {
  return (
    <div className="inline-flex items-center">
      <div className="px-2.5 py-1.5 bg-black/80 backdrop-blur-sm rounded border border-[#1A1A1C] flex items-center gap-2">
        {type === 'CRYPTO' && (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-[#00ff88]">
            <path d="M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m3.94.694-.347 1.969"></path>
          </svg>
        )}
        {type === 'STOCK' && (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-[#00ff88]">
            <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
            <path d="m19 9-5 5-4-4-3 3"></path>
          </svg>
        )}
        {type === 'MARKET' && (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-[#00ff88]">
            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
            <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
          </svg>
        )}
        <span className="text-sm font-medium text-white/90">{symbol}</span>
      </div>
    </div>
  );
};

const TradingSignalCard = ({ signal, isLoading }) => {
  const [displayValue, setDisplayValue] = useState(0);
  const [animationPhase, setAnimationPhase] = useState(0);

  const signalConfig = {
    'STRONG BUY': { color: '#00ff88', value: 95, label: 'Osis: Buy', bgGlow: 'rgba(0, 255, 136, 0.2)' },
    'WEAK BUY': { color: '#4ade80', value: 70, label: 'Osis: Buy', bgGlow: 'rgba(74, 222, 128, 0.15)' },
    'NEUTRAL': { color: '#d4d4d8', value: 50, label: 'NEUTRAL', bgGlow: 'rgba(212, 212, 216, 0.1)' },
    'WEAK SELL': { color: '#ff3a50', value: 30, label: 'Osis: Sell', bgGlow: 'rgba(255, 58, 80, 0.15)' },
    'STRONG SELL': { color: '#ff1a1a', value: 5, label: 'Osis: Sell', bgGlow: 'rgba(255, 26, 26, 0.2)' }
  };

  const config = signalConfig[signal] || signalConfig['NEUTRAL'];
  const isSellSignal = config.value < 50;

  useEffect(() => {
    if (!isLoading) {
      // Animation sequence
      const animationSequence = async () => {
        setAnimationPhase(1);
        await new Promise(resolve => setTimeout(resolve, 600));

        setAnimationPhase(2);
        let startValue = 0;
        const endValue = config.value;
        const duration = 1500;
        const startTime = Date.now();

        const animateValue = () => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);
          const eased = 1 - Math.pow(1 - progress, 3);
          const currentValue = Math.round(startValue + (endValue - startValue) * eased);
          setDisplayValue(currentValue);

          if (progress < 1) {
            requestAnimationFrame(animateValue);
          } else {
            setAnimationPhase(3);
          }
        };

        requestAnimationFrame(animateValue);
      };

      animationSequence();
    }
  }, [isLoading, config.value]);

  return (
    <div className="bg-[#0A0A0C] rounded-xl border border-[#1A1A1C] overflow-hidden shadow-lg relative w-full">
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center">
          <div className="h-6 w-6 rounded-full bg-[#121214] border border-[#1A1A1C] flex items-center justify-center mr-2.5">
            <Activity className="h-3 w-3 text-white/80" />
          </div>
          <h3 className="text-xs font-medium text-white">Trading Signal</h3>
        </div>

        {!isLoading && animationPhase > 0 && (
          <div
            className="flex items-center gap-1 px-2.5 py-0.5 rounded-md transition-all duration-500"
            style={{
              backgroundColor: 'rgba(20, 20, 22, 0.6)',
              border: `1px solid ${isSellSignal ? 'rgba(255, 58, 80, 0.15)' : 'rgba(0, 255, 136, 0.15)'}`,
              opacity: animationPhase === 1 ? 0 : 1,
              transform: `translateY(${animationPhase === 1 ? '5px' : '0'})`,
              transition: 'opacity 0.5s ease, transform 0.5s ease'
            }}
          >
            <Sparkles className="h-2.5 w-2.5" style={{ color: isSellSignal ? '#ff3a50' : '#00ff88' }} />
            <span className="text-[10px] font-medium tracking-wide" style={{ color: isSellSignal ? '#ff3a50' : '#00ff88' }}>
              AI SIGNAL
            </span>
          </div>
        )}
      </div>

      <div className="relative w-full flex flex-col items-center justify-center px-5 pb-4 pt-1">
        {isLoading ? (
          <div className="w-full h-[80px] flex items-center justify-center">
            <div className="flex flex-col items-center space-y-3">
              <div className="h-8 w-8 rounded-full border-2 border-white/10 border-t-white/30 animate-spin"></div>
              <div className="text-xs text-white/50">Analyzing market data...</div>
            </div>
          </div>
        ) : (
          <div className="w-full flex flex-col items-center">
            <div className="relative h-12 flex items-center justify-center my-2">
              <div
                className="px-5 py-1.5 rounded-full transition-all duration-700"
                style={{
                  border: `1px solid ${isSellSignal ? 'rgba(255, 58, 80, 0.4)' : 'rgba(0, 255, 136, 0.4)'}`,
                  boxShadow: `0 0 ${animationPhase === 3 ? '10px' : '8px'} ${isSellSignal ? 'rgba(255, 58, 80, 0.2)' : 'rgba(0, 255, 136, 0.2)'}`,
                  opacity: animationPhase < 1 ? 0 : 1,
                  transform: `scale(${animationPhase < 2 ? 0.95 : 1})`,
                  transition: 'opacity 0.5s ease, transform 0.5s ease, box-shadow 1s ease'
                }}
              >
                <div className="flex items-center gap-2.5">
                  <span
                    className="text-xl font-semibold"
                    style={{
                      color: isSellSignal ? '#ff3a50' : '#00ff88',
                      textShadow: `0 0 8px ${isSellSignal ? 'rgba(255, 58, 80, 0.3)' : 'rgba(0, 255, 136, 0.3)'}`
                    }}
                  >
                    {animationPhase < 2 ? (
                      <span className="inline-flex items-center">
                        <span className="animate-pulse">.</span>
                        <span className="animate-pulse" style={{ animationDelay: '0.2s' }}>.</span>
                        <span className="animate-pulse" style={{ animationDelay: '0.4s' }}>.</span>
                      </span>
                    ) : (
                      `${displayValue}%`
                    )}
                  </span>
                  <span
                    className="text-xs font-medium tracking-wide uppercase"
                    style={{
                      color: isSellSignal ? '#ff3a50' : '#00ff88',
                      opacity: animationPhase < 2 ? 0.7 : 0.9,
                      transition: 'opacity 0.5s ease'
                    }}
                  >
                    {animationPhase >= 2 ? (
                      <WaveText
                        text={
                          animationPhase >= 2
                            ? 'Osis Trading Confidence'
                            : 'Analyzing'
                        }
                        delay={0.1}
                      />
                    ) : (
                      "Analyzing"
                    )}
                  </span>
                </div>
              </div>

              {animationPhase === 3 && (
                <div
                  className="absolute inset-0 rounded-full animate-pulse-subtle"
                  style={{
                    boxShadow: `0 0 15px ${isSellSignal ? 'rgba(255, 58, 80, 0.15)' : 'rgba(0, 255, 136, 0.15)'}`,
                    opacity: 0.4
                  }}
                />
              )}
            </div>

            {animationPhase >= 2 && (
              <div
                className="mt-1 text-xs text-white/70 leading-relaxed px-2 max-w-md text-center transition-all duration-1000"
                style={{
                  opacity: animationPhase < 3 ? 0 : 1,
                  transform: `translateY(${animationPhase < 3 ? '10px' : '0'})`,
                  transition: 'opacity 0.8s ease, transform 0.8s ease'
                }}
              >
                {signal === 'STRONG BUY' && (
                  'Multiple technical indicators suggest significant upside potential with strong momentum. Osis\'s confidence in this signal is high, but this is an AI-generated opinion, not a recommendation.'
                )}
                {signal === 'WEAK BUY' && (
                  'Positive indicators outweigh negatives, suggesting moderate upside potential. Osis\'s confidence in this signal is moderate, and this is an AI-generated opinion.'
                )}
                {signal === 'NEUTRAL' && (
                  'Mixed signals indicate balanced risk/reward at current levels.'
                )}
                {signal === 'WEAK SELL' && (
                  'Several indicators suggest caution, with potential downside risk. Osis\'s confidence in this signal is moderate, and this is an AI-generated opinion.'
                )}
                {signal === 'STRONG SELL' && (
                  'Multiple technical indicators point to significant downside risk with negative momentum. Osis\'s confidence in this signal is high, but this is an AI-generated opinion, not a recommendation.'
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// AI Signal Badge for the chart - Using the green style from the image
const AISignalBadge = ({ signal, animationPhase = 3 }) => {
  const isBullish = signal === 'STRONG BUY' || signal === 'WEAK BUY';
  const isBearish = signal === 'STRONG SELL' || signal === 'WEAK SELL';

  // Default to green AI signal as shown in the image
  const badgeColor = '#00ff88';
  const badgeBorder = 'rgba(0, 255, 136, 0.25)';
  const badgeShadow = 'rgba(0, 255, 136, 0.15)';

  return (
    <div
      className="flex items-center gap-1.5 px-3 py-1.5 rounded-md transition-all duration-500"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        backdropFilter: 'blur(4px)',
        border: `1px solid ${badgeBorder}`,
        boxShadow: `0 0 15px ${badgeShadow}`,
        opacity: animationPhase < 2 ? 0 : 1,
        transform: `translateY(${animationPhase < 2 ? '5px' : '0'})`,
        transition: 'opacity 0.5s ease, transform 0.5s ease'
      }}
    >
      <Sparkles className="h-4 w-4" style={{ color: badgeColor }} />
      <span className="text-xs font-medium tracking-wide" style={{ color: badgeColor }}>
        AI SIGNAL
      </span>
    </div>
  );
};

// Enhanced Chart component with integrated AI insights
const EnhancedChart = ({ symbol, displayData }) => {
  type TimeFrame = '1D' | '5D' | '1M' | 'YTD' | '1Y';
  type ChartType = 'candle' | 'line' | 'area';

  const [timeFrame, setTimeFrame] = useState<TimeFrame>('1M');
  const [chartType, setChartType] = useState<ChartType>('line');
  const [isChartLoading, setIsChartLoading] = useState(false);

  // Determine if this is a market ETF chart
  const isMarketETF = [
    'VTI', 'SPY', 'QQQ', 'IWM', 'EEM', 'TLT', 'GLD', 'USO', 'VIX', 'DIA',
    'XLF', 'XLE', 'XLK', 'XLV', 'XLI', 'XLP', 'XLY', 'XLU', 'XLRE', 'XLC'
  ].includes(symbol);
  const isCryptoMarket = symbol === 'BTC' && displayData?.type === 'MARKET';

  // Get the symbol type
  const getSymbolType = (): 'STOCK' | 'CRYPTO' | 'MARKET' => {
    if (isMarketETF) return 'MARKET';
    if (isCryptoMarket || symbol === 'BTC' || symbol === 'ETH') return 'CRYPTO';
    return 'STOCK';
  };

  // Custom chart title based on symbol
  const getChartTitle = () => {
    switch(symbol) {
      case 'VTI': return 'Total U.S. Market Overview';
      case 'SPY': return 'S&P 500 Market Overview';
      case 'QQQ': return 'NASDAQ/Tech Sector Overview';
      case 'IWM': return 'Small Cap Market Overview';
      case 'EEM': return 'Emerging Markets Overview';
      case 'TLT': return 'Bond Market Overview';
      case 'GLD': return 'Gold Market Overview';
      case 'USO': return 'Oil Market Overview';
      case 'VIX': return 'Market Volatility Overview';
      case 'DIA': return 'Dow Jones Overview';
      case 'XLF': return 'Financial Sector Overview';
      case 'XLE': return 'Energy Sector Overview';
      case 'XLK': return 'Technology Sector Overview';
      case 'XLV': return 'Healthcare Sector Overview';
      case 'XLI': return 'Industrial Sector Overview';
      case 'XLP': return 'Consumer Staples Overview';
      case 'XLY': return 'Consumer Discretionary Overview';
      case 'XLU': return 'Utilities Sector Overview';
      case 'XLRE': return 'Real Estate Sector Overview';
      case 'XLC': return 'Communication Services Overview';
      case 'BTC': return isCryptoMarket ? 'Crypto Market Overview' : 'Bitcoin (BTC/USD)';
      default: return `${symbol} Price Chart`;
    }
  };

  const handleTimeFrameChange = (tf: TimeFrame) => {
    setTimeFrame(tf);
  };

  const handleChartTypeChange = (type: ChartType) => {
    setChartType(type);
  };
  return (
    <div className="w-full bg-[#0A0A0C] rounded-lg overflow-hidden">
      <div className="p-4 border-b border-[#1A1A1C]/60">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white/90">{getChartTitle()}</h3>
            {isMarketETF && (
              <p className="text-xs text-white/50 mt-1">
                {symbol === 'VTI' && 'Tracks the entire U.S. stock market (Total Market Index)'}
                {symbol === 'SPY' && 'Tracks the S&P 500 large-cap index'}
                {symbol === 'QQQ' && 'Represents major tech and growth companies'}
                {symbol === 'IWM' && 'Tracks small-cap U.S. companies'}
                {symbol === 'EEM' && 'Represents emerging market performance'}
                {symbol === 'TLT' && 'Tracks long-term U.S. Treasury bonds'}
                {symbol === 'GLD' && 'Tracks gold market performance'}
                {symbol === 'USO' && 'Tracks oil market performance'}
                {symbol === 'VIX' && 'Tracks market volatility'}
                {symbol === 'DIA' && 'Tracks Dow Jones Industrial Average'}
                {symbol === 'XLF' && 'Tracks financial sector performance'}
                {symbol === 'XLE' && 'Tracks energy sector performance'}
                {symbol === 'XLK' && 'Tracks technology sector performance'}
                {symbol === 'XLV' && 'Tracks healthcare sector performance'}
                {symbol === 'XLI' && 'Tracks industrial sector performance'}
                {symbol === 'XLP' && 'Tracks consumer staples sector performance'}
                {symbol === 'XLY' && 'Tracks consumer discretionary sector performance'}
                {symbol === 'XLU' && 'Tracks utilities sector performance'}
                {symbol === 'XLRE' && 'Tracks real estate sector performance'}
                {symbol === 'XLC' && 'Tracks communication services sector performance'}
              </p>
            )}
            {isCryptoMarket && (
              <p className="text-xs text-white/50 mt-1">Bitcoin price as a proxy for overall crypto market</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center bg-[#1A1A1C] rounded-md p-1">
              {(['1D', '5D', '1M', 'YTD', '1Y'] as const).map((tf) => (
                <button
                  key={tf}
                  className={`px-2 py-0.5 text-xs font-medium rounded ${
                    timeFrame === tf
                      ? 'bg-[#2A2A2E] text-white'
                      : 'text-white/60 hover:text-white/80'
                  }`}
                  onClick={() => handleTimeFrameChange(tf)}
                >
                  {tf}
                </button>
              ))}
            </div>
            <div className="flex items-center bg-[#1A1A1C] rounded-md p-1">
              {(['line', 'candle'] as const).map((type) => (
                <button
                  key={type}
                  className={`px-2 py-0.5 text-xs font-medium rounded ${
                    chartType === type
                      ? 'bg-[#2A2A2E] text-white'
                      : 'text-white/60 hover:text-white/80'
                  }`}
                  onClick={() => handleChartTypeChange(type)}
                >
                  {type === 'line' ? 'Line' : 'Candle'}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="h-[550px] w-full relative border-t border-[#1A1A1C]/60">
        {isChartLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black z-10">
            <div className="w-10 h-10 mb-2">
              <div className="w-full h-full rounded-full border-2 border-t-transparent border-white/20 animate-spin"></div>
            </div>
            <div className="text-xs text-white/70 animate-pulse">Loading {timeFrame} chart data...</div>
          </div>
        )}

        <StockPriceDisplay
          symbol={symbol}
          render={(price, percentChange) => (
            <AdvancedStockChart
              symbol={symbol}
              showVolume={false}
              showControls={true}
              theme="dark"
              chartType="area"
              height={550}
              maxDataPoints={150}
              yAxisDraggable={true}
              defaultYAxisScale="compressed"
              // Removed price and percentChange props to fix linter error
            />
          )}
        />
      </div>
    </div>
  );
};

// Expert Opinions Card component
interface AnalystSignal {
  signal: string;
  reasoning: string;
}

interface AnalystSignals {
  [symbol: string]: AnalystSignal;
}

interface AnalystData {
  analyst_signals: Record<string, AnalystSignals>;
}

type AnalysisResults = {
  complete_analysis?: string;
  geminiMetaData?: string[];
  text?: string;
  title?: string;
  content?: string[];
  type?: 'header' | 'bullet' | 'text';
  data?: AnalystData;
} & Record<string, any>;

interface ExpertOpinionsCardProps {
  symbol: string;
  analysisResults?: AnalysisResults | null;
}

const ExpertOpinionsCard = ({ symbol, analysisResults }: ExpertOpinionsCardProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [chartPoints, setChartPoints] = useState({});

  // Map of agent IDs to display info
  const expertInfo = {
    'warren_buffett_agent': {
      name: 'Warren Buffett',
      photoUrl: 'https://gpcatalysis.blob.core.windows.net/gphostedcontent-prod/Buffett_500x500.jpg'
    },
    'cathie_wood_agent': {
      name: 'Cathie Wood',
      photoUrl: 'https://assets.arkinvest.com/media-8e522a83-1b23-4d58-a202-792712f8d2d3/d486f924-aaf8-4827-a6d9-d7f4f02722f3/ARK-Invest_Headshot_Cathie-Wood_668x668_V2.png'
    },
    'charlie_munger_agent': {
      name: 'Charlie Munger',
      photoUrl: 'https://imageio.forbes.com/specials-images/imageserve/88da1feb2b57450c4b7fe5a4423a1f8d/0x0.jpg?format=jpg&crop=668,669,x53,y16,safe&height=416&width=416&fit=bounds'
    },
    'bill_ackman_agent': {
      name: 'Bill Ackman',
      photoUrl: 'https://image.cnbcfm.com/api/v1/image/107296515-1694018658204-Ackman_Bill_NEW.jpeg?v=1694019175'
    },
    'ben_graham_agent': {
      name: 'Ben Graham',
      photoUrl: 'https://popularbio.com/wp-content/uploads/2020/05/Ben-Graham.jpg'
    }
  };

  useEffect(() => {
    if (analysisResults?.data?.analyst_signals && symbol) {
      // Generate chart points for each expert
      const points = {};
      Object.entries(analysisResults.data.analyst_signals).forEach(([agentId, signals]) => {
        const signalData = signals[symbol];
        if (signalData?.signal) {
          const signalValue = signalData.signal.toLowerCase();
          // Match the exact signal value for chart generation
          let chartSentiment;
          if (signalValue === 'bearish' || signalValue === 'strong sell' || signalValue === 'sell') {
            chartSentiment = 'bearish';
          } else if (signalValue === 'bullish' || signalValue === 'strong buy' || signalValue === 'buy') {
            chartSentiment = 'bullish';
          } else {
            chartSentiment = 'neutral';
          }
          points[agentId] = generateChartPoints(chartSentiment);
        }
      });
      setChartPoints(points);
      setIsLoading(false);
    }
  }, [analysisResults, symbol]);

  // Chart generation function
  const generateChartPoints = (sentiment) => {
        const points = 25;
        const chartData = [];

        // Ensure we go from 0 to 1000 (full width)
        const fullWidth = 1000;

    if (sentiment === 'bullish') {
          const baseValue = 35;
          for (let i = 0; i <= points; i++) {  // Changed to <= to include the last point
        const position = i / points;
            const baseHeight = baseValue + (50 * position);
            const variation = Math.sin(i * 0.8) * 3;
            // Calculate x to ensure the last point is at the full width
            const x = i === points ? fullWidth : i * (fullWidth/points);
            chartData.push([x, 200 - (baseHeight + variation) * 2]);
      }
    } else if (sentiment === 'bearish') {
          const baseValue = 85;
          for (let i = 0; i <= points; i++) {  // Changed to <= to include the last point
        const position = i / points;
            const baseHeight = baseValue - (50 * position);
            const variation = Math.sin(i * 0.8) * 3;
            // Calculate x to ensure the last point is at the full width
            const x = i === points ? fullWidth : i * (fullWidth/points);
            chartData.push([x, 200 - (baseHeight + variation) * 2]);
      }
    } else {
      const baseValue = 50;
          for (let i = 0; i <= points; i++) {  // Changed to <= to include the last point
            const variation = Math.sin(i * 1.8) * 20 + Math.cos(i * 1.2) * 10;
            // Calculate x to ensure the last point is at the full width
            const x = i === points ? fullWidth : i * (fullWidth/points);
            chartData.push([x, 200 - (baseValue + variation) * 2]);
          }
        }
        return chartData;
  };

  // Helper to create a smooth curve SVG path
  const createSmoothPath = (points) => {
    if (!points || points.length < 2) return "";
    let path = `M ${points[0][0]},${points[0][1]}`;
    for (let i = 0; i < points.length - 1; i++) {
      const x1 = points[i][0];
      const y1 = points[i][1];
      const x2 = points[i+1][0];
      const y2 = points[i+1][1];
      const cpx1 = x1 + (x2 - x1) / 3;
      const cpy1 = y1;
      const cpx2 = x1 + 2 * (x2 - x1) / 3;
      const cpy2 = y2;
      path += ` C ${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`;
    }
    return path;
  };

  if (!analysisResults?.data?.analyst_signals || !symbol) {
    return (
      <div className="bg-[#0A0A0C] rounded-xl border border-[#1A1A1C]/70 overflow-hidden shadow-lg h-full flex flex-col relative">
        <div className="flex flex-col items-center justify-center h-full w-full p-8">
          <div className="text-[#8b5cf6]/70 text-sm font-medium">No expert analysis available</div>
      </div>
    </div>
  );
  }

  if (isLoading) {
    return (
      <div className="bg-[#0A0A0C] rounded-xl border border-[#1A1A1C]/70 overflow-hidden shadow-lg h-full flex flex-col relative">
        <div className="flex flex-col items-center justify-center h-full w-full p-8">
          <div className="h-10 w-10 border-4 border-[#1A1A1C] border-t-white/30 rounded-full animate-spin mb-4"></div>
          <div className="text-white/70 text-sm font-medium">Loading expert perspectives...</div>
      </div>
    </div>
  );
  }

  // Add custom scrollbar styles
  const scrollbarStyles = `
    /* Hide scrollbar for Chrome, Safari and Opera */
    .hide-scrollbar::-webkit-scrollbar {
      display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    .hide-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
    }
    /* Ensure the column takes full width */
    .expert-column {
      width: 100%;
      margin-right: 0;
      padding-right: 0;
    }
  `;

  return (
    <div className="bg-[#0A0A0C] rounded-xl border border-[#1A1A1C]/70 overflow-hidden shadow-lg h-full flex flex-col relative expert-column">
      <style>{scrollbarStyles}</style>
      <div className="flex-1 overflow-y-auto hide-scrollbar w-full">
        {Object.entries(analysisResults.data.analyst_signals).map(([agentId, signals]) => {
          if (!expertInfo[agentId] || !signals[symbol]?.signal) return null;

          const signalValue = signals[symbol].signal.toLowerCase();
          // Match the exact signal value for display
          let sentiment;
          if (signalValue === 'bearish' || signalValue === 'strong sell' || signalValue === 'sell') {
            sentiment = 'bearish';
          } else if (signalValue === 'bullish' || signalValue === 'strong buy' || signalValue === 'buy') {
            sentiment = 'bullish';
          } else {
            sentiment = 'neutral';
          }

          const chartColor = sentiment === 'bullish' ? '#00ff88' : sentiment === 'bearish' ? '#ff3a50' : '#d4d4d8';
          const points = chartPoints[agentId];
    const smoothPath = createSmoothPath(points);
          const areaPath = points ? `${smoothPath} L ${1000},${200} L ${0},${200} Z` : "";

    return (
            <div key={agentId} className="relative border-b border-[#1A1A1C]/60 last:border-0">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 mr-2 border border-white/10">
                    <img src={expertInfo[agentId].photoUrl} alt={expertInfo[agentId].name} className="w-full h-full object-cover"/>
            </div>
                  <span className="text-xs font-medium text-white/80">{expertInfo[agentId].name}</span>
          </div>
          <div className={`px-3 py-0.5 h-5 flex items-center rounded font-semibold ${
            sentiment === 'bullish'
              ? 'bg-[#132215] text-[#00ff88] border border-[#00ff88]/20'
              : sentiment === 'bearish'
                ? 'bg-[#221315] text-[#ff3a50] border border-[#ff3a50]/20'
                : 'bg-[#18181B] text-white/80 border border-white/10'
          }`}>
                  <span className="text-[10px]">{signals[symbol].signal.toUpperCase()}</span>
          </div>
        </div>
        <div className="w-full h-20 overflow-hidden relative">
                <svg viewBox="0 0 1000 200" className="w-full h-full relative z-10" preserveAspectRatio="none">
            <defs>
                    <linearGradient id={`gradient-${agentId}`} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={`${chartColor}30`} />
                      <stop offset="100%" stopColor="transparent" />
              </linearGradient>
            </defs>
                  <path d={areaPath} fill={`url(#gradient-${agentId})`} strokeWidth="0" opacity={1} />
            <path
              d={smoothPath}
              fill="none"
              stroke={chartColor}
              strokeWidth="3.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              filter={`drop-shadow(0 0 3px ${chartColor}80)`}
            />
          </svg>
        </div>
      </div>
    );
              })}
      </div>
    </div>
  );
};

// Update the AnalysisCard component to be simpler without boxes
const AnalysisCard = ({ title, content, icon }) => (
  <div className="mb-6">
    <div className="flex items-center gap-2 mb-3">
      <div className="w-8 h-8 rounded-full bg-[#161A23] border border-[#1E2233] flex items-center justify-center">
        {icon}
      </div>
      <h3 className="text-base font-medium text-white/90">{title}</h3>
    </div>
    <div
      className="prose prose-invert max-w-none text-sm pl-10"
      dangerouslySetInnerHTML={{ __html: content }}
    />
  </div>
);

// Clean AI Response Component for LoadingMessage.tsx
const AIResponse = ({ content }) => {
  return (
    <div className="prose prose-invert max-w-none w-full">
      <div
        className="text-base font-bold text-white leading-relaxed w-full"
        style={{ fontFamily: "SF Pro Display, Helvetica, Arial, sans-serif" }}
        dangerouslySetInnerHTML={{ __html: formatText(content) }}
      />
    </div>
  );
};

// Function to filter out cash/portfolio references from the reasoning
const filterPortfolioReferences = (text: string): string => {
  if (!text) return "";

  // Remove sentences containing these phrases
  const phrasesToFilter = [
    "cash available",
    "could purchase",
    "buy up to",
    "purchase up to",
    "shares",
    "position",
    "portfolio",
    "$100,000"
  ];

  // Split text into sentences and filter out those containing the phrases
  const sentences = text.split(/(?<=[.!?])\s+/);
  const filteredSentences = sentences.filter(sentence => {
    const lowerSentence = sentence.toLowerCase();
    return !phrasesToFilter.some(phrase => lowerSentence.includes(phrase.toLowerCase()));
  });

  return filteredSentences.join(' ');
};

// Helper function to format numbers
const formatNumber = (value: string): string => {
  // Check if the value is a number with a decimal point
  const match = value.match(/(-?\d+\.?\d*)/);
  if (!match) return value;

  const num = parseFloat(match[0]);

  // Format percentages (numbers followed by %)
  if (value.includes('%')) {
    const formatted = num.toFixed(2);
    return value.replace(match[0], formatted.endsWith('.00') ? formatted.slice(0, -3) : formatted);
  }

  // Format regular numbers - use integers for bullet points
  if (value.startsWith('•') || value.startsWith('-')) {
    return value.replace(match[0], Math.round(num).toString());
  }

  // Format regular numbers with 2 decimals, removing .00 if present
  const formatted = num.toFixed(2);
  return value.replace(match[0], formatted.endsWith('.00') ? formatted.slice(0, -3) : formatted);
};

// Helper function to format the consolidated analysis
const formatConsolidatedAnalysis = (analysisText) => {
  if (!analysisText) return { sections: [] };

  // Split the analysis into sections based on the headings

  // Extract different parts using regex
  const summaryMatch = analysisText.match(/###\s*Comprehensive Financial Summary for (.+?)(?=\s*\*\*Technical Analysis)/s);
  const summary = summaryMatch ? summaryMatch[0] : '';
  const companyName = summaryMatch && summaryMatch[1] ? summaryMatch[1].trim() : '';

  // Extract major sections with regex
  const extractSection = (title, text) => {
    const regex = new RegExp(`\\*\\*${title}\\*\\*(.+?)(?=\\*\\*\\w+\\s+\\w+\\*\\*|$)`, 's');
    const match = text.match(regex);
    return match ? match[1].trim() : '';
  };

  // Extract all analysis sections
  const sections = [
    {
      title: 'Technical Analysis',
      content: extractSection('Technical Analysis', analysisText),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
          <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
          <path d="M18 17V9"></path>
          <path d="M13 17V5"></path>
          <path d="M8 17v-3"></path>
        </svg>
      )
    },
    {
      title: 'Sentiment Analysis',
      content: extractSection('Sentiment Analysis', analysisText),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M8 15s1.5 2 4 2 4-2 4-2"></path>
          <line x1="9" y1="9" x2="9.01" y2="9"></line>
          <line x1="15" y1="9" x2="15.01" y2="9"></line>
        </svg>
      )
    },
    {
      title: 'Fundamental Analysis',
      content: extractSection('Fundamental Analysis', analysisText),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
          <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
        </svg>
      )
    },
    {
      title: 'Warren Buffett\'s Perspective',
      content: extractSection('Value Investing Perspective', analysisText),
      icon: (
        <div className="relative w-5 h-5 rounded-full overflow-hidden group flex-shrink-0">
          {/* Glow effect layers */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 animate-pulse"></div>
          <div className="absolute inset-0 bg-black/40 mix-blend-overlay"></div>

          {/* Hover glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/0 to-purple-600/0 group-hover:from-blue-600/30 group-hover:to-purple-600/30 transition-all duration-300"></div>

          {/* Image */}
          <img
            src="https://gpcatalysis.blob.core.windows.net/gphostedcontent-prod/Buffett_500x500.jpg"
            alt="Warren Buffett"
            className="w-full h-full object-cover rounded-full"
            style={{ objectFit: 'cover' }}
          />

          {/* Border glow */}
          <div className="absolute inset-0 rounded-full border border-white/10 group-hover:border-white/20 transition-colors duration-300 shadow-[0_0_10px_rgba(255,255,255,0.1)]"></div>

          {/* Extra outer glow */}
          <div className="absolute -inset-0.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-blue-500/10 to-purple-500/10 blur-sm"></div>
        </div>
      )
    },
    {
      title: 'Cathie Wood\'s Perspective',
      content: extractSection('Growth Investing Perspective', analysisText),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
          <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
        </svg>
      )
    },
    {
      title: 'Risks and Challenges',
      content: extractSection('Potential Risks', analysisText),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
          <line x1="12" y1="9" x2="12" y2="13"></line>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
      )
    },
    {
      title: 'Market Outlook',
      content: extractSection('Market Outlook', analysisText),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M12 16a4 4 0 0 1-4-4 4 4 0 0 1 8 0 4 4 0 0 1-4 4z"></path>
          <path d="M12 8V4"></path>
          <path d="M9.17 9.17 6.34 6.34"></path>
          <path d="M16 12h4"></path>
          <path d="M14.83 9.17l2.83-2.83"></path>
        </svg>
      )
    }
  ];

  // Get the overall signal from the text
  const getOverallSignal = (text) => {
    const signalMatch = text.match(/\*\*Overall Signal:\*\*\s*([A-Za-z]+)/);
    return signalMatch ? signalMatch[1].toLowerCase() : 'neutral';
  };

  const overallSignal = getOverallSignal(analysisText);

  return {
    companyName,
    summary,
    sections,
    overallSignal
  };
};

// New interfaces for structured analysis
interface AnalysisSection {
  title: string;
  content: string[];
  type: 'header' | 'bullet' | 'text';
}

// New interface for managing typing state
interface TypingState {
  isTyping: boolean;
  currentSectionIndex: number;
  displayedContent: string[];
}

// Global typing state to coordinate across sections
let globalTypingState: TypingState = {
  isTyping: false,
  currentSectionIndex: 0,
  displayedContent: []
};

// Helper function to format text with minimal styling
const formatText = (text: string): string => {
  if (!text) return '';

  // Filter out AI disclaimers and investment advice warnings
  const disclaimerPatterns = [
    /please remember that i am an ai.*?financial advisor\./gi,
    /this analysis is for (?:educational|informational) purposes only.*?/gi,
    /always (?:do your own research|conduct your own research|consult|speak).*?(?:financial advisor|investment advisor|professional).*?\./gi,
    /disclaimer:.*?(?:financial advisor|investment advisor|professional).*?\./gi,
    /this is not financial advice.*?\./gi,
    /this information should not be construed as financial advice.*?\./gi,
    /past performance.*?future results.*?\./gi
  ];

  // Remove disclaimer sentences
  disclaimerPatterns.forEach(pattern => {
    text = text.replace(pattern, '');
  });

  const baseStyle = 'text-base leading-normal text-white break-words';
  const headerStyle = 'text-lg font-semibold text-white mt-6 mb-3';
  const fontFamily = 'font-family: SF Pro Display, -apple-system, system-ui, sans-serif';

  // First, protect bullet points by replacing them with a temporary marker
  let processedText = text.replace(/(?:^|\n)\s*\*\s+/g, '§BULLET§');

  // Handle basic paragraph structure - ensure proper paragraph breaks for Gemini responses
  // First make sure consecutive newlines create proper paragraph breaks with moderate margins
  processedText = processedText.replace(/\n\n+/g, `</p><p class="${baseStyle} mb-3" style="${fontFamily}">`);

  // Ensure single newlines are preserved as breaks with moderate vertical space
  processedText = processedText.replace(/\n(?!\n)/g, '<br class="leading-normal">');

  // Handle cases where response might start with a newline
  processedText = processedText.replace(/^<br[^>]*>/, '');

  // Wrap in paragraph if needed
  if (!processedText.startsWith('<h') && !processedText.startsWith('<p')) {
    processedText = `<p class="${baseStyle}" style="${fontFamily}">${processedText}</p>`;
  }

  // Section headers (###)
  processedText = processedText.replace(
    /###\s+(.*?)(?=<br|<\/p>|$)/g,
    `<h3 class="${headerStyle}" style="${fontFamily}">$1</h3>`
  );

  // Bold text (**text**)
  processedText = processedText.replace(
    /\*\*(.*?)\*\*/g,
    '<span class="font-semibold text-white">$1</span>'
  );

  // Italic text (*text*) - but not bullet points
  processedText = processedText.replace(
    /(?<!\*)\*([^\*]+)\*(?!\*)/g,
    '<em class="italic text-white/90">$1</em>'
  );

  // Restore bullet points and format them with moderate spacing
  processedText = processedText.replace(
    /§BULLET§(.*?)(?=<br|<\/p>|§BULLET§|$)/g,
    `<div class="flex gap-2 py-1">
      <span class="text-white/90">•</span>
      <span class="${baseStyle} flex-1 break-words" style="${fontFamily}">$1</span>
    </div>`
  );

  // Special handling for code blocks and technical content
  processedText = processedText.replace(
    /```([a-zA-Z]*)\n([\s\S]*?)\n```/g,
    `<div class="bg-white/10 p-3 rounded-md my-3 overflow-x-auto">
      <pre class="text-sm text-white/90"><code>$2</code></pre>
    </div>`
  );

  // Clean up any remaining tags
  if (!processedText.endsWith('</p>') && !processedText.endsWith('</div>') && !processedText.endsWith('</h3>')) {
    processedText += '</p>';
  }

  return processedText;
};

// Add shared state for user scroll control at file level
let userHasManuallyScrolled = false;

// New component for rendering analysis sections
const AnalysisSection: React.FC<{
  section: AnalysisSection;
  index: number;
  totalSections: number;
  onTypingComplete: () => void;
  shouldStartTyping: boolean;
}> = ({
  section,
  index,
  totalSections,
  onTypingComplete,
  shouldStartTyping
}) => {
  const [typedContent, setTypedContent] = useState<string[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isDone, setIsDone] = useState(false);

  useEffect(() => {
    if (shouldStartTyping && !isTyping && !isDone) {
      setIsTyping(true);
      const cleanup = typeText();
      return cleanup;
    }
  }, [shouldStartTyping]);

  const typeText = () => {
    let currentIndex = 0;
    const content = [...section.content];
    const typingInterval = setInterval(() => {
      if (currentIndex < content.length) {
        setTypedContent(prev => [...prev, content[currentIndex]]);
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        setIsTyping(false);
        setIsDone(true);
        onTypingComplete();
      }
    }, 50); // Faster typing speed for better UX

    return () => clearInterval(typingInterval);
  };

  // Format the content based on section type
  const getFormattedContent = () => {
    if (section.type === 'header') {
      return (
        <>
          <h3 className="text-[16px] text-white/90 mt-6 mb-3" style={{ fontFamily: 'SF Pro Display, -apple-system, system-ui, sans-serif' }}>{section.title}</h3>
          <div className="prose prose-invert max-w-none">
            {typedContent.map((line, i) => (
              <p key={i} className="text-[13px] leading-relaxed text-white/70" style={{ fontFamily: 'SF Pro Display, -apple-system, system-ui, sans-serif' }} dangerouslySetInnerHTML={{ __html: formatText(line) }} />
            ))}
          </div>
        </>
      );
    } else {
      return (
        <div className="prose prose-invert max-w-none">
          {typedContent.map((line, i) => (
            <p key={i} className="text-[13px] leading-relaxed text-white/70" style={{ fontFamily: 'SF Pro Display, -apple-system, system-ui, sans-serif' }} dangerouslySetInnerHTML={{ __html: formatText(line) }} />
          ))}
        </div>
      );
    }
  };

  return (
    <div className="mb-4">
      {getFormattedContent()}
      {isTyping && (
        <div className="typing-indicator">
          <span className="dot"></span>
          <span className="dot"></span>
          <span className="dot"></span>
        </div>
      )}
    </div>
  );
};

// Function to parse analysis content into sections
const parseAnalysisContent = (content: string): AnalysisSection[] => {
  if (!content) return [];

  const sections: AnalysisSection[] = [];
  const lines = content.split('\n');

  let currentSection: AnalysisSection | null = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Check if this is a header line
    if (line.startsWith('###')) {
      // If we have a current section, push it to sections
      if (currentSection) {
        sections.push(currentSection);
      }

      // Start a new section
      currentSection = {
        title: line.replace(/^###\s*/, ''),
        content: [],
        type: 'header'
      };
    }
    // Check if this is a bullet point
    else if (line.startsWith('-') || line.startsWith('*')) {
      if (!currentSection) {
        currentSection = {
          title: 'Analysis',
          content: [],
          type: 'bullet'
        };
      }

      if (currentSection.type !== 'bullet') {
        currentSection.content.push(line);
      } else {
        // Add to existing bullet list
        currentSection.content.push(line);
      }
    }
    // Regular text
    else if (line.length > 0) {
      if (!currentSection) {
        currentSection = {
          title: 'Analysis',
          content: [],
          type: 'text'
        };
      }

      currentSection.content.push(line);
    }
  }

  // Don't forget the last section
  if (currentSection) {
    sections.push(currentSection);
  }

  return sections;
};

// Add CSS for typing animation
const typingAnimationStyles = `
  .typing-indicator {
    display: inline-flex;
    align-items: center;
    margin-left: 4px;
  }

  .typing-indicator .dot {
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    margin: 0 2px;
    animation: typing-dot 1.4s infinite ease-in-out both;
  }

  .typing-indicator .dot:nth-child(1) {
    animation-delay: 0s;
  }

  .typing-indicator .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-indicator .dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing-dot {
    0%, 80%, 100% {
      transform: scale(0.7);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

// Add ActionButtons component before LoadingMessage component
const ActionButtons = ({ onCopy, onRegenerate, analysisText }) => {
  const [copySuccess, setCopySuccess] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);

  const handleCopy = async () => {
    try {
      // Use the onCopy prop which will call the parent component's handleCopy function
      await onCopy();
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 1500);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleRegenerate = async () => {
    try {
      setIsRegenerating(true);
      await onRegenerate();
    } catch (error) {
      console.error('Failed to regenerate:', error);
    } finally {
      setIsRegenerating(false);
    }
  };

  return (
    <div className="flex items-center gap-3 pt-4 border-t border-[#1A1A1C]/60">
      <div className="relative group">
        <button
          onClick={handleCopy}
          className="p-2 rounded-lg bg-[#1A1A1C] hover:bg-[#2A2A2E] transition-colors duration-200 text-white/80 hover:text-white/90 relative"
          aria-label="Copy message"
        >
          {copySuccess ? (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-[#00ff88] transition-all duration-200">
              <polyline points="20 6 9 17 4 12" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
              <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
              <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
            </svg>
          )}
        </button>
        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-[#2A2A2E] text-white text-xs rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
          Copy
        </div>
      </div>

      <div className="relative group">
        <button
          onClick={handleRegenerate}
          disabled={isRegenerating}
          className={`p-2 rounded-lg bg-[#1A1A1C] hover:bg-[#2A2A2E] transition-colors duration-200 text-white/80 hover:text-white/90 ${isRegenerating ? 'animate-spin' : ''}`}
          aria-label="Regenerate response"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
            <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
            <path d="M3 3v5h5" />
            <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
            <path d="M16 16h5v5" />
          </svg>
        </button>
        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-[#2A2A2E] text-white text-xs rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
          Regenerate response
        </div>
      </div>
    </div>
  );
};

const LoadingMessage: React.FC<LoadingMessageProps> = ({
  userQuery,
  isComplete,
  onAnimationComplete,
  selectedResponseTypes,
  symbols,
  finalContent,
  marketData,
  isLoading,
  EXAAnalysis,
  symbolTypes,
  isGeneralMessage,
  onRegenerate,
  userEmail,
  selectedAgent,
  agentResults
}): JSX.Element => {
  const [data, setData] = useState(marketData);
  const [copySuccess, setCopySuccess] = useState(false);
  const [shareSuccess, setShareSuccess] = useState(false);
  useEffect(() => {
    setData(marketData);
  }, [marketData]);

  // Add state for typing indices at the top level
  const [typingIndices, setTypingIndices] = useState<{ [key: string]: number }>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const [userHasScrolled, setUserHasScrolled] = useState(false);

  // Simplified scroll handling
  useEffect(() => {
    const handleScroll = () => {
      const scrolledPosition = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const fullHeight = document.documentElement.scrollHeight;

      // If user has scrolled up more than 100px from bottom, consider it a manual scroll
      if (fullHeight - (scrolledPosition + windowHeight) > 100) {
        setUserHasScrolled(true);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Auto-scroll effect
  useEffect(() => {
    if (!userHasScrolled && containerRef.current) {
      const scrollToBottom = () => {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth'
        });
      };

      const timeoutId = setTimeout(scrollToBottom, 50);
      return () => clearTimeout(timeoutId);
    }
  }, [typingIndices, userHasScrolled]);

  const symbol = symbols[0] || "";
  const [progress, setProgress] = useState(0);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showMarketData, setShowMarketData] = useState(false);
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [visibleSections, setVisibleSections] = useState<string[]>([]);
  const [analysisStage, setAnalysisStage] = useState('initializing');
  const [parsedContent, setParsedContent] = useState<{[key: string]: string}>({
    technical_analysis: "",
    sentiment_analysis: ""
  });
  const [showConfirmation, setShowConfirmation] = useState(true);
  const [chartError, setChartError] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);

  // Updated state to hold all agent analyses
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults>({});
  const [consolidatedAnalysis, setConsolidatedAnalysis] = useState("");
  const [loading, setLoading] = useState(true);
  const [analysisSections, setAnalysisSections] = useState<AnalysisSection[]>([]);

  // Add state to track which section is currently typing
  const [currentTypingSection, setCurrentTypingSection] = useState(0);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedText, setStreamedText] = useState("");

  // Add at the top with other state variables
  const [chartInitiated, setChartInitiated] = useState(false);

  // Add state to track regeneration attempts
  const [regenerationCount, setRegenerationCount] = useState(0);
  const [isAutoRegenerating, setIsAutoRegenerating] = useState(false);
  const MAX_AUTO_REGENERATIONS = 2; // Maximum number of automatic regenerations to prevent infinite loops
  const REGENERATION_TIMEOUT = 15000; // 15 seconds timeout for regeneration

  // Add state for trading strategy
  const [tradingStrategy, setTradingStrategy] = useState(null);

  // Add function to check if chart is initiated
  const advancedStockChartInitiated = () => {
    return chartInitiated;
  };

  // Add useEffect to trigger analysis
  useEffect(() => {
    if (isLoading) {
      fetchAllAnalyses();
    }
  }, [isLoading]);

  // Generate confirmation message based on user query
  const getConfirmationMessage = () => {
    if (!userQuery) return "I'll analyze this for you right away.";

    if (userQuery.toLowerCase().includes('scan') && symbols.length > 0) {
      return `I'll scan ${symbols.join(', ')} and provide a comprehensive analysis.`;
    } else if (userQuery.toLowerCase().includes('analyze') && symbols.length > 0) {
      return `Analyzing ${symbols.join(', ')} for you. I'll provide detailed insights shortly.`;
    } else if (userQuery.toLowerCase().includes('compare') && symbols.length > 1) {
      return `I'll compare ${symbols.join(' and ')} and highlight the key differences.`;
    } else if (symbols.length > 0) {
      return `Analyzing ${symbols.join(', ')} for you. I'll have insights ready in a moment.`;
    } else {
      return "I'm working on your request. This will just take a moment.";
    }
  };

  // Get market data from Polygon only
  const getMarketData = () => {
    if (!marketData || !symbol) {
      return {
        price: "0.00",
        change: "0.00%",
        volume: "N/A",
        marketCap: null,
        peRatio: null,
        revenueGrowth: null,
        support: [],
        resistance: [],
        type: 'STOCK',
        strategies: {
          conservative: { entry: null, exit: null, stop: null },
          moderate: { entry: null, exit: null, stop: null },
          aggressive: { entry: null, exit: null, stop: null }
        }
      };
    }

    let symbolData;
    if (Array.isArray(marketData)) {
      symbolData = marketData.find(item => item.symbol === symbol);
    } else {
      symbolData = marketData[symbol];
    }

    if (!symbolData) {
      return {
        price: "0.00",
        change: "0.00%",
        volume: "N/A",
        marketCap: null,
        peRatio: null,
        revenueGrowth: null,
        support: [],
        resistance: [],
        type: 'STOCK',
        strategies: {
          conservative: { entry: null, exit: null, stop: null },
          moderate: { entry: null, exit: null, stop: null },
          aggressive: { entry: null, exit: null, stop: null }
        }
      };
    }

    // Get decimal places from the price
    const priceStr = (symbolData.price || symbolData.lastPrice || "0.00").toString();
    const decimalPlaces = (priceStr.split('.')[1] || '').length;

    return {
      price: symbolData.price?.toFixed(decimalPlaces) || symbolData.lastPrice?.toFixed(decimalPlaces) || "0.00",
      change: symbolData.percentChange ? `${symbolData.percentChange > 0 ? '+' : ''}${symbolData.percentChange}%` : "0.00%",
      volume: symbolData.volume ? (symbolData.volume >= 1000000 ? `${(symbolData.volume/1000000).toFixed(1)}M` : symbolData.volume) : "N/A",
      marketCap: symbolData.marketCap || null,
      peRatio: symbolData.peRatio || null,
      revenueGrowth: symbolData.revenueGrowth || null,
      support: symbolData.support || [],
      resistance: symbolData.resistance || [],
      type: symbolData.type || 'STOCK',
      strategies: symbolData.strategies || {
        conservative: { entry: null, exit: null, stop: null },
        moderate: { entry: null, exit: null, stop: null },
        aggressive: { entry: null, exit: null, stop: null }
      }
    };
  };

  const displayData = getMarketData();

  // Phases for the loading animation
  const phases = [
    { label: 'Initializing analysis...', stage: 'initializing' },
    { label: 'Fetching market data...', stage: 'fetching_market_data' },
    { label: 'Processing technical indicators...', stage: 'processing_indicators' },
    { label: 'Analyzing price patterns...', stage: 'analyzing_patterns' },
    { label: 'Searching web for latest news...', stage: 'searching_web' }, // Added EXA search phase
    { label: 'Generating insights...', stage: 'generating_insights' },
    { label: 'Finalizing report...', stage: 'finalizing' },
    { label: 'Analysis complete', stage: 'complete' }
  ];

  // Parse the final content into sections when it becomes available
  useEffect(() => {
    if (finalContent && isComplete) {
      try {
        // First try to parse as JSON
        const parsed = JSON.parse(finalContent);
        setParsedContent(parsed);
      } catch (e) {
        // If parsing fails, use the content directly in the analysis results
        setAnalysisResults({
          complete_analysis: finalContent
        });
      }
      setLoading(false);
    }
  }, [finalContent, isComplete]);

  // Update the animation sequence to show analysis sooner
  useEffect(() => {
    if (isLoading) {
      setTimeout(() => {
        setShowConfirmation(false);

        // Start progress animation
        let currentProgress = 0;
        const progressInterval = setInterval(() => {
          if (currentProgress >= 100) {
            clearInterval(progressInterval);
            return;
          }

          currentProgress += 1;
          setProgress(currentProgress);

          // Update phases based on progress
          if (currentProgress === 15) {
            setCurrentPhase(1);
            setAnalysisStage('fetching_market_data');
          } else if (currentProgress === 30) {
            setCurrentPhase(2);
            setAnalysisStage('processing_indicators');
            setShowMarketData(true);
          } else if (currentProgress === 45) {
            setCurrentPhase(3);
            setAnalysisStage('analyzing_patterns');
            setShowAIAnalysis(true); // Show analysis earlier
          } else if (currentProgress === 60) {
            setCurrentPhase(4);
            setAnalysisStage('searching_web');
          } else if (currentProgress === 75) {
            setCurrentPhase(5);
            setAnalysisStage('generating_insights');
            setVisibleSections(['market_context']);
          } else if (currentProgress === 85) {
            setCurrentPhase(6);
            setAnalysisStage('finalizing');
            setVisibleSections(prev => [...prev, 'technical_levels']);
          } else if (currentProgress === 95) {
            setVisibleSections(prev => [...prev, 'risks', 'strategies']);
          } else if (currentProgress === 100) {
            setCurrentPhase(7);
            setAnalysisStage('complete');
            setVisibleSections(prev => [...prev, 'summary']);
            setAnimationComplete(true);
            if (onAnimationComplete) onAnimationComplete();
          }
        }, 50);
      }, 1000);
    }
  }, [isLoading, onAnimationComplete]);

  // Handle chart error
  useEffect(() => {
    const handleChartError = () => {
      setChartError(true);
    };

    // Add event listener for chart error
    if (chartRef.current) {
      chartRef.current.addEventListener('error', handleChartError);
    }

    return () => {
      if (chartRef.current) {
        chartRef.current.removeEventListener('error', handleChartError);
      }
    };
  }, [chartRef.current]);

  // Get dynamic dates for API calls
  const getDateParams = () => {
    const today = new Date();

    // Calculate date 90 days ago
    const startDate = new Date();
    startDate.setDate(today.getDate() - 90);

    // Format dates as YYYY-MM-DD
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    return {
      start_date: formatDate(startDate),
      end_date: formatDate(today)
    };
  };

  // Fetch all analyses in a single API call
  const fetchAllAnalyses = async () => {
    try {
      const effectiveSymbolTypes = symbolTypes || {};
      const hasSymbols = symbols && symbols.length > 0;
      const isCrypto = hasSymbols && symbols.some(symbol =>
        effectiveSymbolTypes[symbol] === 'CRYPTO'
      );
      const isMarketETF = hasSymbols && symbols.some(symbol =>
        ['VTI', 'SPY', 'QQQ', 'IWM', 'EEM', 'TLT', 'GLD', 'USO', 'VIX', 'DIA',
        'XLF', 'XLE', 'XLK', 'XLV', 'XLI', 'XLP', 'XLY', 'XLU', 'XLRE', 'XLC'].includes(symbol) ||
        effectiveSymbolTypes[symbol] === 'MARKET'
      );

      // Fetch chart data to get the latest price
      let latestPriceData = null;
      if (hasSymbols) {
        try {
          // First get the chart data for the symbol
          const { fetchPolygonChartData } = await import('@/services/polygonService');
          // Use the first symbol in the array
          const currentSymbol = Array.isArray(symbols) ? symbols[0] : symbols;
          const chartData = await fetchPolygonChartData({
            symbol: currentSymbol,
            timeframe: '1D'
          });

          // If we have chart data, use the last data point for the latest price
          if (chartData && chartData.data && chartData.data.length > 0) {
            const lastDataPoint = chartData.data[chartData.data.length - 1];
            latestPriceData = [{
              symbol: currentSymbol,
              formattedSymbol: chartData.symbol,
              price: lastDataPoint.close,
              change: 0, // We don't need these for the analysis
              percentChange: 0,
              volume: lastDataPoint.volume,
              timestamp: lastDataPoint.timestamp,
              dataType: 'chart_data',
              dataPoints: chartData.data.length,
              open: lastDataPoint.open,
              timeframe: '1D'
            }];
          } else {
            // Fallback to the old method if chart data is not available
            const { fetchLatestPriceData } = await import('@/services/polygonService');
            latestPriceData = await fetchLatestPriceData(symbols);
          }
        } catch (error) {
          console.error("Error fetching latest price data:", error);
        }
      }

      if (isGeneralMessage || isCrypto || isMarketETF) {
        try {
          const traceId = crypto.randomUUID();

          // Prepare polygon data section for the prompt
          let polygonDataSection = '';
          if (latestPriceData && latestPriceData.length > 0) {
            polygonDataSection = `\n\nHere is the latest price data from the chart:\n\`\`\`json\n${JSON.stringify(latestPriceData, null, 2)}\n\`\`\`\n\nIMPORTANT: Use ONLY this price data from the chart for your analysis. The current price of ${latestPriceData[0].price} is the EXACT price shown on the chart. You MUST use this price for all current price references in your response.`;
          }

          // Single consolidated Gemini call with grounding and latest price data
          const geminiResponse = await callGeminiAPI(
            `You are Osis, an elite market intelligence AI built for real-time, tactical, and actionable financial insight. Your mission is to deliver immediate clarity on U.S. public markets using live, real-world data via Google search. You are always connected to live search and are expected to use it when market-specific questions are asked—especially when the date, timing, or urgency is part of the request.

You are not allowed to respond with generic placeholders or hypotheticals when real data exists. If asked "What moved the market today?" or "What earnings just dropped?" or "What are the top gainers?"—you must use live search and ground your answer in verified current information, not templated filler.

You are focused on publicly traded U.S. markets—equities, ETFs, sectors, macro indicators, and events directly affecting the U.S. average investor or trader. You do not prioritize private companies, foreign equities, or inaccessible assets unless they are directly relevant to the U.S. market.

When users ask for tickers, trade setups, earnings impact, or market movers:
- Use live search immediately
- Name real stocks
- Give real context
- Highlight what matters now, not "in general"

Do not ever say:
"I cannot provide stock recommendations."
"Do your own research."
"I'm a harmless AI assistant."
"It depends on your goals."
"Watch for economic data releases" unless they just happened or are about to.

Those phrases are banned. Osis gives answers, not avoidance.

Tone: Tactical. Confident. Clear. Speak like a strategist managing capital—not a compliance officer avoiding risk.

You are here to deliver clarity, grounded in the now. No fluff. No fiction. No excuses. Return the response in markdown format.${polygonDataSection}

User Query: ${userQuery}`,
            {
              traceId,
              useGrounding: true, // Enable grounding
              properties: {
                query_type: isCrypto ? 'crypto' : isMarketETF ? 'market' : 'general',
                symbols: symbols?.join(','),
                has_price_data: !!latestPriceData
              }
            },
            {
              generationConfig: {
                temperature: 0.7,
                maxOutputTokens: 4096
              }
          }
          );

          const content = geminiResponse?.candidates?.[0]?.content?.parts?.[0]?.text || '';

          if (content) {
            // Set the analysis results directly without using sections
            setAnalysisResults({
              complete_analysis: content
            });
          } else {
            setAnalysisResults({
              complete_analysis: "Analysis generation completed, but no content was received."
            });
          }

        } catch (error) {
          console.error('Error in Gemini API call:', error);
          setAnalysisResults({
            complete_analysis: ""
          });
        }

          setLoading(false);
          return;
        }

      if (!isCrypto && !isGeneralMessage) {
        const { start_date, end_date } = getDateParams();

        const axiosConfig = {
          timeout: 30000,
          retries: 3,
          retryDelay: 1000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        };

        const geminiMetaDataPrompt = `Analyze the user query: "${userQuery}". If the query is a simple question about a stock (e.g., "Should I buy AAPL?", "Scan AAPL", "AAPL", "Should I Long or Short AAPL?"), or if the query is just a ticker symbol, then return a list of all analyst types and famous investor agents. Otherwise, based on the query, determine the relevant analyst types from the following list: technical_analyst, fundamentals_analyst, sentiment_analyst, warren_buffett, cathie_wood, ben_graham, bill_ackman, charlie_munger, valuation_analyst. Return a comma-separated list of the analyst types that are most appropriate for answering the query. Do not include any commentary or other text. For example, if the query is about technical indicators, return "technical_analyst". If the query requires fundamental analysis and valuation, return "fundamentals_analyst, valuation_analyst".`;

        const fetchGeminiMetaData = async () => {
          // Use the callGeminiAPI utility which now uses the edge function
          const response = await callGeminiAPI(geminiMetaDataPrompt, {}, {
            generationConfig: { temperature: 0.7, maxOutputTokens: 4000 }
          });
          // Extract content directly from the Gemini response
          const metaDataString = response?.candidates?.[0]?.content?.parts?.[0]?.text || "Unable to generate analysis.";
          return metaDataString.split(',').map(item => item.trim());
        };

        const geminiMetaData = await fetchGeminiMetaData();

        const makeRequest = async (retryCount = 0) => {
          try {
            // Use the secure API call utility with encryption for api.osis.co
            const response = await callOsisAPI('analyze', {
              tickers: symbols,
              start_date,
              end_date,
              portfolio: {},
              selected_analysts: geminiMetaData,
              model_name: "gemini-2.0-flash",
              show_reasoning: false
            });

            // Handle errors from the API call
            if (response.error) {
              throw new Error(`API error: ${response.error}`);
            }

            // Extract the response data
            const analyzeResponse = response.data;

            // Validate response data
            if (!analyzeResponse) {
              throw new Error('Invalid response: No data received');
            }

            // Check if the response indicates an error
            if (analyzeResponse.success === false) {
              throw new Error(`API error: ${analyzeResponse.error || 'Unknown error'}`);
            }

            // Extract the data from the response
            const responseData = analyzeResponse.success ? analyzeResponse.data : analyzeResponse;

            // Handle the response correctly - don't parse if it's already an object
            const analysisData = typeof responseData === 'string'
              ? JSON.parse(responseData.replace(/: *NaN/g, ': null')) // Replace NaN with null
              : responseData;

            // For technical analysis, use the Aura edge function
            if (symbols && symbols.length > 0) {
              try {
                // Call Aura for technical analysis
                const auraResponse = await callAuraAPI('analyze', {
                  tickers: symbols,
                  start_date,
                  end_date
                });

                if (!auraResponse.error && auraResponse.data && auraResponse.data.results) {
                  // Replace or add technical analysis data
                  analysisData.technical_analysis = auraResponse.data.results;
                }
              } catch (auraError) {
                console.error('Error calling Aura for technical analysis:', auraError);
                // Continue with api.osis.co data if Aura fails
              }
            }

            // Initialize empty analyst signals object
            const analystSignals = {};

            // Safely process analyst signals if they exist
            if (analysisData.data?.analyst_signals) {
              Object.entries(analysisData.data.analyst_signals).forEach(([analyst, signals]) => {
                if (signals && typeof signals === 'object') {
                  analystSignals[analyst] = {};
                  Object.entries(signals).forEach(([ticker, signalData]) => {
                    if (signalData?.signal) {
                      analystSignals[analyst][ticker] = {
                        signal: signalData.signal,
                        reasoning: signalData.reasoning || 'No reasoning provided'
                      };
                    }
                  });
                }
              });
            }

              // Extract active analysts from the response
            const activeAnalysts = Object.keys(analysisData.data?.analyst_signals || {})
                .map(key => {
                  if (key === 'cathie_wood_agent') return 'cathie_wood';
                  if (key === 'warren_buffett_agent') return 'warren_buffett';
                  if (key === 'charlie_munger_agent') return 'charlie_munger';
                  if (key === 'bill_ackman_agent') return 'bill_ackman';
                  if (key === 'ben_graham_agent') return 'ben_graham';
                return key.replace('_agent', '');
                })
                .filter(Boolean);

              // Update geminiMetaData to include active analysts
              const updatedGeminiMetaData = [...new Set([...geminiMetaData, ...activeAnalysts])];

            // Add Polygon data to the analysis
            let polygonDataSection = '';
            if (latestPriceData && latestPriceData.length > 0) {
              polygonDataSection = `\n\nLatest price data from chart:\n\`\`\`json\n${JSON.stringify(latestPriceData, null, 2)}\n\`\`\`\n\nIMPORTANT: Use ONLY this price data from the chart for your analysis. The current price of ${latestPriceData[0].price} is the EXACT price shown on the chart. You MUST use this price for all current price references, entry/exit points, and trading strategy calculations.`;
            }

            // Generate comprehensive analysis using Gemini
              const geminiAnalysisPrompt = `You are an expert financial analyst providing comprehensive, in-depth analysis. Your analysis should include:
1. Detailed technical analysis with specific indicators and patterns
2. Thorough fundamental analysis including financial metrics and ratios
3. Deep sentiment analysis covering market psychology and news impact
4. Expert perspectives from different investment styles (value, growth, etc.)
4. Comprehensive risk assessment and market outlook
5. Specific price levels, support/resistance, and ONE clear trading strategy with a single stop loss (SL) and take profit (TP) point

IMPORTANT GUIDELINES:
- NEVER provide specific share quantity recommendations
- NEVER mention portfolio allocations or position sizes
- NEVER suggest how many shares to buy or sell
- Focus on analysis, price targets, and market conditions only
- Avoid any specific investment advice about position sizing
- Use hyphens (-) sparingly and only for the most important key points (limit to 2-3 bullet points per section)
- Write in clear paragraphs for most analysis, using bullets only to highlight critical insights
- Format important points with bold and use ### for section headers
- NEVER repeat the user query in your response
- NEVER start with phrases like "Based on your query about..." or "Given the user query..."
- ALWAYS clearly state for the expert opinions where they are BULLISH, BEARISH, or NEUTRAL
- PROVIDE ONLY ONE TAKE PROFIT (TP) TARGET and ONE STOP LOSS (SL) LEVEL for clear chart visualization

FIRST ANALYZE USER QUERY:
User query: "${userQuery}"

If the user is asking about a specific aspect of stock analysis rather than requesting a full analysis, determine which parts they want:
1. If asking about a specific investor's opinion, focus only on fundamental analysis from that investor's perspective and tailor the trading strategy to match their investing style
2. If asking about technical indicators, focus only on technical analysis with appropriate trading strategy based on technicals
3. If asking about current sentiment, focus only on sentiment analysis and how it affects trading decisions
4. If asking about price targets or entry/exit points, focus on trading strategy with specific levels
5. If asking about company fundamentals, focus on fundamental data with a value-based trading approach
6. If asking a general question, provide the full comprehensive analysis

RESPONSE STRUCTURE BY QUESTION TYPE:
- For specific investor opinion questions:
  ### [Investor Name]'s Perspective on [Symbol]
  [Analysis from their investment philosophy]
  ### Key Metrics [Investor] Would Consider
  [Relevant metrics for this investor]
  ### Trading Strategy (Based on [Investor]'s Approach)
  [Trading strategy aligned with this investor's methodology]

- For technical analysis questions:
  ### Technical Analysis for [Symbol]
  [Detailed technical breakdown]
  ### Key Technical Indicators
  [Important indicators and their readings]
  ### Chart Patterns
  [Any relevant patterns]
  ### Trading Strategy (Based on Technicals)
  [Technical-based trading approach]

- For sentiment analysis questions:
  ### Current Market Sentiment for [Symbol]
  [Overall sentiment analysis]
  [What different market participants are saying]
  ### Trading Strategy (Based on Sentiment)
  [How to trade based on current sentiment]

For specific investor perspectives:
- Warren Buffett: Focus on long-term value, competitive moats, financial health, and management quality.
- Cathie Wood: Focus on disruptive innovation, growth potential, and future market leadership.
- Charlie Munger: Focus on rationality, avoiding stupidity, and long-term investing.
- Bill Ackman: Focus on simple, predictable businesses with high barriers to entry.
- Ben Graham: Focus on value investing, margin of safety, and buying undervalued assets.

REQUIRED FINAL SECTION:
Always end your analysis with a "### Trading Strategy" section that includes:

RECOMMENDATION: [LONG/SHORT] | Confidence: XX% | Current Price: $XX.XX

Entry Point: $XX.XX
Stop Loss: $XX.XX | Risk/Reward: 1:X.XX

Take Profit Target: $XX.XX

Key Levels: Support $XX.XX | Resistance $XX.XX

ALWAYS INCLUDE ONLY ONE STOP LOSS AND ONE TAKE PROFIT TARGET.

Based on the following analysis data, provide a comprehensive analysis following the guidelines above: ${JSON.stringify(analysisData)}${polygonDataSection}`;

              // Use the callGeminiAPI utility which now uses the edge function
              const geminiResponse = await callGeminiAPI(geminiAnalysisPrompt, {}, {
                generationConfig: { temperature: 0.7, maxOutputTokens: 4000 }
              });

              // Extract content from the Gemini response
              const content = geminiResponse?.candidates?.[0]?.content?.parts?.[0]?.text || '';

            // Return complete analysis with all data
            return {
              complete_analysis: content || "Unable to generate analysis content",
              data: {
                analyst_signals: analystSignals
              },
                geminiMetaData: updatedGeminiMetaData
            };

          } catch (error) {

            // Handle retry logic
            if (retryCount < 3) {
              await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
              return makeRequest(retryCount + 1);
            }

            // If all retries fail, return a valid structure with empty data
            return {
              complete_analysis: "Unable to complete analysis at this time",
              data: {
                analyst_signals: {}
              },
              geminiMetaData: []
            };
          }
        };

        try {
          const result = await makeRequest();
          setAnalysisResults(result);
          setConsolidatedAnalysis(result.complete_analysis);
              setAnalysisStage('complete');
              setLoading(false);
              if (onAnimationComplete) {
                onAnimationComplete();
            }
          } catch (error) {
          setLoading(false);
            setAnalysisResults({
              complete_analysis: "Unable to load analysis at this time. The analysis server returned an error. Please try again later."
            });
        }
        return;
      }

      setLoading(false);

    } catch (error) {
      setLoading(false);
      setAnalysisResults({
        complete_analysis: "Unable to load analysis at this time. The analysis server returned an error. Please try again later."
      });
    }
  };

  // DISABLED: Old agent system - now using new simplified chat flow
  // useEffect(() => {
  //   // Call fetchAllAnalyses even when there are no symbols
  //   // This allows handling general market questions or other non-symbol specific analyses
  //   fetchAllAnalyses();
  // }, [symbols]);

  // Helper function to convert agent ID to display name
  const getDisplayName = (agentId) => {
    const displayNames = {
      'technical_analyst': 'Technical Analysis',
      'sentiment_analyst': 'Sentiment Analysis',
      'fundamentals_analyst': 'Fundamental Analysis',
      'warren_buffett': 'Warren Buffett',
      'cathie_wood': 'Cathie Wood',
      'ben_graham': 'Ben Graham',
      'bill_ackman': 'Bill Ackman',
      'charlie_munger': 'Charlie Munger',
      'valuation_analyst': 'Valuation Analysis'
    };
    return displayNames[agentId] || agentId.replace('_', ' ');
  };

  // Helper function to get icon for each agent type
  const getAgentIcon = (agentId) => {
    switch (agentId) {
      case 'technical_analyst':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-white/70">
            <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
            <path d="M18 17V9"></path>
            <path d="M13 17V5"></path>
            <path d="M8 17v-3"></path>
          </svg>
        );
      case 'sentiment_analyst':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-white/70">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M8 15s1.5 2 4 2 4-2 4-2"></path>
            <line x1="9" y1="9" x2="9.01" y2="9"></line>
            <line x1="15" y1="9" x2="15.01" y2="9"></line>
          </svg>
        );
      case 'fundamentals_analyst':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-white/70">
            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
            <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
          </svg>
        );
      case 'warren_buffett':
        return (
          <div className="relative w-6 h-6 rounded-full overflow-hidden group flex-shrink-0 mr-2">
            {/* Glow effect layers */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 animate-pulse"></div>
            <div className="absolute inset-0 bg-black/40 mix-blend-overlay"></div>

            {/* Hover glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/0 to-purple-600/0 group-hover:from-blue-600/30 group-hover:to-purple-600/30 transition-all duration-300"></div>

            {/* Image */}
            <img
              src="https://gpcatalysis.blob.core.windows.net/gphostedcontent-prod/Buffett_500x500.jpg"
              alt="Warren Buffett"
              className="w-full h-full object-cover rounded-full"
              style={{ objectFit: 'cover' }}
            />

            {/* Border glow */}
            <div className="absolute inset-0 rounded-full border border-white/10 group-hover:border-white/20 transition-colors duration-300 shadow-[0_0_10px_rgba(255,255,255,0.1)]"></div>

            {/* Extra outer glow */}
            <div className="absolute -inset-0.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-blue-500/10 to-purple-500/10 blur-sm"></div>
          </div>
        );
      case 'cathie_wood':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-white/70">
            <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-white/70">
            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
            <path d="M13 16a3 3 0 0 1 2.24 5"></path>
            <path d="M19 21a3 3 0 0 1-2.24-5"></path>
          </svg>
        );
    }
  };

  // Add new function to check if symbol is in query
  const isSymbolInQuery = (symbol: string, query: string): boolean => {
    return query.toUpperCase().includes(symbol.toUpperCase());
  };

  // Replace the hardcoded shouldShowMarketData function with a Gemini-based one
  const shouldShowMarketData = async (query: string): Promise<boolean> => {
    try {
      // Import the callGeminiAPI function
      const { callGeminiAPI } = await import('@/utils/geminiUtils');

      const prompt = `Analyze this user query and determine if it requires real-time market data visualization (chart).

User Query: "${query}"

Rules:
1. If the query is about current market conditions, trends, or real-time price action, return "true"
2. If the query is educational, theoretical, or about trading concepts/strategies, return "false"
3. If the query asks about historical patterns but not current conditions, return "false"
4. If the query mentions a stock ticker (including 1-letter tickers like F for Ford or X for US Steel), return "true"

Examples:
- "Is the market in an uptrend?" -> true (needs current market data)
- "How do I use fibonacci retracements?" -> false (educational)
- "What's happening with the S&P 500 today?" -> true (current market conditions)
- "Explain the concept of support and resistance" -> false (educational)
- "What's going on with F stock?" -> true (mentions ticker F for Ford)
- "Tell me about X" -> true (mentions ticker X for US Steel)

Return ONLY "true" or "false" with no other text.`;

      // Use the secure edge function through our utility
      const data = await callGeminiAPI(prompt, {}, {
        generationConfig: {
          temperature: 0,
          maxOutputTokens: 10
        }
      });

      const result = data?.candidates?.[0]?.content?.parts?.[0]?.text?.toLowerCase().trim() || 'true';
      return result === 'true';
    } catch (error) {
      console.error('Error in shouldShowMarketData:', error);
      return true; // Default to showing market data if there's an error
    }
  };

  // Update the component to handle async shouldShowMarketData
  const [showChart, setShowChart] = useState(true);

  useEffect(() => {
    const checkMarketDataNeeded = async () => {
      const shouldShow = await shouldShowMarketData(userQuery);
      setShowChart(shouldShow);
    };

    checkMarketDataNeeded();
  }, [userQuery]);

  // Update the animation sequence to show analysis sooner
  useEffect(() => {
    if (!isLoading) {
      setShowAIAnalysis(true);
    }
  }, [isLoading]);

  // Add effect for text streaming
  useEffect(() => {
    if (!loading && Object.keys(analysisResults).length > 0) {
      const analysis = analysisResults.complete_analysis || "";
      if (analysis && !isStreaming) {
        setIsStreaming(true);
              streamText(analysis);
      }
    }
  }, [loading, analysisResults]);

  // Function to stream text gradually
  const streamText = (text: string) => {
    // If we've already tried regenerating MAX_AUTO_REGENERATIONS times, show the text anyway
    if (text.length < 300 && regenerationCount >= MAX_AUTO_REGENERATIONS) {
      setStreamedText(text);
      setIsStreaming(false);
      setIsAutoRegenerating(false);
      return () => {};
    }

    // Remove backslashes before dollar signs
    text = text.replace(/\\\$/g, '$');

    // Preserve newlines but normalize them to avoid excessive spacing
    // Replace multiple consecutive newlines with a double marker
    text = text.replace(/\n\n+/g, ' §DOUBLENEWLINE§ ');
    // Replace single newlines with a single marker
    text = text.replace(/\n/g, ' §NEWLINE§ ');

    const words = text.split(' ');
    let currentIndex = 0;

    const streamInterval = setInterval(() => {
      if (currentIndex < words.length) {
        // Add more words at a time for faster streaming (5-8 words)
        const chunk = words.slice(currentIndex, currentIndex + 5).join(' ') + ' ';

        // Update the streamed text
        setStreamedText(prev => {
          // Replace the special newline markers with actual newlines
          let formattedText = prev + chunk;
          formattedText = formattedText.replace(/§DOUBLENEWLINE§/g, '\n\n');
          return formattedText.replace(/§NEWLINE§/g, '\n');
        });

        currentIndex += 5;

        // Auto-scroll as text streams
        if (!userHasScrolled && containerRef.current) {
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth'
          });
        }
      } else {
        clearInterval(streamInterval);
        setIsStreaming(false);
      }
    }, 50); // Adjust speed as needed

    return () => clearInterval(streamInterval);
  };

  // Add a function to handle the export
  const handleExportChart = () => {
    const chartElement = document.getElementById('chart-container'); // Ensure this ID matches your chart container
    if (chartElement) {
        domtoimage.toPng(chartElement)
            .then((dataUrl) => {
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = 'chart.png';
                link.click();
            })
            .catch((error) => {
                console.error('Error exporting chart:', error);
            });
    }
};

  // Update the chart display logic
  const shouldShowChart = () => {
    // Add detailed logging
    // Don't show chart for general messages
    if (isGeneralMessage) {
      return false;
    }

    // Always show chart for crypto
    if (symbols?.some(symbol => symbolTypes?.[symbol] === 'CRYPTO')) {
      return true;
    }

    // Always show chart for stocks
    if (symbols?.some(symbol => symbolTypes?.[symbol] === 'STOCK')) {
      return true;
    }

    // Show chart for market symbols
    if (symbols?.some(symbol => symbolTypes?.[symbol] === 'MARKET')) {
      return true;
    }

    return false;
  };

  // Add logging before chart render
  useEffect(() => {
  }, [symbols, isGeneralMessage]);

  // Add handleCopy function
  const handleCopy = async () => {
    try {
      // Remove all HTML tags and markdown formatting to get plain text
      const plainText = streamedText
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold markdown
        .replace(/\*([^*]+)\*/g, '$1') // Remove italic markdown
        .replace(/###\s+/g, '') // Remove heading markdown
        .replace(/\n\s*[-*]\s+/g, '\n• '); // Standardize bullet points

      await navigator.clipboard.writeText(plainText);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 1500);
    } catch (error) {
      // Handle error silently or use a toast notification instead
    }
  };

  // Modify this section to use a different name for the function
  // Use safeRegenerate instead of handleRegenerate
  const safeRegenerate = async () => {
    // Reset regeneration counter when manually regenerating
    setRegenerationCount(0);
    setIsAutoRegenerating(false);

    if (onRegenerate) {
      try {
        // Show loading state
      setLoading(true);
      setStreamedText('');

        // Call the parent component's onRegenerate function
        // This will handle incrementing the message counter and making the API call
      await onRegenerate();
      } catch (error) {
      }
    }
  };

  // Function to save trade to Supabase
  const saveTradeToDB = async (ticker: string, strategy: any) => {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return;
      }

      // Determine entry price (use the first entry point)
      const entryPrice = strategy.entryPoints && strategy.entryPoints.length > 0
        ? strategy.entryPoints[0].price
        : null;

      // Determine take profit (use the first take profit target)
      const takeProfit = strategy.takeProfitTargets && strategy.takeProfitTargets.length > 0
        ? strategy.takeProfitTargets[0].price
        : null;

      // Create the trade record
      const { data, error } = await supabase
        .from('trades')
        .insert({
          user_id: user.id,
          ticker: ticker,
          entry_price: entryPrice,
          take_profit: takeProfit,
          stop_loss: strategy.stopLoss,
          direction: strategy.direction,
          confidence: strategy.confidence,
          timeframe: '1M', // Default timeframe
          status: 'suggested',
          reasoning: streamedText.substring(0, 1000) // Store first 1000 chars of analysis
        });

      if (error) {
      } else {
      }
    } catch (error) {
    }
  };

  // Parse trading strategy when analysis is available
  useEffect(() => {
    if (streamedText && !isStreaming && !isAutoRegenerating) {
      try {
        // Log a part of the analysis text to help with debugging

        // Try to find "Trading Strategy" section
        const tradingStrategySection = streamedText.match(/### Trading Strategy[\s\S]*?(?=###|$)/i)?.[0];
        if (tradingStrategySection) {
        } else {
        }

        const extractedStrategy = extractTradingStrategy(streamedText);

        // Create a simplified trading strategy with only one TP target
        const simplifiedStrategy = extractedStrategy ? {
          direction: extractedStrategy.recommendation,
          confidence: extractedStrategy.confidence,
          entryPoints: [{
            price: extractedStrategy.entry.primary,
            timestamp: Date.now(),
            label: 'Entry'
          }],
          stopLoss: extractedStrategy.stopLoss,
          takeProfitTargets: [{
            price: extractedStrategy.exit.target1, // Only use the first TP target
            timestamp: Date.now(),
            label: 'TP'
          }],
          riskRewardRatio: `1:${extractedStrategy.riskReward}`
        } : null;


        // Only set the trading strategy if both TP and SL are valid numbers greater than 0
        if (simplifiedStrategy &&
            simplifiedStrategy.stopLoss > 0 &&
            simplifiedStrategy.takeProfitTargets[0].price > 0) {
                  setTradingStrategy(simplifiedStrategy);

                  // Save the trade to the database
                  if (symbol) {
                    saveTradeToDB(symbol, simplifiedStrategy);
                  }
        } else {
          // Try to do a primitive extraction of TP and SL values if the main extraction failed
          if (!simplifiedStrategy || simplifiedStrategy.stopLoss <= 0 || simplifiedStrategy.takeProfitTargets[0].price <= 0) {
            // Simple regex patterns to directly extract TP and SL values
            const tpMatch = streamedText.match(/(?:take\s*profit|tp).*?\$?(\d+\.?\d*)/i);
            const slMatch = streamedText.match(/(?:stop\s*loss|sl).*?\$?(\d+\.?\d*)/i);

            if (tpMatch && slMatch) {
              const tpValue = parseFloat(tpMatch[1]);
              const slValue = parseFloat(slMatch[1]);

              if (!isNaN(tpValue) && !isNaN(slValue) && tpValue > 0 && slValue > 0) {

                // Try to detect the trade direction from the text first
                let direction = 'LONG'; // Default to LONG

                if (streamedText.match(/\b(short|bearish)\b/i)) {
                  direction = 'SHORT';
                } else if (streamedText.match(/\b(long|bullish)\b/i)) {
                  direction = 'LONG';
                } else {
                  // If direction not found in text, infer from TP and SL values
                  // For LONG trades: TP > SL, for SHORT trades: TP < SL
                  direction = tpValue > slValue ? 'LONG' : 'SHORT';
                }

                const primitiveStrategy = {
                  direction: direction,
                  confidence: 'MEDIUM',
                  entryPoints: [{
                    price: (tpValue + slValue) / 2, // Use midpoint as entry
                    timestamp: Date.now(),
                    label: 'Entry'
                  }],
                  stopLoss: slValue,
                  takeProfitTargets: [{
                    price: tpValue,
                    timestamp: Date.now(),
                    label: 'TP'
                  }],
                  riskRewardRatio: '1:1' // Default ratio
                };

                setTradingStrategy(primitiveStrategy);

                // Save the trade to the database
                if (symbol) {
                  saveTradeToDB(symbol, primitiveStrategy);
                }
              } else {
                setTradingStrategy(null);
              }
            } else {
              setTradingStrategy(null);
            }
          } else {
            setTradingStrategy(null);
          }
        }
      } catch (error) {
        setTradingStrategy(null);
      }
    }
  }, [streamedText, isStreaming, isAutoRegenerating, symbol]);

  return (
    <div
      className="space-y-6 animate-fade-in w-full max-w-full overflow-hidden"
      style={{ margin: "0" }}
      ref={containerRef}
    >
      <style>{typingAnimationStyles}</style>

      {/* Only render the chart and expert analysis section if we have data and it's not a general message */}
      {(symbols?.length > 0 && shouldShowChart() && !isGeneralMessage) && (
        <div className="flex flex-col lg:flex-row gap-4 w-full max-w-[100vw] mx-auto px-4">
          {(() => {
            // Check if there are any expert opinions for the current symbol
            const hasExpertOpinions = () => {
              if (!analysisResults?.data?.analyst_signals || !symbol) {
                return false;
              }

              // Check if there are any expert agents with signals for this symbol
              const expertAgents = Object.keys({
                'warren_buffett_agent': true,
                'cathie_wood_agent': true,
                'charlie_munger_agent': true,
                'bill_ackman_agent': true,
                'ben_graham_agent': true
              });

              return Object.entries(analysisResults.data.analyst_signals).some(([agentId, signals]) => {
                return expertAgents.includes(agentId) && signals[symbol]?.signal;
              });
            };

            const showExpertColumn = symbolTypes?.[symbol] !== 'CRYPTO' && hasExpertOpinions();

            return (
              <>
                <div className={`w-full ${showExpertColumn ? 'lg:w-2/3' : 'lg:w-full'} h-[350px] sm:h-[450px] lg:h-[580px]`}>
                  <div className="h-full w-full bg-black rounded-lg sm:rounded-xl border border-[#1A1A1C]/60 overflow-hidden shadow-lg">
                    <AdvancedStockChart
                      symbol={symbol}
                      showVolume={false}
                      showControls={true}
                      theme="dark"
                      chartType="area"
                      height={350}
                      maxDataPoints={150}
                      yAxisDraggable={true}
                      defaultYAxisScale="compressed"
                      yAxisPosition="right"
                      tradingStrategy={tradingStrategy}
                      timeframe="1M" // Set a fixed timeframe to prevent automatic reset
                    />
                  </div>
                </div>

                {showExpertColumn && (
                  <div className="w-full lg:w-1/3 h-[300px] sm:h-[400px] lg:h-[580px] flex">
          {showAIAnalysis && !loading && symbol && (
                      <div className="w-full">
            <ExpertOpinionsCard
              symbol={symbol}
              analysisResults={analysisResults}
            />
                      </div>
                    )}
                    {showAIAnalysis && loading && (
                      <div className="h-full w-full bg-[#0A0A0C] rounded-lg sm:rounded-xl border border-[#1A1A1C]/60 overflow-hidden shadow-lg flex items-center justify-center">
                        <div className="flex flex-col items-center">
                          <div className="h-8 w-8 border-4 border-[#1A1A1C] border-t-white/30 rounded-full animate-spin mb-3" />
                          <div className="text-white/70 text-sm font-medium">
                            Loading expert analysis...
        </div>
      </div>
                      </div>
                    )}
                  </div>
                )}
              </>
            );
          })()
          }
        </div>
      )}

      {/* Second loading state: Always show "Loading analysis..." after chart is displayed or when no chart is needed */}
      {(!showAIAnalysis || loading) && (
        <div className="mt-6 space-y-4">
          <div className="flex flex-col space-y-4 pb-4">
            <div className="text-white/70 flex items-center gap-2">
              <div className="h-4 w-4 rounded-full border-2 border-white/10 border-t-white/30 animate-spin" />
              Loading analysis...
            </div>
          </div>
        </div>
      )}

      {/* AI Analysis Section - Simplified streaming approach */}
      {showAIAnalysis && !loading && (
        <div className="mt-6 space-y-3 w-full">
          <div className="flex flex-col space-y-3 pb-4 w-full">
            <div className="space-y-3 w-full" style={{ marginLeft: '10px' }}>
              <div className="text-white w-full">
                <div
                  className="text-base leading-normal space-y-3 w-full break-words"
                  style={{ fontFamily: "SF Pro Display, -apple-system, system-ui, sans-serif", wordWrap: "break-word", overflowWrap: "break-word" }}
                  dangerouslySetInnerHTML={{ __html: formatText(streamedText) }}
                />
                {(isStreaming || isAutoRegenerating) && (
                  <div className="typing-indicator mt-2">
                    <span className="dot"></span>
                    <span className="dot"></span>
                    <span className="dot"></span>
                  </div>
                )}
                {!isStreaming && !isAutoRegenerating && (
                  <ActionButtons
                    onCopy={() => handleCopy()}
                    onRegenerate={() => safeRegenerate()}
                    analysisText={streamedText}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add disclaimer at the bottom */}
      <div style={{ marginTop: '0px', fontSize: '12px', color: 'gray' }}>
      DISCLAIMER: This is not financial advice. Osis assumes no liability for any losses. Past performance is no guarantee of future results. Invest at your own risk.
      </div>
    </div>
  );
};

// Keep only one export at the end of the file
export default LoadingMessage;
