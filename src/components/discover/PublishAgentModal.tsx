import React, { useState, useEffect } from 'react';
import { X, Upload, Tag, FileText, Folder } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { publishAgent, type AgentCategory } from '@/services/discoverService';
import { getAgents, type Agent } from '@/services/agentService';

interface PublishAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: AgentCategory[];
  onSuccess: () => void;
}

const PublishAgentModal: React.FC<PublishAgentModalProps> = ({
  isOpen,
  onClose,
  categories,
  onSuccess
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(false);
  const [userAgents, setUserAgents] = useState<Agent[]>([]);
  
  const [formData, setFormData] = useState({
    agentId: '',
    name: '',
    description: '',
    category: '',
    tags: [] as string[]
  });

  const [newTag, setNewTag] = useState('');

  // Common tags for suggestions
  const suggestedTags = [
    'RSI', 'MACD', 'Moving Average', 'Bollinger Bands', 'Support/Resistance',
    'Breakout', 'Momentum', 'Volume', 'Candlestick', 'Trend Following',
    'Mean Reversion', 'Scalping', 'Position Sizing', 'Stop Loss', 'Take Profit',
    'Day Trading', 'Swing Trading', 'Options', 'Risk Management'
  ];

  // Load user's agents when modal opens
  useEffect(() => {
    if (isOpen) {
      loadUserAgents();
    }
  }, [isOpen]);

  const loadUserAgents = async () => {
    try {
      setLoadingAgents(true);
      const agents = await getAgents();
      setUserAgents(agents);
    } catch (error) {
      console.error('Error loading user agents:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load your agents"
      });
    } finally {
      setLoadingAgents(false);
    }
  };

  const handleAgentSelect = (agentId: string) => {
    const selectedAgent = userAgents.find(agent => agent.id === agentId);
    if (selectedAgent) {
      setFormData(prev => ({
        ...prev,
        agentId,
        name: selectedAgent.name,
        description: selectedAgent.description || ''
      }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSuggestedTagClick = (tag: string) => {
    if (!formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.agentId || !formData.name || !formData.category) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all required fields"
      });
      return;
    }

    try {
      setLoading(true);
      
      const response = await publishAgent({
        agentId: formData.agentId,
        name: formData.name,
        description: formData.description,
        category: formData.category,
        tags: formData.tags
      });

      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Agent published successfully"
        });
        onSuccess();
        handleClose();
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to publish agent"
        });
      }
    } catch (error) {
      console.error('Error publishing agent:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      agentId: '',
      name: '',
      description: '',
      category: '',
      tags: []
    });
    setNewTag('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Publish Agent to Marketplace
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Share your trading agent with the community. Published agents can be discovered and imported by other users.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Agent Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">
              Select Agent to Publish *
            </label>
            <Select value={formData.agentId} onValueChange={handleAgentSelect}>
              <SelectTrigger className="bg-gray-800 border-gray-700">
                <SelectValue placeholder="Choose an agent from your library" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {loadingAgents ? (
                  <SelectItem value="loading" disabled>Loading agents...</SelectItem>
                ) : userAgents.length === 0 ? (
                  <SelectItem value="no-agents" disabled>No agents found</SelectItem>
                ) : (
                  userAgents.map((agent) => (
                    <SelectItem key={agent.id!} value={agent.id!}>
                      {agent.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">
              Display Name *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter a display name for your agent"
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">
              Description
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what your agent does, its strategy, and when to use it..."
              rows={4}
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">
              Category *
            </label>
            <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
              <SelectTrigger className="bg-gray-800 border-gray-700">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-gray-300">
              Tags
            </label>
            
            {/* Add Tag Input */}
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag..."
                className="bg-gray-800 border-gray-700 text-white"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
              />
              <Button
                type="button"
                onClick={handleAddTag}
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Add
              </Button>
            </div>

            {/* Current Tags */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="border-blue-500 text-blue-300 cursor-pointer"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    {tag}
                    <X className="w-3 h-3 ml-1" />
                  </Badge>
                ))}
              </div>
            )}

            {/* Suggested Tags */}
            <div className="space-y-2">
              <p className="text-xs text-gray-500">Suggested tags (click to add):</p>
              <div className="flex flex-wrap gap-1">
                {suggestedTags
                  .filter(tag => !formData.tags.includes(tag))
                  .slice(0, 10)
                  .map((tag) => (
                    <Badge
                      key={tag}
                      variant="outline"
                      className="border-gray-600 text-gray-400 cursor-pointer hover:border-blue-500 hover:text-blue-300 text-xs"
                      onClick={() => handleSuggestedTagClick(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading || !formData.agentId || !formData.name || !formData.category}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
            >
              {loading ? 'Publishing...' : 'Publish Agent'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PublishAgentModal;
