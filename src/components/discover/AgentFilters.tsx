import React, { useState } from 'react';
import { X, Star, Download, Calendar, TrendingUp } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { type AgentCategory, type DiscoverFilters } from '@/services/discoverService';

interface AgentFiltersProps {
  categories: AgentCategory[];
  filters: DiscoverFilters;
  onFilterChange: (filters: Partial<DiscoverFilters>) => void;
}

const AgentFilters: React.FC<AgentFiltersProps> = ({ categories, filters, onFilterChange }) => {
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || []);

  // Common tags for filtering
  const commonTags = [
    'RSI', 'MACD', 'Moving Average', 'Bollinger Bands', 'Support/Resistance',
    'Breakout', 'Momentum', 'Volume', 'Candlestick', 'Trend Following',
    'Mean Reversion', 'Scalping', 'Position Sizing', 'Stop Loss', 'Take Profit'
  ];

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      onFilterChange({ category });
    } else {
      onFilterChange({ category: '' });
    }
  };

  const handleTagChange = (tag: string, checked: boolean) => {
    let newTags: string[];
    if (checked) {
      newTags = [...selectedTags, tag];
    } else {
      newTags = selectedTags.filter(t => t !== tag);
    }
    setSelectedTags(newTags);
    onFilterChange({ tags: newTags });
  };

  const handleFeaturedChange = (checked: boolean) => {
    onFilterChange({ featured: checked || undefined });
  };

  const clearAllFilters = () => {
    setSelectedTags([]);
    onFilterChange({
      category: '',
      tags: [],
      featured: undefined
    });
  };

  const hasActiveFilters = filters.category || (filters.tags && filters.tags.length > 0) || filters.featured;

  return (
    <div className="space-y-6">
      {/* Unified Filters Card */}
      <Card className="bg-white/[0.02] border-white/[0.08]">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-white flex items-center gap-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
              <img
                src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Sliders.svg"
                alt="Filters"
                className="w-5 h-5"
              />
              Filters
            </CardTitle>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-white/50 hover:text-white"
              >
                <X className="w-4 h-4 mr-1" />
                Clear
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-6">
          {/* Quick Filters Section */}
          <div>
            <h4 className="text-xs font-medium text-white/50 mb-3 uppercase tracking-wider">Quick Filters</h4>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={filters.featured || false}
                onCheckedChange={handleFeaturedChange}
                className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
              />
              <label htmlFor="featured" className="text-sm text-white/70 cursor-pointer" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                Featured Agents
              </label>
            </div>
          </div>

          {/* Categories Section */}
          <div>
            <h4 className="text-xs font-medium text-white/50 mb-3 uppercase tracking-wider">Categories</h4>
            <div className="space-y-3">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`category-${category.id}`}
                    checked={filters.category === category.name}
                    onCheckedChange={(checked) => handleCategoryChange(category.name, checked as boolean)}
                    className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                  />
                  <label
                    htmlFor={`category-${category.id}`}
                    className="text-sm text-white/70 cursor-pointer flex-1"
                    style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                  >
                    {category.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Tags Section */}
          <div>
            <h4 className="text-xs font-medium text-white/50 mb-3 uppercase tracking-wider">Tags</h4>
            <div className="space-y-3 max-h-48 overflow-y-auto">
              {commonTags.map((tag) => (
                <div key={tag} className="flex items-center space-x-2">
                  <Checkbox
                    id={`tag-${tag}`}
                    checked={selectedTags.includes(tag)}
                    onCheckedChange={(checked) => handleTagChange(tag, checked as boolean)}
                    className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                  />
                  <label
                    htmlFor={`tag-${tag}`}
                    className="text-sm text-white/70 cursor-pointer flex-1"
                    style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                  >
                    {tag}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div>
              <h4 className="text-xs font-medium text-white/50 mb-3 uppercase tracking-wider">Active Filters</h4>
              <div className="flex flex-wrap gap-2">
                {filters.featured && (
                  <Badge variant="outline" className="border-green-500/30 text-green-400 bg-green-500/10">
                    Featured
                  </Badge>
                )}
                {filters.category && (
                  <Badge variant="outline" className="border-white/[0.12] text-white/70 bg-white/[0.04]">
                    {filters.category}
                  </Badge>
                )}
                {filters.tags && filters.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="border-white/[0.12] text-white/70 bg-white/[0.04]">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentFilters;
