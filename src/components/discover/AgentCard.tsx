import React, { useState } from 'react';
import { Star, Download, Calendar, User, Tag, Eye, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';
import { type PublishedAgent } from '@/services/discoverService';
import AgentPreviewModal from './AgentPreviewModal';

interface AgentCardProps {
  agent: PublishedAgent;
  viewMode: 'grid' | 'list';
  onImport: (publishedAgentId: string, customName?: string) => void;
  importing: boolean;
}

const AgentCard: React.FC<AgentCardProps> = ({ agent, viewMode, onImport, importing }) => {
  const [showPreview, setShowPreview] = useState(false);

  const handleImport = () => {
    onImport(agent.id);
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : i < rating
            ? 'text-yellow-400 fill-current opacity-50'
            : 'text-gray-600'
        }`}
      />
    ));
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Day Trading': 'bg-red-500/20 text-red-300 border-red-500/30',
      'Technical Analysis': 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      'Options Trading': 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      'Swing Trading': 'bg-green-500/20 text-green-300 border-green-500/30',
      'Risk Management': 'bg-orange-500/20 text-orange-300 border-orange-500/30',
      'Market Scanning': 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
      'Fundamental Analysis': 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
      'General': 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    };
    return colors[category] || colors['General'];
  };

  if (viewMode === 'list') {
    return (
      <>
        <Card className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08] hover:border-white/[0.12] transition-all duration-300 hover:from-white/[0.04] hover:to-white/[0.02] hover:shadow-lg hover:shadow-black/20">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-start gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-semibold text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>{agent.name}</h3>
                      {agent.isFeatured && (
                        <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                          Featured
                        </Badge>
                      )}
                      <Badge className="border-white/[0.12] text-white/60 bg-white/[0.04] text-xs cursor-default">
                        {agent.category}
                      </Badge>
                    </div>

                    <p className="text-white/50 mb-3 line-clamp-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                      {agent.description || 'No description available'}
                    </p>

                    <div className="flex items-center gap-6 text-sm text-white/40">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        <span>{agent.publisherName}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Download className="w-4 h-4" />
                        <span>{agent.downloadCount} downloads</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {renderStars(agent.averageRating)}
                        <span className="ml-1 text-white/50">
                          {agent.averageRating.toFixed(1)} ({agent.totalReviews})
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDistanceToNow(new Date(agent.createdAt), { addSuffix: true })}</span>
                      </div>
                    </div>

                    {agent.tags.length > 0 && (
                      <div className="flex items-center gap-2 mt-3">
                        <Tag className="w-4 h-4 text-white/40" />
                        <div className="flex flex-wrap gap-1">
                          {agent.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs border-white/[0.12] text-white/50 bg-white/[0.02] cursor-default">
                              {tag}
                            </Badge>
                          ))}
                          {agent.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs border-white/[0.12] text-white/50 bg-white/[0.02] cursor-default">
                              +{agent.tags.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2 ml-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04] hover:border-white/[0.2] bg-transparent transition-all duration-200"
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={importing}
                  size="sm"
                  className="bg-white text-black hover:bg-white/90 border-0 transition-all duration-200 shadow-[0_0_20px_rgba(255,255,255,0.2)] hover:shadow-[0_0_30px_rgba(255,255,255,0.3)]"
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  {importing ? 'Importing...' : 'Import'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <AgentPreviewModal
          agent={agent}
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          onImport={handleImport}
          importing={importing}
        />
      </>
    );
  }

  return (
    <>
      <Card className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08] hover:border-white/[0.12] transition-all duration-300 hover:from-white/[0.04] hover:to-white/[0.02] hover:shadow-lg hover:shadow-black/20 h-full flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <CardTitle className="text-lg text-white line-clamp-1">{agent.name}</CardTitle>
                {agent.isFeatured && (
                  <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 text-xs">
                    Featured
                  </Badge>
                )}
              </div>
              <Badge className="border-white/[0.12] text-white/60 bg-white/[0.04] text-xs cursor-default">
                {agent.category}
              </Badge>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/[0.04]">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="bg-[#1A1A1A] border-white/[0.08] text-white shadow-[0_8px_32px_rgba(0,0,0,0.4)]"
                sideOffset={5}
                alignOffset={-5}
              >
                <DropdownMenuItem
                  onClick={handlePreview}
                  className="text-white/80 hover:bg-white/[0.04] hover:text-white focus:bg-white/[0.04] focus:text-white cursor-pointer"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="pt-0 flex flex-col h-full">
          <div className="flex-1">
            <CardDescription className="text-white/50 mb-4 line-clamp-3 min-h-[3rem]" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
              {agent.description || 'No description available'}
            </CardDescription>

            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1 text-white/40">
                  <User className="w-4 h-4" />
                  <span className="truncate">{agent.publisherName}</span>
                </div>
                <div className="flex items-center gap-1 text-white/40">
                  <Download className="w-4 h-4" />
                  <span>{agent.downloadCount}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  {renderStars(agent.averageRating)}
                  <span className="text-sm text-white/50 ml-1">
                    {agent.averageRating.toFixed(1)} ({agent.totalReviews})
                  </span>
                </div>
                <span className="text-xs text-white/40">
                  {formatDistanceToNow(new Date(agent.createdAt), { addSuffix: true })}
                </span>
              </div>

              {agent.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {agent.tags.slice(0, 2).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs border-white/[0.12] text-white/50 bg-white/[0.02] cursor-default">
                      {tag}
                    </Badge>
                  ))}
                  {agent.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs border-white/[0.12] text-white/50 bg-white/[0.02] cursor-default">
                      +{agent.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Import button always at bottom */}
          <div className="mt-4 pt-3 border-t border-white/[0.06]">
            <Button
              onClick={handleImport}
              disabled={importing}
              className="w-full bg-white text-black hover:bg-white/90 border-0 transition-all duration-200 shadow-[0_0_20px_rgba(255,255,255,0.2)] hover:shadow-[0_0_30px_rgba(255,255,255,0.3)]"
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
            >
              {importing ? 'Importing...' : 'Import to Library'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <AgentPreviewModal
        agent={agent}
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        onImport={handleImport}
        importing={importing}
      />
    </>
  );
};

export default AgentCard;
