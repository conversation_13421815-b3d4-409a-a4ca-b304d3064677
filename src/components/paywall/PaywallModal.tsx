import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Crown, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface PaywallModalProps {
  isOpen: boolean;
  onClose: () => void;
  featureName?: string;
}

const PaywallModal: React.FC<PaywallModalProps> = ({ 
  isOpen, 
  onClose, 
  featureName = "this feature" 
}) => {
  const navigate = useNavigate();

  const handleUpgrade = () => {
    onClose();
    navigate('/subscription/manage');
  };

  const proFeatures = [
    'Access to Agent Builder',
    'Discover Community Agents', 
    'Advanced Stock Search',
    'Stock Scanner Tools',
    '200 messages per month',
    'Priority Support'
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm p-4">
          <motion.div
            className="absolute inset-0 bg-black/60"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          <motion.div
            className="relative w-full max-w-md bg-[#121212] rounded-2xl shadow-2xl border border-white/[0.08] overflow-hidden"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 p-2 text-white/60 hover:text-white/80 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>

            {/* Header */}
            <div className="p-6 text-center border-b border-white/[0.08]">
              <div className="mx-auto w-12 h-12 mb-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-white text-xl font-semibold mb-2 font-sans">
                Upgrade to Access {featureName}
              </h2>
              <p className="text-white/60 text-sm font-sans">
                Unlock premium features with our Pro plan
              </p>
            </div>

            {/* Features List */}
            <div className="p-6">
              <div className="space-y-3 mb-6">
                {proFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <Check className="w-3 h-3 text-green-400" />
                    </div>
                    <span className="text-white/80 text-sm font-sans">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={handleUpgrade}
                  className="w-full bg-white hover:bg-white/95 text-black font-medium py-2.5 rounded-lg transition-all duration-200 shadow-[inset_0_1px_2px_rgba(0,0,0,0.03),0_1px_3px_rgba(0,0,0,0.08)] font-sans"
                >
                  Upgrade to Pro
                </Button>
                
                <button
                  onClick={onClose}
                  className="w-full py-2.5 text-white/60 hover:text-white/80 text-sm transition-colors font-sans"
                >
                  Maybe later
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default PaywallModal;
