import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { usePageLock } from '@/hooks/usePageLock';
import PaywallModal from './PaywallModal';

interface ProtectedPageRouteProps {
  children: React.ReactNode;
}

const ProtectedPageRoute: React.FC<ProtectedPageRouteProps> = ({ children }) => {
  const location = useLocation();
  const { isPageLocked, isLoading } = usePageLock();
  const [showPaywall, setShowPaywall] = useState(false);

  // Check if current page is locked
  useEffect(() => {
    if (!isLoading && isPageLocked(location.pathname)) {
      setShowPaywall(true);
    } else {
      setShowPaywall(false);
    }
  }, [location.pathname, isPageLocked, isLoading]);

  // Get feature name based on path
  const getFeatureName = (path: string): string => {
    if (path.includes('/agent-builder')) return 'Agent Builder';
    if (path.includes('/discover')) return 'Discover';
    if (path.includes('/stock-search')) return 'Stock Search';
    if (path.includes('/agent-scanner')) return 'Stock Scanner';
    return 'this feature';
  };

  // If page is locked, show paywall modal and prevent rendering children
  if (!isLoading && isPageLocked(location.pathname)) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-white/[0.03] rounded-full flex items-center justify-center border border-white/[0.08]">
            <svg className="w-8 h-8 text-white/40" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-white text-lg font-medium mb-2 font-sans">
            {getFeatureName(location.pathname)} Locked
          </h3>
          <p className="text-white/60 text-sm font-sans mb-4">
            Upgrade to Pro to access this feature
          </p>
        </div>
        
        <PaywallModal
          isOpen={showPaywall}
          onClose={() => setShowPaywall(false)}
          featureName={getFeatureName(location.pathname)}
        />
      </div>
    );
  }

  // If not locked or still loading, render children normally
  return <>{children}</>;
};

export default ProtectedPageRoute;
