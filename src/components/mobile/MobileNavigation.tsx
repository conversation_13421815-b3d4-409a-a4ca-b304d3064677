import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Menu, 
  X, 
  MessagesSquare, 
  PieChart, 
  Search,
  TrendingUp,
  Settings,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { usePageLock } from '@/hooks/usePageLock';
import PaywallModal from '@/components/paywall/PaywallModal';
import MobileRestrictionModal from './MobileRestrictionModal';

const MobileNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isPageLocked } = usePageLock();
  const [isOpen, setIsOpen] = useState(false);
  const [showPaywall, setShowPaywall] = useState(false);
  const [paywallFeature, setPaywallFeature] = useState('');
  const [showMobileRestriction, setShowMobileRestriction] = useState(false);

  // Navigation items for mobile
  const navItems = [
    {
      id: 'home',
      label: 'Headquarters',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/dashboard.svg"
        alt="Dashboard"
        className="w-5 h-5"
      />,
      path: '/home'
    },
    {
      id: 'chat',
      label: 'Chat Interface',
      icon: <MessagesSquare className="w-5 h-5" />,
      path: '/chat'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <PieChart className="w-5 h-5" />,
      path: '/portfolio-builder'
    },
    {
      id: 'discover',
      label: 'Discover',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//glboe.svg"
        alt="Discover"
        className="w-5 h-5"
      />,
      path: '/discover'
    },
    {
      id: 'stock-search',
      label: 'Stock Search',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Search.svg"
        alt="Stock Search"
        className="w-5 h-5"
      />,
      path: '/stock-search'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Scan.svg"
        alt="Scan"
        className="w-5 h-5"
      />,
      path: '/agent-scanner'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Bar%20chart.svg"
        alt="Bar Chart"
        className="w-5 h-5"
      />,
      path: '/agent-backtesting'
    },
    {
      id: 'agent-builder',
      label: 'Agent Builder',
      icon: <Monitor className="w-5 h-5" />,
      path: '/agent-builder',
      isRestricted: true
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      path: '/settings'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const getFeatureName = (path: string) => {
    if (path.includes('/agent-builder')) return 'Agent Builder';
    if (path.includes('/discover')) return 'Discover';
    if (path.includes('/stock-search')) return 'Stock Search';
    if (path.includes('/agent-scanner')) return 'Stock Scanner';
    return 'Premium Feature';
  };

  const handleNavigation = (item: any) => {
    // Check if it's the restricted builder item
    if (item.isRestricted) {
      setShowMobileRestriction(true);
      setIsOpen(false);
      return;
    }

    // Check if page is locked for basic plan users
    if (isPageLocked(item.path)) {
      const featureName = getFeatureName(item.path);
      setPaywallFeature(featureName);
      setShowPaywall(true);
      setIsOpen(false);
      return;
    }

    navigate(item.path);
    setIsOpen(false);
  };

  return (
    <>
      {/* Mobile Menu Button - Clean without box */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed top-4 left-4 z-50 p-3 rounded-lg hover:bg-white/[0.05] transition-colors"
      >
        <Menu className="w-6 h-6 text-white" />
      </button>

      {/* Mobile Navigation Overlay */}
      <AnimatePresence>
        {isOpen && (
          <div className="fixed inset-0 z-50">
            <motion.div
              className="absolute inset-0 bg-black/60 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />
            
            <motion.div
              className="absolute left-0 top-0 h-full w-80 bg-[#121212] border-r border-white/[0.08] shadow-2xl"
              initial={{ x: -320 }}
              animate={{ x: 0 }}
              exit={{ x: -320 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/[0.08]">
                <div className="flex items-center gap-2.5">
                  <img
                    src="http://thecodingkid.oyosite.com/logo_only.png"
                    alt="Osis Logo"
                    className="h-6 w-6 drop-shadow-sm"
                  />
                  <span className="text-lg font-medium text-white tracking-tight">Osis</span>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 rounded-lg hover:bg-white/[0.08] transition-colors"
                >
                  <X className="w-5 h-5 text-white/70" />
                </button>
              </div>

              {/* Navigation Items */}
              <div className="p-4 space-y-2">
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => handleNavigation(item)}
                    className={cn(
                      "w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-all duration-200",
                      isActiveRoute(item.path)
                        ? "bg-white/[0.08] text-white border border-white/[0.05]"
                        : "text-white/70 hover:text-white hover:bg-white/[0.04]",
                      (isPageLocked(item.path) || item.isRestricted) ? "opacity-60" : ""
                    )}
                  >
                    <div className="flex-shrink-0">
                      {item.icon}
                    </div>
                    <span className="font-medium text-sm">{item.label}</span>
                    {item.isRestricted && (
                      <div className="ml-auto">
                        <Monitor className="w-4 h-4 text-white/40" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Modals */}
      <PaywallModal
        isOpen={showPaywall}
        onClose={() => setShowPaywall(false)}
        featureName={paywallFeature}
      />

      <MobileRestrictionModal
        isOpen={showMobileRestriction}
        onClose={() => setShowMobileRestriction(false)}
      />
    </>
  );
};

export default MobileNavigation;
