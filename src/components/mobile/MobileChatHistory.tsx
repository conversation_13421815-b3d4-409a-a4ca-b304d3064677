import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Trash2, Clock, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface ChatHistoryItem {
  id: string;
  title: string;
  timestamp: string;
  preview: string;
}

interface MobileChatHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  loadChatHistory: () => Promise<ChatHistoryItem[]>;
  onNewChat: () => void;
  onSelectChat: (chatId: string) => void;
}

const MobileChatHistory: React.FC<MobileChatHistoryProps> = ({
  isOpen,
  onClose,
  loadChatHistory,
  onNewChat,
  onSelectChat
}) => {
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Load chat history when component opens
  useEffect(() => {
    if (isOpen) {
      loadChats();
    }
  }, [isOpen]);

  const loadChats = async () => {
    setLoading(true);
    try {
      const chats = await loadChatHistory();
      setChatHistory(chats);
    } catch (error) {
      console.error('Error loading chats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Delete chat function
  const deleteChat = async (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent chat selection

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Delete chat messages first
      await supabase
        .from('messages')
        .delete()
        .eq('chat_id', chatId);

      // Delete the chat
      await supabase
        .from('chats')
        .delete()
        .eq('id', chatId)
        .eq('user_id', user.id);

      // Reload chat history
      await loadChats();
    } catch (error) {
      console.error('Error deleting chat:', error);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50">
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          <motion.div
            className="absolute right-0 top-0 h-full w-80 bg-[#121212] border-l border-white/[0.08] shadow-2xl"
            initial={{ x: 320 }}
            animate={{ x: 0 }}
            exit={{ x: 320 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/[0.08]">
              <div className="flex items-center gap-2.5">
                <Clock className="w-5 h-5 text-white/60" />
                <span className="text-lg font-medium text-white tracking-tight">Chat History</span>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-white/[0.08] transition-colors"
              >
                <X className="w-5 h-5 text-white/70" />
              </button>
            </div>

            {/* New Chat Button */}
            <div className="p-4 border-b border-white/[0.08]">
              <button
                onClick={onNewChat}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-white/[0.06] hover:bg-white/[0.1] border border-white/[0.08] hover:border-white/[0.12] rounded-lg text-white/80 hover:text-white transition-all duration-200"
              >
                <Plus className="w-4 h-4" />
                <span className="font-medium text-sm">New Chat</span>
              </button>
            </div>

            {/* Chat History List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 text-white/60 animate-spin" />
                </div>
              ) : chatHistory.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-white/40 text-sm mb-2">No conversations yet</div>
                  <div className="text-white/30 text-xs">Start a new chat to begin</div>
                </div>
              ) : (
                chatHistory.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => onSelectChat(chat.id)}
                    className="group p-4 rounded-lg bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 cursor-pointer"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="text-white/80 text-sm font-medium mb-1 truncate">
                          {chat.title}
                        </div>
                        <div className="text-white/50 text-xs mb-2">
                          {chat.timestamp}
                        </div>
                        <div className="text-white/40 text-xs line-clamp-2">
                          {chat.preview}
                        </div>
                      </div>
                      <button
                        onClick={(e) => deleteChat(chat.id, e)}
                        className="opacity-0 group-hover:opacity-100 ml-2 w-6 h-6 rounded-md bg-white/[0.06] hover:bg-red-500/20 border border-white/[0.08] hover:border-red-500/30 flex items-center justify-center transition-all duration-200"
                      >
                        <Trash2 className="w-3 h-3 text-white/60 hover:text-red-400" />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-white/[0.08]">
              <div className="text-center text-white/40 text-xs">
                {chatHistory.length} conversations
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default MobileChatHistory;
