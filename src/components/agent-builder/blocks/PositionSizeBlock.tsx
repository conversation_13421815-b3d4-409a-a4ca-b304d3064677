import React from 'react';
import { Handle, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Scale, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PositionSizeBlockProps {
  data: {
    id: string;
    method: 'fixed_amount' | 'percentage_account' | 'volatility_based' | 'risk_based';
    fixedAmount?: number;
    accountPercentage?: number;
    riskPercentage?: number;
    atrMultiplier?: number;
    outputConnections: string[];
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const PositionSizeBlock: React.FC<PositionSizeBlockProps> = ({ data, selected }) => {

  const handleUpdate = (field: string, value: any) => {
    data.onUpdate({ [field]: value });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-blue-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center">
              <Scale className="h-3 w-3 text-muted-foreground" />
            </div>
            <CardTitle className="text-sm font-medium">Position Sizing</CardTitle>
          </div>
          <div className="flex gap-1">

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                console.log('Delete button clicked for position size block:', data.id);
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-3">
          <div>
            <Label className="text-xs">Method</Label>
            <Select value={data.method} onValueChange={(value) => handleUpdate('method', value)}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fixed_amount">Fixed Amount</SelectItem>
                <SelectItem value="percentage_account">% of Account</SelectItem>
                <SelectItem value="volatility_based">Volatility Based</SelectItem>
                <SelectItem value="risk_based">Risk Based</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {data.method === 'fixed_amount' && (
            <div>
              <Label className="text-xs">Fixed Amount ($)</Label>
              <Input
                type="number"
                value={data.fixedAmount || 1000}
                onChange={(e) => handleUpdate('fixedAmount', parseFloat(e.target.value) || 1000)}
                className="h-8"
                min="1"
                step="100"
              />
            </div>
          )}

          {data.method === 'percentage_account' && (
            <div>
              <Label className="text-xs">Account Percentage (%)</Label>
              <Input
                type="number"
                value={data.accountPercentage || 2}
                onChange={(e) => handleUpdate('accountPercentage', parseFloat(e.target.value) || 2)}
                className="h-8"
                min="0.1"
                max="100"
                step="0.1"
              />
            </div>
          )}

          {data.method === 'risk_based' && (
            <div>
              <Label className="text-xs">Risk Percentage (%)</Label>
              <Input
                type="number"
                value={data.riskPercentage || 1}
                onChange={(e) => handleUpdate('riskPercentage', parseFloat(e.target.value) || 1)}
                className="h-8"
                min="0.1"
                max="10"
                step="0.1"
              />
            </div>
          )}

          {data.method === 'volatility_based' && (
            <div>
              <Label className="text-xs">ATR Multiplier</Label>
              <Input
                type="number"
                value={data.atrMultiplier || 2}
                onChange={(e) => handleUpdate('atrMultiplier', parseFloat(e.target.value) || 2)}
                className="h-8"
                min="0.5"
                max="10"
                step="0.1"
              />
            </div>
          )}

            <div className="text-xs text-muted-foreground">
              {data.method === 'fixed_amount' && `$${data.fixedAmount || 1000} fixed`}
              {data.method === 'percentage_account' && `${data.accountPercentage || 2}% of account`}
              {data.method === 'risk_based' && `${data.riskPercentage || 1}% risk per trade`}
              {data.method === 'volatility_based' && `${data.atrMultiplier || 2}x ATR based`}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PositionSizeBlock;
