import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { BarChart3, Trash2, Star, Settings } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface CandlePatternBlockProps {
  data: {
    id: string;
    pattern: string;
    timeframe?: string;
    inputConnections: string[];
    trueConnection?: string;
    falseConnection?: string;
    bullishConnection?: string;
    bearishConnection?: string;
    neutralConnection?: string;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const CandlePatternBlock: React.FC<CandlePatternBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available candle patterns - Updated to match implemented patterns
  const patterns = [
    { value: 'any', label: 'Any Pattern', description: 'Detect any recognizable pattern' },
    { value: 'doji', label: 'Doji', description: 'Indecision pattern with small body' },
    { value: 'hammer', label: 'Hammer', description: 'Bullish reversal with long lower shadow' },
    { value: 'shooting_star', label: 'Shooting Star', description: 'Bearish reversal with long upper shadow' },
    { value: 'marubozu', label: 'Marubozu', description: 'Strong directional candle with minimal wicks' },
    { value: 'engulfing', label: 'Engulfing', description: 'Current candle engulfs previous candle' },
    { value: 'bullish_engulfing', label: 'Bullish Engulfing', description: 'Bullish candle engulfs bearish candle' },
    { value: 'bearish_engulfing', label: 'Bearish Engulfing', description: 'Bearish candle engulfs bullish candle' },
    { value: 'morning_star', label: 'Morning Star', description: 'Three-candle bullish reversal' },
    { value: 'evening_star', label: 'Evening Star', description: 'Three-candle bearish reversal' },
    { value: 'bullish', label: 'Any Bullish Pattern', description: 'Detect any bullish reversal pattern' },
    { value: 'bearish', label: 'Any Bearish Pattern', description: 'Detect any bearish reversal pattern' },
    { value: 'reversal', label: 'Any Reversal Pattern', description: 'Detect any reversal pattern' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: 'hour', label: 'Hourly' },
    { value: '15minute', label: '15 Minutes' },
    { value: '5minute', label: '5 Minutes' },
    { value: '1minute', label: '1 Minute' }
  ];

  // Handle pattern change
  const handlePatternChange = (value: string) => {
    data.onUpdate({ pattern: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Get the current pattern
  const currentPattern = patterns.find(p => p.value === data.pattern) || patterns[0];
  const isAnyPattern = data.pattern === 'any';

  // Render error messages
  const renderErrorMessages = () => {
    if (!data.hasError || !data.errorMessages || data.errorMessages.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center gap-2 text-red-700 font-medium text-xs mb-1">
          <span>⚠️ Errors:</span>
        </div>
        <ul className="text-red-600 text-xs space-y-1">
          {data.errorMessages.map((message, index) => (
            <li key={index}>• {message}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: data.hasError ? '#ef4444' : '#555',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      {/* Output handles - conditional based on pattern type */}
      {isAnyPattern ? (
        <>
          {/* Bullish output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="bullish"
            style={{
              background: data.hasError ? '#ef4444' : '#22c55e',
              top: '30%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />

          {/* Bearish output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="bearish"
            style={{
              background: data.hasError ? '#ef4444' : '#ef4444',
              top: '50%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />

          {/* Neutral output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="neutral"
            style={{
              background: data.hasError ? '#ef4444' : '#6b7280',
              top: '70%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />
        </>
      ) : (
        <>
          {/* True output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="true"
            style={{
              background: data.hasError ? '#ef4444' : '#22c55e',
              top: '40%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />

          {/* False output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="false"
            style={{
              background: data.hasError ? '#ef4444' : '#ef4444',
              top: '60%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />
        </>
      )}

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <BarChart3 className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Candle Pattern</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title={isEditing ? "Close editor" : "Edit block"}
            >
              <Settings className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium block mb-1">Pattern</label>
              <Select
                value={data.pattern}
                onValueChange={handlePatternChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select pattern" />
                </SelectTrigger>
                <SelectContent>
                  {patterns.map(pattern => (
                    <SelectItem key={pattern.value} value={pattern.value} className="text-xs">
                      {pattern.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-xs font-medium block mb-1">Timeframe</label>
              <Select
                value={data.timeframe || 'day'}
                onValueChange={handleTimeframeChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map(tf => (
                    <SelectItem key={tf.value} value={tf.value} className="text-xs">
                      {tf.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {isEditing && (
              <div className="mt-2">
                <p className="text-xs text-muted-foreground mb-2">
                  {currentPattern.description}
                </p>

                <div className="flex flex-col gap-2 mt-3">
                  {isAnyPattern ? (
                    <>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="text-xs">Bullish patterns</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <span className="text-xs">Bearish patterns</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                        <span className="text-xs">Neutral patterns</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="text-xs">Pattern detected</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <span className="text-xs">Pattern not detected</span>
                      </div>
                    </>
                  )}
                </div>

                <div className="mt-3 text-xs text-muted-foreground">
                  <p>Connect price data to the input to analyze candlestick patterns.</p>
                  <p className="mt-1">This block analyzes the most recent candle for pattern matching.</p>
                </div>
              </div>
            )}

            {/* Display error messages */}
            {renderErrorMessages()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CandlePatternBlock;
