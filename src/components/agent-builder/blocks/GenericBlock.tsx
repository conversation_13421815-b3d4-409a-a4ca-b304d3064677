import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Activity, LineChart, Volume2, Gauge, Shield, Clock, Calendar, Globe, Users,
  Layers, Scale, Percent, TrendingUpDown, AlertTriangle, Timer, Building,
  Crosshair, ArrowUpDown, RotateCcw, PieChart, BarChart2, TrendingUp, Trash2, Star, Terminal
} from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';
import { AgentBlock } from '@/services/agentService';

interface GenericBlockProps {
  data: {
    id: string;
    type: string;
    timeframe?: string;
    period?: number;
    indicator?: string;
    method?: string;
    threshold?: number;
    percentage?: number;
    isEntryBlock?: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
    [key: string]: any; // Allow additional properties
  };
  selected: boolean;
}

const GenericBlock: React.FC<GenericBlockProps> = ({ data, selected }) => {
  const getBlockInfo = (type: string) => {
    switch (type) {
      case BlockType.TREND_INDICATOR:
        return { title: 'Trend Indicator', icon: TrendingUpDown, description: 'MACD, ADX, Parabolic SAR, Ichimoku' };
      case BlockType.VOLUME_INDICATOR:
        return { title: 'Volume Indicator', icon: Volume2, description: 'Volume Profile, OBV, VWAP, MFI' };
      case BlockType.VOLATILITY_INDICATOR:
        return { title: 'Volatility Indicator', icon: Gauge, description: 'Bollinger Bands, ATR, Keltner Channels' };
      case BlockType.SUPPORT_RESISTANCE:
        return { title: 'Support/Resistance', icon: BarChart2, description: 'Pivot points, Fibonacci levels' };
      case BlockType.CHART_PATTERN:
        return { title: 'Chart Pattern', icon: TrendingUp, description: 'Triangles, flags, head & shoulders' };
      case BlockType.BREAKOUT_DETECTION:
        return { title: 'Breakout Detection', icon: ArrowUpDown, description: 'Price breaking key levels' };
      case BlockType.GAP_ANALYSIS:
        return { title: 'Gap Analysis', icon: Crosshair, description: 'Gap up/down detection' };
      case BlockType.STOP_LOSS:
        return { title: 'Stop Loss', icon: Shield, description: 'Risk management stop loss' };
      case BlockType.TAKE_PROFIT:
        return { title: 'Take Profit', icon: Percent, description: 'Profit taking targets' };
      case BlockType.RISK_LIMIT:
        return { title: 'Risk Limits', icon: AlertTriangle, description: 'Daily loss and exposure limits' };
      case BlockType.TIME_FILTER:
        return { title: 'Time Filter', icon: Clock, description: 'Time-based trading filters' };
      case BlockType.SESSION_FILTER:
        return { title: 'Session Filter', icon: Timer, description: 'Market session filters' };
      case BlockType.ECONOMIC_CALENDAR:
        return { title: 'Economic Calendar', icon: Calendar, description: 'Economic events filter' };
      case BlockType.TREND_DETECTION:
        return { title: 'Trend Detection', icon: TrendingUpDown, description: 'Market trend analysis' };
      case BlockType.VOLATILITY_REGIME:
        return { title: 'Volatility Regime', icon: Gauge, description: 'Volatility environment detection' };
      case BlockType.MARKET_SENTIMENT:
        return { title: 'Market Sentiment', icon: Users, description: 'VIX, put/call ratio analysis' };
      case BlockType.MARKET_BREADTH:
        return { title: 'Market Breadth', icon: Globe, description: 'Advance/decline indicators' };
      case BlockType.MULTI_TIMEFRAME:
        return { title: 'Multi-Timeframe', icon: Layers, description: 'Multi-timeframe confirmation' };
      case BlockType.CONFLUENCE:
        return { title: 'Confluence', icon: Building, description: 'Multiple signal alignment' };
      case BlockType.SCALE_STRATEGY:
        return { title: 'Scale Strategy', icon: RotateCcw, description: 'Scale-in/out strategies' };
      case BlockType.PARTIAL_PROFIT:
        return { title: 'Partial Profit', icon: PieChart, description: 'Partial profit taking' };
      case BlockType.CONSOLE_LOG:
        return { title: 'Console Log', icon: Terminal, description: 'Debug logging for agent execution' };
      default:
        return { title: 'Trading Block', icon: Activity, description: 'Trading strategy component' };
    }
  };

  const blockInfo = getBlockInfo(data.type);
  const IconComponent = blockInfo.icon;

  // Determine if this block has input/output connections
  const hasInput = data.inputConnections !== undefined ||
                   ['TREND_INDICATOR', 'VOLUME_INDICATOR', 'VOLATILITY_INDICATOR', 'SUPPORT_RESISTANCE',
                    'CHART_PATTERN', 'BREAKOUT_DETECTION', 'GAP_ANALYSIS', 'TIME_FILTER', 'SESSION_FILTER',
                    'ECONOMIC_CALENDAR', 'TREND_DETECTION', 'VOLATILITY_REGIME', 'MARKET_SENTIMENT',
                    'MARKET_BREADTH', 'RISK_LIMIT'].includes(data.type);

  const hasOutput = data.outputConnections !== undefined ||
                    !['PARTIAL_PROFIT'].includes(data.type);

  // Handle property updates
  const handleUpdate = (field: string, value: any) => {
    data.onUpdate({ [field]: value });
  };

  // Get timeframe options
  const timeframeOptions = [
    { value: '1min', label: '1 min' },
    { value: '5min', label: '5 min' },
    { value: '15min', label: '15 min' },
    { value: '1hour', label: '1 hour' },
    { value: '4hour', label: '4 hour' },
    { value: 'day', label: 'Daily' }
  ];

  // Render configuration based on block type
  const renderConfiguration = () => {
    // Always show configuration since settings are always open

    const commonTimeframe = (
      <div>
        <Label className="text-xs">Timeframe</Label>
        <Select value={data.timeframe || '1hour'} onValueChange={(value) => handleUpdate('timeframe', value)}>
          <SelectTrigger className="h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {timeframeOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );

    const commonPeriod = (
      <div>
        <Label className="text-xs">Period</Label>
        <Input
          type="number"
          value={data.period || 20}
          onChange={(e) => handleUpdate('period', parseInt(e.target.value) || 20)}
          className="h-8"
          min="1"
          max="200"
        />
      </div>
    );

    switch (data.type) {
      case BlockType.TREND_INDICATOR:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Indicator</Label>
              <Select value={data.indicator || 'macd'} onValueChange={(value) => handleUpdate('indicator', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="macd">MACD</SelectItem>
                  <SelectItem value="adx">ADX</SelectItem>
                  <SelectItem value="parabolic_sar">Parabolic SAR</SelectItem>
                  <SelectItem value="ichimoku">Ichimoku</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            {data.indicator === 'macd' && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Fast Period</Label>
                  <Input
                    type="number"
                    value={data.fastPeriod || 12}
                    onChange={(e) => handleUpdate('fastPeriod', parseInt(e.target.value) || 12)}
                    className="h-8"
                    min="1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Slow Period</Label>
                  <Input
                    type="number"
                    value={data.slowPeriod || 26}
                    onChange={(e) => handleUpdate('slowPeriod', parseInt(e.target.value) || 26)}
                    className="h-8"
                    min="1"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case BlockType.VOLUME_INDICATOR:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Indicator</Label>
              <Select value={data.indicator || 'vwap'} onValueChange={(value) => handleUpdate('indicator', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vwap">VWAP</SelectItem>
                  <SelectItem value="obv">OBV</SelectItem>
                  <SelectItem value="volume_profile">Volume Profile</SelectItem>
                  <SelectItem value="mfi">Money Flow Index</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            {data.indicator === 'mfi' && commonPeriod}
          </div>
        );

      case BlockType.VOLATILITY_INDICATOR:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Indicator</Label>
              <Select value={data.indicator || 'bollinger_bands'} onValueChange={(value) => handleUpdate('indicator', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bollinger_bands">Bollinger Bands</SelectItem>
                  <SelectItem value="atr">ATR</SelectItem>
                  <SelectItem value="keltner_channels">Keltner Channels</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            {commonPeriod}
            {(data.indicator === 'bollinger_bands' || data.indicator === 'keltner_channels') && (
              <div>
                <Label className="text-xs">Multiplier</Label>
                <Input
                  type="number"
                  value={data.multiplier || 2}
                  onChange={(e) => handleUpdate('multiplier', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.1"
                  step="0.1"
                />
              </div>
            )}
          </div>
        );

      case BlockType.TIME_FILTER:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Filter Type</Label>
              <Select value={data.filterType || 'time_of_day'} onValueChange={(value) => handleUpdate('filterType', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="time_of_day">Time of Day</SelectItem>
                  <SelectItem value="day_of_week">Day of Week</SelectItem>
                  <SelectItem value="market_open_close">Market Open/Close</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {data.filterType === 'time_of_day' && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Start Time</Label>
                  <Input
                    type="time"
                    value={data.startTime || '09:30'}
                    onChange={(e) => handleUpdate('startTime', e.target.value)}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-xs">End Time</Label>
                  <Input
                    type="time"
                    value={data.endTime || '16:00'}
                    onChange={(e) => handleUpdate('endTime', e.target.value)}
                    className="h-8"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case BlockType.STOP_LOSS:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Method</Label>
              <Select value={data.method || 'percentage'} onValueChange={(value) => handleUpdate('method', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed_price">Fixed Price</SelectItem>
                  <SelectItem value="atr_based">ATR Based</SelectItem>
                  <SelectItem value="trailing">Trailing</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {data.method === 'percentage' && (
              <div>
                <Label className="text-xs">Percentage (%)</Label>
                <Input
                  type="number"
                  value={data.percentage || 2}
                  onChange={(e) => handleUpdate('percentage', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.1"
                  max="50"
                  step="0.1"
                />
              </div>
            )}
            {data.method === 'atr_based' && (
              <div>
                <Label className="text-xs">ATR Multiplier</Label>
                <Input
                  type="number"
                  value={data.atrMultiplier || 2}
                  onChange={(e) => handleUpdate('atrMultiplier', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.5"
                  max="10"
                  step="0.1"
                />
              </div>
            )}
          </div>
        );

      case BlockType.TAKE_PROFIT:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Method</Label>
              <Select value={data.method || 'risk_reward_ratio'} onValueChange={(value) => handleUpdate('method', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="risk_reward_ratio">Risk/Reward Ratio</SelectItem>
                  <SelectItem value="fixed_price">Fixed Price</SelectItem>
                  <SelectItem value="multiple_targets">Multiple Targets</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {data.method === 'risk_reward_ratio' && (
              <div>
                <Label className="text-xs">Risk/Reward Ratio</Label>
                <Input
                  type="number"
                  value={data.riskRewardRatio || 2}
                  onChange={(e) => handleUpdate('riskRewardRatio', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.5"
                  max="10"
                  step="0.1"
                />
              </div>
            )}
          </div>
        );

      case BlockType.BREAKOUT_DETECTION:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Level Type</Label>
              <Select value={data.level || 'resistance'} onValueChange={(value) => handleUpdate('level', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="support">Support</SelectItem>
                  <SelectItem value="resistance">Resistance</SelectItem>
                  <SelectItem value="range_high">Range High</SelectItem>
                  <SelectItem value="range_low">Range Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            <div>
              <Label className="text-xs">Volume Confirmation</Label>
              <Select value={data.volumeConfirmation ? 'true' : 'false'} onValueChange={(value) => handleUpdate('volumeConfirmation', value === 'true')}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Required</SelectItem>
                  <SelectItem value="false">Not Required</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case BlockType.CHART_PATTERN:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Pattern Type</Label>
              <Select value={data.pattern || 'any'} onValueChange={(value) => handleUpdate('pattern', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any Pattern</SelectItem>
                  <SelectItem value="triangle">Triangle</SelectItem>
                  <SelectItem value="flag">Flag</SelectItem>
                  <SelectItem value="pennant">Pennant</SelectItem>
                  <SelectItem value="head_and_shoulders">Head and Shoulders</SelectItem>
                  <SelectItem value="double_top">Double Top</SelectItem>
                  <SelectItem value="double_bottom">Double Bottom</SelectItem>
                  <SelectItem value="cup_and_handle">Cup and Handle</SelectItem>
                  <SelectItem value="wedge">Wedge</SelectItem>
                  <SelectItem value="channel">Channel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            <div>
              <Label className="text-xs">Lookback Period</Label>
              <Input
                type="number"
                value={data.lookbackPeriod || 50}
                onChange={(e) => handleUpdate('lookbackPeriod', parseInt(e.target.value) || 50)}
                className="h-8"
                min="10"
                max="200"
              />
            </div>
            <div>
              <Label className="text-xs">Min Pattern Size</Label>
              <Input
                type="number"
                value={data.minPatternSize || 10}
                onChange={(e) => handleUpdate('minPatternSize', parseInt(e.target.value) || 10)}
                className="h-8"
                min="5"
                max="50"
              />
            </div>
          </div>
        );

      case BlockType.SUPPORT_RESISTANCE:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Output Type</Label>
              <Select value={data.outputType || 'support'} onValueChange={(value) => handleUpdate('outputType', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="support">Support Level</SelectItem>
                  <SelectItem value="resistance">Resistance Level</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            <div>
              <Label className="text-xs">Lookback Period</Label>
              <Input
                type="number"
                value={data.lookbackPeriod || 20}
                onChange={(e) => handleUpdate('lookbackPeriod', parseInt(e.target.value) || 20)}
                className="h-8"
                min="5"
                max="100"
              />
            </div>
            <div>
              <Label className="text-xs">Strength (Min Touches)</Label>
              <Input
                type="number"
                value={data.strength || 2}
                onChange={(e) => handleUpdate('strength', parseInt(e.target.value) || 2)}
                className="h-8"
                min="1"
                max="10"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-2">
            {data.timeframe !== undefined && commonTimeframe}
            {data.period !== undefined && commonPeriod}
            {data.threshold !== undefined && (
              <div>
                <Label className="text-xs">Threshold</Label>
                <Input
                  type="number"
                  value={data.threshold || 0}
                  onChange={(e) => handleUpdate('threshold', parseFloat(e.target.value) || 0)}
                  className="h-8"
                  step="0.1"
                />
              </div>
            )}
            {data.method !== undefined && (
              <div>
                <Label className="text-xs">Method</Label>
                <Input
                  value={data.method || ''}
                  onChange={(e) => handleUpdate('method', e.target.value)}
                  className="h-8"
                  placeholder="Enter method"
                />
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      {hasInput && (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          style={{
            background: '#3b82f6',
            width: '12px',
            height: '12px',
            border: '2px solid white'
          }}
        />
      )}

      {/* Output handle */}
      {hasOutput && (
        <Handle
          type="source"
          position={Position.Right}
          id="output"
          style={{
            background: '#3b82f6',
            width: '12px',
            height: '12px',
            border: '2px solid white'
          }}
        />
      )}

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <IconComponent className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">{blockInfo.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                console.log('Delete button clicked for block:', data.id);
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              {blockInfo.description}
            </div>

            {/* Configuration interface - always shown */}
            {renderConfiguration()}

            {/* Error messages */}
            {data.hasError && data.errorMessages && data.errorMessages.length > 0 && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center gap-2 text-red-700 font-medium text-xs mb-1">
                  <span>⚠️ Errors:</span>
                </div>
                <ul className="text-red-600 text-xs space-y-1">
                  {data.errorMessages.map((message, index) => (
                    <li key={index}>• {message}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GenericBlock;
