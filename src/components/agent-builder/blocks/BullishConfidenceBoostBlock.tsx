import React, { useState } from 'react';
import { <PERSON>le, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Settings, Trash2, Star, Plus } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface BullishConfidenceBoostBlockProps {
  data: {
    id: string;
    percentage: number;
    inputConnections: string[];
    outputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const BullishConfidenceBoostBlock: React.FC<BullishConfidenceBoostBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Handle percentage change
  const handlePercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    data.onUpdate({ percentage: Math.max(0, value) });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: '#22c55e',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-green-500/10 flex items-center justify-center">
              <Plus className="h-3 w-3 text-green-500" />
            </div>
            <CardTitle className="text-sm font-medium">+{data.percentage}% Bullish</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title="Edit block"
            >
              <Settings className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive hover:text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              Increases bullish confidence by {data.percentage}%
            </div>

            {isEditing && (
              <div className="space-y-3 pt-2 border-t">
                <div className="space-y-2">
                  <Label htmlFor="percentage" className="text-xs">Confidence Boost Percentage</Label>
                  <Input
                    id="percentage"
                    type="number"
                    min="0"
                    step="0.1"
                    value={data.percentage}
                    onChange={handlePercentageChange}
                    className="h-8 text-xs"
                    placeholder="e.g., 10.0"
                  />
                  <p className="text-xs text-muted-foreground">
                    Enter the percentage to boost bullish confidence (e.g., 10 for +10%)
                  </p>
                </div>
              </div>
            )}

            {isEditing && (
              <div className="mt-2 p-2 bg-muted/50 rounded-md">
                <p className="text-xs text-muted-foreground">
                  This block increases the bullish confidence of the trading signal by the specified percentage.
                  Multiple confidence boost blocks can be chained together to create nuanced signal adjustments.
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <Plus className="h-3 w-3 text-green-500" />
                  <span className="text-xs font-medium text-green-600">
                    Boosts bullish confidence by +{data.percentage}%
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BullishConfidenceBoostBlock;
