import React, { useState } from 'react';
import { <PERSON>le, Position } from 'reactflow';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Terminal, Settings, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/hooks/useAgentBuilder';

interface ConsoleLogBlockProps {
  data: {
    id: string;
    message: string;
    logValue: boolean;
    inputConnections: string[];
    outputConnections: string[];
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
    isEntryBlock?: boolean;
  };
  selected: boolean;
}

const ConsoleLog<PERSON>lock: React.FC<ConsoleLogBlockProps> = ({ data, selected }) => {

  const handleUpdate = (field: string, value: any) => {
    data.onUpdate({ [field]: value });
  };

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#555',
          border: '2px solid #fff',
          width: '12px',
          height: '12px',
          left: '-6px'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center">
              <Terminal className="h-3 w-3 text-orange-600" />
            </div>
            <CardTitle className="text-sm font-medium">Console Log</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-red-500 hover:text-red-700"
              onClick={() => {
                console.log('Delete button clicked for console log block:', data.id);
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-3">
            <div>
              <Label className="text-xs">Log Message</Label>
              <Input
                type="text"
                value={data.message}
                onChange={(e) => handleUpdate('message', e.target.value)}
                placeholder="Enter debug message"
                className="h-8"
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Log Input Value</Label>
              <Switch
                checked={data.logValue}
                onCheckedChange={(checked) => handleUpdate('logValue', checked)}
              />
            </div>
          </div>

          {/* Error display */}
          {data.hasError && data.errorMessages && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
              {data.errorMessages.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </div>
          )}

          {/* Connection info */}
          {data.isDisconnected && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-600">
              This block is not connected to the agent flow
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#555',
          border: '2px solid #fff',
          width: '12px',
          height: '12px',
          right: '-6px'
        }}
      />
    </>
  );
};

export default ConsoleLogBlock;
