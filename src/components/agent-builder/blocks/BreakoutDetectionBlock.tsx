import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ArrowUpDown, Star, Trash2, ChevronDown } from 'lucide-react';
import { AgentBlock } from '@/types/agent';

interface BreakoutDetectionBlockProps {
  data: {
    id: string;
    breakoutType: string;
    timeframe?: string;
    lookbackPeriod?: number;
    volumeConfirmation?: boolean;
    minBreakoutSize?: number;
    inputConnections: string[];
    trueConnection?: string;
    falseConnection?: string;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const BreakoutDetectionBlock: React.FC<BreakoutDetectionBlockProps> = ({ data, selected }) => {
  // State for collapsible "How this block works" section
  const [isHelpOpen, setIsHelpOpen] = React.useState(false);

  // Available breakout types
  const breakoutTypes = [
    { value: 'support_resistance', label: 'Support/Resistance Breakout' },
    { value: 'trend_line', label: 'Trend Line Breakout' },
    { value: 'range', label: 'Range Breakout' },
    { value: 'consolidation', label: 'Consolidation Breakout' },
    { value: 'any', label: 'Any Breakout' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: '4hour', label: '4 Hours' },
    { value: '1hour', label: '1 Hour' },
    { value: '15min', label: '15 Minutes' },
    { value: '5min', label: '5 Minutes' },
    { value: '1min', label: '1 Minute' }
  ];

  // Handle breakout type change
  const handleBreakoutTypeChange = (value: string) => {
    data.onUpdate({ breakoutType: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Handle lookback period change
  const handleLookbackChange = (value: string) => {
    const numValue = parseInt(value) || 20;
    data.onUpdate({ lookbackPeriod: numValue });
  };

  // Handle min breakout size change
  const handleMinBreakoutSizeChange = (value: string) => {
    const numValue = parseFloat(value) || 0.5;
    data.onUpdate({ minBreakoutSize: numValue });
  };

  // Handle volume confirmation toggle
  const handleVolumeConfirmationChange = (checked: boolean) => {
    data.onUpdate({ volumeConfirmation: checked });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Breakout output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="true"
        style={{
          background: data.hasError ? '#ef4444' : '#22c55e',
          top: '40%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* No breakout output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="false"
        style={{
          background: data.hasError ? '#ef4444' : '#ef4444',
          top: '60%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <ArrowUpDown className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Breakout Detection</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              Detects price breakouts from key levels with volume confirmation
            </div>

            {/* Breakout type selection */}
            <div>
              <Label className="text-xs">Breakout Type</Label>
              <Select value={data.breakoutType || 'any'} onValueChange={handleBreakoutTypeChange}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {breakoutTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Timeframe selection */}
            <div>
              <Label className="text-xs">Timeframe</Label>
              <Select value={data.timeframe || 'day'} onValueChange={handleTimeframeChange}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map(timeframe => (
                    <SelectItem key={timeframe.value} value={timeframe.value}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Lookback period */}
            <div>
              <Label className="text-xs">Lookback Period</Label>
              <Input
                type="number"
                value={data.lookbackPeriod || 20}
                onChange={(e) => handleLookbackChange(e.target.value)}
                className="h-8"
                min="5"
                max="100"
              />
            </div>

            {/* Min breakout size */}
            <div>
              <Label className="text-xs">Min Breakout Size (%)</Label>
              <Input
                type="number"
                value={data.minBreakoutSize || 0.5}
                onChange={(e) => handleMinBreakoutSizeChange(e.target.value)}
                className="h-8"
                min="0.1"
                max="10"
                step="0.1"
              />
            </div>

            {/* Volume confirmation */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="volumeConfirmation"
                checked={data.volumeConfirmation || false}
                onChange={(e) => handleVolumeConfirmationChange(e.target.checked)}
                className="h-3 w-3"
              />
              <Label htmlFor="volumeConfirmation" className="text-xs">
                Require Volume Confirmation
              </Label>
            </div>

            {/* How this block works - collapsible */}
            <Collapsible open={isHelpOpen} onOpenChange={setIsHelpOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto text-xs text-muted-foreground hover:text-foreground">
                  <span>How this block works</span>
                  <ChevronDown className={`h-3 w-3 transition-transform ${isHelpOpen ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2 mt-2">
                <div className="text-xs text-muted-foreground">
                  <div className="mb-2">
                    <div className="font-medium mb-1">Breakout Analysis:</div>
                    <p>Analyzes price movements to detect breakouts from key levels like support, resistance, trend lines, or consolidation ranges.</p>
                  </div>
                  
                  <div className="mb-2">
                    <div className="font-medium mb-1">Output Terminals:</div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span>Breakout Detected - Routes when a valid breakout is confirmed</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        <span>No Breakout - Routes when no breakout is detected</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="font-medium mb-1">Configuration:</div>
                    <ul className="space-y-1 text-xs">
                      <li>• <strong>Breakout Type:</strong> Type of breakout to detect</li>
                      <li>• <strong>Timeframe:</strong> Chart timeframe for analysis</li>
                      <li>• <strong>Lookback Period:</strong> Number of bars to analyze (5-100)</li>
                      <li>• <strong>Min Breakout Size:</strong> Minimum breakout percentage (0.1-10%)</li>
                      <li>• <strong>Volume Confirmation:</strong> Require volume spike for validation</li>
                    </ul>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Error messages */}
            {data.hasError && data.errorMessages && data.errorMessages.length > 0 && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center gap-2 text-red-700 font-medium text-xs mb-1">
                  <span>⚠️ Errors:</span>
                </div>
                <ul className="text-red-600 text-xs space-y-1">
                  {data.errorMessages.map((message, index) => (
                    <li key={index}>• {message}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BreakoutDetectionBlock;
