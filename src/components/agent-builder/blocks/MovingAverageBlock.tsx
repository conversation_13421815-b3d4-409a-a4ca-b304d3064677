import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { LineChart, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface MovingAverageBlockProps {
  data: {
    id: string;
    averageType: 'sma' | 'ema' | 'wma';
    period: number;
    timeframe: string;
    source: 'close' | 'open' | 'high' | 'low' | 'hl2' | 'hlc3' | 'ohlc4';
    outputConnections: string[];
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const MovingAverageBlock: React.FC<MovingAverageBlockProps> = ({ data, selected }) => {

  const handleUpdate = (field: string, value: any) => {
    data.onUpdate({ [field]: value });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-blue-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center">
              <LineChart className="h-3 w-3 text-muted-foreground" />
            </div>
            <CardTitle className="text-sm font-medium">Moving Average</CardTitle>
          </div>
          <div className="flex gap-1">

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                console.log('Delete button clicked for moving average block:', data.id);
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-3">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Type</Label>
              <Select value={data.averageType} onValueChange={(value) => handleUpdate('averageType', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sma">SMA</SelectItem>
                  <SelectItem value="ema">EMA</SelectItem>
                  <SelectItem value="wma">WMA</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Period</Label>
              <Input
                type="number"
                value={data.period}
                onChange={(e) => handleUpdate('period', parseInt(e.target.value) || 20)}
                className="h-8"
                min="1"
                max="200"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Timeframe</Label>
              <Select value={data.timeframe} onValueChange={(value) => handleUpdate('timeframe', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1min">1 min</SelectItem>
                  <SelectItem value="5min">5 min</SelectItem>
                  <SelectItem value="15min">15 min</SelectItem>
                  <SelectItem value="1hour">1 hour</SelectItem>
                  <SelectItem value="4hour">4 hour</SelectItem>
                  <SelectItem value="day">Daily</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Source</Label>
              <Select value={data.source} onValueChange={(value) => handleUpdate('source', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="close">Close</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="hl2">HL2</SelectItem>
                  <SelectItem value="hlc3">HLC3</SelectItem>
                  <SelectItem value="ohlc4">OHLC4</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

            <div className="text-xs text-muted-foreground">
              {data.averageType.toUpperCase()}({data.period}) on {data.timeframe} {data.source}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MovingAverageBlock;
