import React, { useCallback, useRef, useState } from 'react';
import ReactFlow, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeTypes,
  EdgeTypes,
  NodeChange,
  EdgeChange,
  ConnectionLineType,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';
import './agent-builder.css';
import { AgentBlock } from '@/services/agentService';
import { BlockType, ConnectionType } from '@/hooks/useAgentBuilder';
import IndicatorNode from './blocks/IndicatorBlock';
import PriceNode from './blocks/PriceBlock';
import FundamentalNode from './blocks/FundamentalBlock';
import ConditionNode from './blocks/ConditionBlock';
import TriggerNode from './blocks/TriggerBlock';
import OperatorNode from './blocks/OperatorBlock';
import WhenRunNode from './blocks/WhenRunBlock';
import BullishConfidenceBoostNode from './blocks/BullishConfidenceBoostBlock';
import BearishConfidenceBoostNode from './blocks/BearishConfidenceBoostBlock';
import CandlePatternNode from './blocks/CandlePatternBlock';
import ChartPatternNode from './blocks/ChartPatternBlock';
import BreakoutDetectionNode from './blocks/BreakoutDetectionBlock';
import GapAnalysisNode from './blocks/GapAnalysisBlock';
import ConsoleLogNode from './blocks/ConsoleLogBlock';
import MomentumIndicatorNode from './blocks/MomentumIndicatorBlock';
import MovingAverageNode from './blocks/MovingAverageBlock';
import PositionSizeNode from './blocks/PositionSizeBlock';
import SignalNode from './blocks/SignalBlock';
import GenericNode from './blocks/GenericBlock';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, Maximize, LayoutGrid } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define custom node types
const nodeTypes: NodeTypes = {
  [BlockType.WHEN_RUN]: WhenRunNode,
  [BlockType.INDICATOR]: IndicatorNode,
  [BlockType.PRICE]: PriceNode,
  [BlockType.FUNDAMENTAL]: FundamentalNode,
  [BlockType.CONDITION]: ConditionNode,
  [BlockType.TRIGGER]: TriggerNode,
  [BlockType.OPERATOR]: OperatorNode,
  [BlockType.BULLISH_CONFIDENCE_BOOST]: BullishConfidenceBoostNode,
  [BlockType.BEARISH_CONFIDENCE_BOOST]: BearishConfidenceBoostNode,
  [BlockType.CANDLE_PATTERN]: CandlePatternNode,
  [BlockType.CHART_PATTERN]: ChartPatternNode,
  [BlockType.BREAKOUT_DETECTION]: BreakoutDetectionNode,
  [BlockType.GAP_ANALYSIS]: GapAnalysisNode,
  [BlockType.CONSOLE_LOG]: ConsoleLogNode,
  [BlockType.MOMENTUM_INDICATOR]: MomentumIndicatorNode,
  [BlockType.MOVING_AVERAGE]: MovingAverageNode,
  [BlockType.POSITION_SIZE]: PositionSizeNode,
  [BlockType.SIGNAL]: SignalNode,
  // Use GenericNode for all other block types that don't have specific components
  [BlockType.TREND_INDICATOR]: GenericNode,
  [BlockType.VOLUME_INDICATOR]: GenericNode,
  [BlockType.VOLATILITY_INDICATOR]: GenericNode,
  [BlockType.SUPPORT_RESISTANCE]: GenericNode,
  [BlockType.STOP_LOSS]: GenericNode,
  [BlockType.TAKE_PROFIT]: GenericNode,
  [BlockType.RISK_LIMIT]: GenericNode,
  [BlockType.TIME_FILTER]: GenericNode,
  [BlockType.MARKET_BREADTH]: GenericNode,
  [BlockType.MULTI_TIMEFRAME]: GenericNode,
  [BlockType.CONFLUENCE]: GenericNode,
  [BlockType.SCALE_STRATEGY]: GenericNode,
  [BlockType.PARTIAL_PROFIT]: GenericNode,
  [BlockType.AND]: GenericNode,
  [BlockType.OR]: GenericNode
};

interface BuildCanvasProps {
  blocks: AgentBlock[];
  connections: any[];
  entryBlockId: string;
  onBlockUpdate: (id: string, properties: Partial<AgentBlock>) => void;
  onBlockRemove: (id: string) => void;
  onConnectionAdd: (connection: any) => boolean;
  onConnectionRemove: (connection: any) => void;
  onSetEntryBlock: (id: string) => boolean;
  addBlock: (type: string, position: { x: number; y: number }) => string;
  errorDetails?: Record<string, string[]>;
  disconnectedBlocks?: AgentBlock[];
}

const BuildCanvas: React.FC<BuildCanvasProps> = ({
  blocks,
  connections,
  entryBlockId,
  onBlockUpdate,
  onBlockRemove,
  onConnectionAdd,
  onConnectionRemove,
  onSetEntryBlock,
  addBlock,
  errorDetails = {},
  disconnectedBlocks = []
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // Convert blocks to ReactFlow nodes
  const initialNodes: Node[] = blocks.map((block, index) => {
    const hasError = !!errorDetails[block.id];
    const isDisconnected = disconnectedBlocks.some(b => b.id === block.id);

    // Ensure position is valid
    const position = block.position || { x: 0, y: 0 };
    const validPosition = {
      x: typeof position.x === 'number' && !isNaN(position.x) ? position.x : 100 + (index * 50),
      y: typeof position.y === 'number' && !isNaN(position.y) ? position.y : 100 + (index * 50)
    };

    return {
      id: block.id,
      type: block.type,
      position: validPosition,
      data: {
        ...block,
        isEntryBlock: block.id === entryBlockId,
        onUpdate: (properties: Partial<AgentBlock>) => onBlockUpdate(block.id, properties),
        onRemove: () => onBlockRemove(block.id),
        onSetAsEntry: () => onSetEntryBlock(block.id),
        hasError,
        isDisconnected,
        errorMessages: errorDetails[block.id] || []
      },
      className: hasError ? 'error-node' : ''
    };
  });

  // Convert connections to ReactFlow edges
  const initialEdges: Edge[] = connections.map((conn, index) => ({
    id: `e-${index}`,
    source: conn.sourceId,
    target: conn.targetId,
    sourceHandle: conn.sourceHandle,
    targetHandle: conn.targetHandle,
    type: 'default',
    animated: true
  }));

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Store the ReactFlow instance
  const [reactFlowInstance, setReactFlowInstance] = React.useState<any>(null);

  // Update nodes when blocks change - simplified to reduce glitchiness
  React.useEffect(() => {
    // Skip updates during dragging operations
    if (isDragging) return;

    // Save current viewport before updating nodes
    let currentViewport = { x: 0, y: 0, zoom: 1 };
    if (reactFlowInstance) {
      currentViewport = reactFlowInstance.getViewport();
    }

    // Only update if blocks have changed
    const updatedNodes = blocks.map(block => {
      const hasError = !!errorDetails[block.id];
      const isDisconnected = disconnectedBlocks.some(b => b.id === block.id);

      // Find the existing node to preserve its position
      const existingNode = nodes.find(node => node.id === block.id);

      return {
        id: block.id,
        type: block.type,
        // Preserve existing node position if available
        position: existingNode?.position || block.position,
        data: {
          ...block,
          isEntryBlock: block.id === entryBlockId,
          onUpdate: (properties: Partial<AgentBlock>) => onBlockUpdate(block.id, properties),
          onRemove: () => onBlockRemove(block.id),
          onSetAsEntry: () => onSetEntryBlock(block.id),
          hasError,
          isDisconnected,
          errorMessages: errorDetails[block.id] || []
        },
        className: hasError ? 'error-node' : ''
      };
    });

    // Only update if there are blocks to show
    if (updatedNodes.length > 0) {
      setNodes(updatedNodes);

      // Restore viewport immediately
      if (reactFlowInstance) {
        reactFlowInstance.setViewport(currentViewport);
      }
    }
  }, [blocks, entryBlockId, onBlockUpdate, onBlockRemove, onSetEntryBlock, errorDetails, disconnectedBlocks, reactFlowInstance]);

  // Organize blocks in a hierarchical layout
  const organizeBlocks = useCallback(() => {
    console.log("Organizing blocks...");
    if (!reactFlowInstance || blocks.length === 0) {
      console.log("No ReactFlow instance or blocks");
      return;
    }

    // Find the entry block (usually the "When Run" block)
    const entryBlock = blocks.find(block => block.id === entryBlockId);
    if (!entryBlock) {
      console.log("No entry block found");
      return;
    }

    // Create a map of blocks by ID for quick lookup
    const blocksMap = blocks.reduce((map, block) => {
      map[block.id] = block;
      return map;
    }, {} as Record<string, AgentBlock>);

    // Build a graph of connections
    const graph: Record<string, string[]> = {};
    blocks.forEach(block => {
      graph[block.id] = [];
    });

    // Populate the graph with connections
    blocks.forEach(block => {
      if ('outputConnections' in block) {
        const outputConnections = (block as any).outputConnections || [];
        outputConnections.forEach((targetId: string) => {
          if (graph[block.id]) {
            graph[block.id].push(targetId);
          }
        });
      }
    });

    // Perform a breadth-first traversal to organize blocks by level
    const visited = new Set<string>();
    const levels: string[][] = []; // Array of levels, each containing block IDs
    const queue: { id: string; level: number }[] = [{ id: entryBlock.id, level: 0 }];

    visited.add(entryBlock.id);

    while (queue.length > 0) {
      const { id, level } = queue.shift()!;

      // Ensure the level exists in our levels array
      if (!levels[level]) {
        levels[level] = [];
      }

      // Add the block to its level
      levels[level].push(id);

      // Add all unvisited neighbors to the queue
      const neighbors = graph[id] || [];
      for (const neighborId of neighbors) {
        if (!visited.has(neighborId)) {
          visited.add(neighborId);
          queue.push({ id: neighborId, level: level + 1 });
        }
      }
    }

    // Add any blocks that weren't visited (disconnected blocks) to the last level
    const unvisitedBlocks = blocks.filter(block => !visited.has(block.id));
    if (unvisitedBlocks.length > 0) {
      const lastLevel = levels.length;
      levels[lastLevel] = unvisitedBlocks.map(block => block.id);
    }

    // Calculate new positions for each block - left to right flow
    const LEVEL_HORIZONTAL_SPACING = 300; // Horizontal spacing between levels
    const BLOCK_VERTICAL_SPACING = 250; // Increased vertical spacing between blocks in the same level
    const START_X_OFFSET = 100; // Starting X offset to avoid edge of canvas
    const START_Y_OFFSET = 100; // Increased Y offset for more space at the top

    // Calculate new positions for each block
    const newPositions: Record<string, { x: number; y: number }> = {};

    levels.forEach((levelBlocks, levelIndex) => {
      const levelHeight = levelBlocks.length * BLOCK_VERTICAL_SPACING;
      const startY = -levelHeight / 2 + BLOCK_VERTICAL_SPACING / 2 + START_Y_OFFSET;

      levelBlocks.forEach((blockId, blockIndex) => {
        newPositions[blockId] = {
          x: START_X_OFFSET + levelIndex * LEVEL_HORIZONTAL_SPACING, // Levels go from left to right
          y: startY + blockIndex * BLOCK_VERTICAL_SPACING // Blocks in same level stacked vertically
        };
      });
    });

    // Update block positions
    const updatedBlocks = blocks.map(block => {
      if (newPositions[block.id]) {
        return {
          ...block,
          position: newPositions[block.id]
        };
      }
      return block;
    });

    // Update all blocks at once
    console.log("Updating block positions...");
    updatedBlocks.forEach(block => {
      onBlockUpdate(block.id, { position: block.position });
    });

    // Center the view on the organized blocks
    console.log("Fitting view...");
    setTimeout(() => {
      if (reactFlowInstance) {
        reactFlowInstance.fitView({ padding: 0.2 });
        console.log("Organization complete");
      }
    }, 100);
  }, [blocks, entryBlockId, onBlockUpdate, reactFlowInstance]);

  // Handle window resize without auto-rescaling
  React.useEffect(() => {
    // Only add the event listener if reactFlowInstance is initialized
    if (!reactFlowInstance) return;

    const handleResize = () => {
      // Instead of fitView, just update the flow to ensure it renders correctly
      const currentViewport = reactFlowInstance.getViewport();
      reactFlowInstance.setViewport(currentViewport);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [reactFlowInstance]);

  // Track if this is the first load
  const isFirstLoad = React.useRef(true);

  // Organize blocks only when component first mounts
  React.useEffect(() => {
    if (!reactFlowInstance || blocks.length === 0) return;

    // Only organize blocks on first load
    if (isFirstLoad.current) {
      // Use a short delay to ensure ReactFlow is fully initialized
      const timer = setTimeout(() => {
        // Automatically organize blocks when loaded
        console.log("Auto-organizing blocks on first load");
        organizeBlocks();
        isFirstLoad.current = false;
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [reactFlowInstance, blocks.length, organizeBlocks]); // Include dependencies but use isFirstLoad to control execution

  // Update edges when connections change - simplified to fix connection issues
  React.useEffect(() => {
    // Skip updates during dragging operations
    if (isDragging) return;

    // Create edges with subtle wave animation and no arrows
    const updatedEdges = connections.map((conn, index) => ({
      id: `e-${index}`,
      source: conn.sourceId,
      target: conn.targetId,
      sourceHandle: conn.sourceHandle,
      targetHandle: conn.targetHandle,
      type: 'smoothstep',
      animated: true,
      style: {
        strokeWidth: 1.5,
        stroke: 'rgba(16, 185, 129, 0.4)',
        strokeLinecap: 'round',
        strokeDasharray: '6,4',
        filter: 'drop-shadow(0 1px 2px rgba(16, 185, 129, 0.1))'
      }
      // No markerEnd - clean lines without arrows
    }));

    // Always update edges to ensure they're in sync with connections
    setEdges(updatedEdges);
  }, [connections, isDragging]);

  // Handle node changes (position, etc.)
  const handleNodesChange = useCallback(
    (changes: NodeChange[]) => {
      // Apply changes to the nodes state
      onNodesChange(changes);

      // Track dragging state and update block positions
      changes.forEach(change => {
        if (change.type === 'position') {
          // Update dragging state
          if (change.dragging !== undefined) {
            setIsDragging(change.dragging);
          }

          // Update block position when dragging is complete
          if (change.dragging === false && change.position) {
            // Update immediately without timeout to prevent delay
            onBlockUpdate(change.id, { position: change.position });
          }
        }
      });
    },
    [onNodesChange, onBlockUpdate]
  );

  // Handle edge changes - simplified to fix connection issues
  const handleEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      // Save current viewport before making changes
      let currentViewport = { x: 0, y: 0, zoom: 1 };
      if (reactFlowInstance) {
        currentViewport = reactFlowInstance.getViewport();
      }

      // Apply changes to the edges state
      onEdgesChange(changes);

      // Handle edge removal
      changes.forEach(change => {
        if (change.type === 'remove') {
          // Find the edge that was removed
          const edge = edges.find(e => e.id === change.id);
          if (edge) {
            // Remove the connection from our state
            onConnectionRemove({
              sourceId: edge.source,
              targetId: edge.target,
              sourceHandle: edge.sourceHandle || 'output',
              targetHandle: edge.targetHandle || 'input'
            });
          }
        }
      });

      // Restore viewport immediately
      if (reactFlowInstance) {
        reactFlowInstance.setViewport(currentViewport);
      }
    },
    [edges, onEdgesChange, onConnectionRemove, reactFlowInstance]
  );

  // Handle new connections - simplified to fix connection issues
  const handleConnect = useCallback(
    (connection: Connection) => {
      // Ensure we have valid source and target handles
      const sourceHandle = connection.sourceHandle || 'output';
      const targetHandle = connection.targetHandle || 'input';

      // Add the connection to our state
      const success = onConnectionAdd({
        sourceId: connection.source,
        targetId: connection.target,
        sourceHandle,
        targetHandle
      });

      // Only add the edge if the connection was successful
      if (success) {
        const newEdge = {
          ...connection,
          sourceHandle,
          targetHandle,
          id: `e-${connection.source}-${connection.target}-${sourceHandle}-${targetHandle}`,
          type: 'default',
          animated: false
        };
        setEdges(eds => addEdge(newEdge, eds));
      }
    },
    [onConnectionAdd, setEdges]
  );

  // Handle dropping a new block onto the canvas
  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const dragData = event.dataTransfer.getData('application/reactflow');

      if (!dragData || !reactFlowBounds) {
        return;
      }

      // Parse the drag data
      try {
        // Check if the data is JSON or just a string (for backward compatibility)
        let type, properties = {};

        try {
          const parsedData = JSON.parse(dragData);
          type = parsedData.type;
          properties = parsedData.properties || {};
        } catch {
          // If parsing fails, assume it's just the type string (old format)
          type = dragData;
        }

        if (!type) {
          return;
        }

        // Get the position where the block was dropped
        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        });

        // Ensure the position is valid
        if (isNaN(position.x) || isNaN(position.y)) {
          position.x = 100;
          position.y = 100;
        }

        // Add the new block with properties
        addBlock(type, position, properties);
      } catch (error) {
        console.error('Error handling block drop:', error);
      }
    },
    [addBlock, reactFlowInstance]
  );

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);


  // Fit view to see all nodes
  const handleFitView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Zoom in while preserving viewport center
  const handleZoomIn = useCallback(() => {
    if (reactFlowInstance) {
      requestAnimationFrame(() => {
        // Get current viewport
        const { x, y, zoom } = reactFlowInstance.getViewport();
        // Calculate new zoom level
        const newZoom = Math.min(zoom * 1.2, 1.5); // Increase zoom by 20%, max 1.5
        // Set viewport with new zoom but same position
        reactFlowInstance.setViewport({ x, y, zoom: newZoom }, { duration: 0 });
      });
    }
  }, [reactFlowInstance]);

  // Zoom out while preserving viewport center
  const handleZoomOut = useCallback(() => {
    if (reactFlowInstance) {
      requestAnimationFrame(() => {
        // Get current viewport
        const { x, y, zoom } = reactFlowInstance.getViewport();
        // Calculate new zoom level
        const newZoom = Math.max(zoom * 0.8, 0.2); // Decrease zoom by 20%, min 0.2
        // Set viewport with new zoom but same position
        reactFlowInstance.setViewport({ x, y, zoom: newZoom }, { duration: 0 });
      });
    }
  }, [reactFlowInstance]);

  // Initialize ReactFlow when component mounts
  React.useEffect(() => {
    // Force a re-render after a short delay to ensure ReactFlow is properly initialized
    const timer = setTimeout(() => {
      if (nodes.length > 0) {
        setNodes(nodes => [...nodes]);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="h-full w-full relative" ref={reactFlowWrapper} style={{ minHeight: '500px', minWidth: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={handleConnect}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onInit={setReactFlowInstance}
        nodeTypes={nodeTypes}
        connectionLineType={ConnectionLineType.SmoothStep}
        connectionLineStyle={{
          strokeWidth: 1.5,
          stroke: 'rgba(16, 185, 129, 0.5)',
          strokeLinecap: 'round',
          strokeDasharray: '6,4',
          filter: 'drop-shadow(0 1px 2px rgba(16, 185, 129, 0.15))'
        }}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        snapToGrid={true}
        snapGrid={[10, 10]}
        fitViewOptions={{ padding: 0.5 }}
        minZoom={0.2}
        maxZoom={1.5}
        deleteKeyCode={['Backspace', 'Delete']}
        multiSelectionKeyCode={['Control', 'Meta']}
        selectionKeyCode={['Shift']}
      >
        <Background />
        <Panel position="top-right" className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleZoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Zoom In</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleZoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Zoom Out</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleFitView}>
                  <Maximize className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Fit View</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    console.log("Organize button clicked");
                    organizeBlocks();
                  }}
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Organize Blocks</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </Panel>
      </ReactFlow>
    </div>
  );
};

export default BuildCanvas;
