.error-node {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.7);
}

.error-node .react-flow__handle {
  background-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.error-icon {
  color: #ef4444;
  margin-left: 0.25rem;
}

.disconnected-block {
  opacity: 0.7;
}

.error-tooltip {
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 0.375rem;
  padding: 0.5rem;
  max-width: 20rem;
  z-index: 50;
}

.error-tooltip-title {
  font-weight: 600;
  color: #b91c1c;
  margin-bottom: 0.25rem;
}

.error-tooltip-list {
  list-style-type: disc;
  padding-left: 1rem;
  color: #ef4444;
}

/* Improve dragging experience */
/* Reset all forced styles that might be causing issues */
.react-flow__edge,
.react-flow__handle,
.react-flow__edge-path {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Disable transitions during dragging for better performance */
.react-flow__node.dragging,
.react-flow__node.dragging *,
.react-flow__node.dragging .react-flow__handle,
.react-flow__node.dragging .card,
.react-flow__node.dragging .card *,
.react-flow__node.dragging [class*="card"],
.react-flow__node.dragging [class*="card"] *,
.react-flow__node.dragging [class*="button"],
.react-flow__node.dragging [class*="input"],
.react-flow__node.dragging [class*="select"] {
  transition: none !important;
  animation: none !important;
  transform-origin: center !important;
  box-shadow: none !important;
}

/* Improve dragging performance */
.react-flow__node {
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Ensure smooth dragging by disabling pointer events on children during drag */
.react-flow__node.dragging * {
  pointer-events: none !important;
}

/* Force hardware acceleration for better performance */
.react-flow__node,
.react-flow__node .card {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Optimize React Flow canvas for smooth dragging and zooming */
.react-flow {
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

.react-flow__viewport {
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Ensure background and nodes move together during zoom */
.react-flow__background,
.react-flow__nodes {
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Disable transitions on viewport changes to prevent lag */
.react-flow__viewport,
.react-flow__background,
.react-flow__nodes {
  transition: none !important;
}

/* Disable any default transitions on the canvas */
.react-flow__pane {
  transition: none !important;
}

/* Global dragging performance optimization */
.react-flow__node.dragging {
  z-index: 1000 !important;
  cursor: grabbing !important;
}

/* Disable all hover effects during dragging */
.react-flow__node.dragging *:hover {
  background-color: inherit !important;
  color: inherit !important;
  border-color: inherit !important;
  box-shadow: inherit !important;
}

/* Fix node appearance */
.react-flow__node {
  background: transparent;
  border: none;
}

/* Improve node selection appearance */
.react-flow__node.selected {
  z-index: 10;
}

/* Improve edge appearance and interaction */
.react-flow__edge {
  cursor: pointer;
}

.react-flow__edge-path {
  stroke-width: 3px !important;
  stroke: #555 !important;
  cursor: pointer;
  transition: stroke-width 0.2s ease, stroke 0.2s ease;
}

/* Create larger hit area for edges using invisible stroke */
.react-flow__edge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  stroke-width: 12px !important;
  stroke: transparent !important;
  cursor: pointer;
  pointer-events: auto;
}

/* Edge hover and selection states */
.react-flow__edge:hover .react-flow__edge-path {
  stroke-width: 4px !important;
  stroke: #6b7280 !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke-width: 5px !important;
  stroke: #3b82f6 !important;
}

/* Improve handle appearance and interaction */
.react-flow__handle {
  width: 12px !important;
  height: 12px !important;
  border: 2px solid #fff !important;
  border-radius: 50% !important;
  transition: background-color 0.2s ease, box-shadow 0.2s ease !important;
  cursor: pointer !important;
  z-index: 10 !important;
  position: absolute !important;
}

/* Create larger interaction area for handles */
.react-flow__handle::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: auto;
}

/* Handle hover state - simple color change only */
.react-flow__handle:hover {
  background-color: #4a5568 !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3) !important;
}

/* Handle connection state */
.react-flow__handle.connecting {
  background-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
}

/* Node selection appearance */
.react-flow__node-default.selected,
.react-flow__node-input.selected,
.react-flow__node-output.selected {
  box-shadow: 0 0 0 2px #1a192b;
}

/* Hide ReactFlow controls */
.react-flow__controls {
  display: none !important;
}

/* Additional edge interaction improvements */
.react-flow__edge {
  pointer-events: auto !important;
}

/* Improve edge path interaction */
.react-flow__edge-path {
  pointer-events: auto !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
}

/* Add visual feedback for edge interaction */
.react-flow__edge:hover {
  filter: drop-shadow(0 0 4px rgba(107, 114, 128, 0.5));
}

.react-flow__edge.selected {
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.7));
}

/* Improve connection line appearance during dragging */
.react-flow__connection-line {
  stroke-width: 3px !important;
  stroke: #3b82f6 !important;
  stroke-dasharray: 5, 5 !important;
  animation: dash 1s linear infinite !important;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* Enhance handle interaction feedback */
.react-flow__handle.react-flow__handle-connecting {
  background-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4) !important;
}

/* Improve handle visibility on different backgrounds */
.react-flow__handle {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Additional specificity to override ReactFlow defaults */
.react-flow__node .react-flow__handle {
  position: absolute !important;
}

.react-flow__node .react-flow__handle:hover {
  background-color: #4a5568 !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3) !important;
}

.react-flow__node .react-flow__handle.connecting {
  background-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
}

.react-flow__node .react-flow__handle.react-flow__handle-connecting {
  background-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4) !important;
}

/* Simplified handle styling - no scaling */
.react-flow__handle[data-handleid]:hover,
.react-flow__handle[style*="top:"]:hover,
.react-flow__handle[data-handleid="true"]:hover,
.react-flow__handle[data-handleid="false"]:hover,
.react-flow__handle[data-handleid="input"]:hover,
.react-flow__handle[data-handleid="output"]:hover {
  background-color: #4a5568 !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3) !important;
}

/* Simple connecting state - no animation */
.react-flow__handle.connecting {
  background-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
}
