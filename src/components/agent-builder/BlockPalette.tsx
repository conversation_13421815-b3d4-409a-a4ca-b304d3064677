import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Bar<PERSON>hart2, TrendingUp, TrendingDown, DollarSign, Calculator, GitBranch, Target, Zap, Plus, Minus, BarChart3, Activity, LineChart, Crosshair, Terminal, Shield, Percent, Clock, Globe, Layers, Building, RotateCcw, PieChart, ArrowUpDown } from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockPaletteProps {
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
}

interface BlockItemProps {
  type: BlockType;
  title: string;
  icon: React.ReactNode;
  description: string;
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
  properties?: Record<string, any>;
}

const BlockItem: React.FC<BlockItemProps> = ({ type, title, icon, description, onBlockDrop, properties }) => {
  const handleDragStart = (e: React.DragEvent) => {
    // Store both the type and any additional properties
    const dragData = {
      type,
      properties: properties || {}
    };

    // Ensure position is not included in the properties to avoid issues
    if (dragData.properties.position) {
      delete dragData.properties.position;
    }

    e.dataTransfer.setData('application/reactflow', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Card
      className="mb-2 cursor-grab hover:bg-accent transition-colors"
      draggable
      onDragStart={handleDragStart}
    >
      <CardContent className="p-3 flex items-center gap-3">
        <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary/10 text-primary">
          {icon}
        </div>
        <div>
          <h3 className="text-sm font-medium">{title}</h3>
          <p className="text-xs text-muted-foreground">{description}</p>
        </div>
      </CardContent>
    </Card>
  );
};

const BlockPalette: React.FC<BlockPaletteProps> = ({ onBlockDrop }) => {
  return (
    <Accordion type="multiple" defaultValue={['data', 'technical', 'logic', 'signals', 'output', 'debugging']}>
      <AccordionItem value="data">
        <AccordionTrigger className="py-2">Data Sources</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.PRICE}
            title="Price Data"
            icon={<DollarSign className="h-4 w-4" />}
            description="Open, High, Low, Close, Volume"
            onBlockDrop={onBlockDrop}
            properties={{ dataPoint: 'close', lookback: 0 }}
          />
          <BlockItem
            type={BlockType.FUNDAMENTAL}
            title="Fundamental Data"
            icon={<TrendingUp className="h-4 w-4" />}
            description="P/E, P/B, ROE, Revenue Growth, etc."
            onBlockDrop={onBlockDrop}
            properties={{ metric: 'return_on_equity', statement: 'calculated', period: 'quarterly' }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="technical">
        <AccordionTrigger className="py-2">Technical Analysis</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.INDICATOR}
            title="Technical Indicator"
            icon={<BarChart2 className="h-4 w-4" />}
            description="RSI, MACD, SMA, EMA, Bollinger Bands, Support/Resistance, etc."
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', parameters: { period: 14 } }}
          />
          <BlockItem
            type={BlockType.MOVING_AVERAGE}
            title="Moving Average"
            icon={<LineChart className="h-4 w-4" />}
            description="SMA, EMA, WMA with customizable periods"
            onBlockDrop={onBlockDrop}
            properties={{ averageType: 'sma', period: 20, timeframe: 'day', source: 'close' }}
          />
          <BlockItem
            type={BlockType.MOMENTUM_INDICATOR}
            title="Momentum Indicator"
            icon={<Activity className="h-4 w-4" />}
            description="RSI, Stochastic, Williams %R, CCI"
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', period: 14, timeframe: 'day', overbought: 70, oversold: 30 }}
          />
          <BlockItem
            type={BlockType.CANDLE_PATTERN}
            title="Candle Pattern"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Doji, Hammer, Shooting Star, Marubozu, Engulfing, etc."
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.CHART_PATTERN}
            title="Chart Pattern"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Triangles, flags, head & shoulders, double tops/bottoms"
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.BREAKOUT_DETECTION}
            title="Breakout Detection"
            icon={<ArrowUpDown className="h-4 w-4" />}
            description="Support/resistance breakouts, trend line breaks"
            onBlockDrop={onBlockDrop}
            properties={{ breakoutType: 'support_resistance', timeframe: 'day', volumeConfirmation: false }}
          />
          <BlockItem
            type={BlockType.GAP_ANALYSIS}
            title="Gap Analysis"
            icon={<Crosshair className="h-4 w-4" />}
            description="Gap up/down detection with size thresholds"
            onBlockDrop={onBlockDrop}
            properties={{ gapType: 'any', minGapSize: 1, timeframe: 'day' }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="logic">
        <AccordionTrigger className="py-2">Logic & Processing</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.CONDITION}
            title="Condition"
            icon={<GitBranch className="h-4 w-4" />}
            description=">, <, ==, >=, <=, !=, AND, OR, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operator: '>', compareValue: 0 }}
          />
          <BlockItem
            type={BlockType.CANDLE_PATTERN}
            title="Candle Pattern"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Doji, Hammer, Shooting Star, Marubozu, Engulfing, Morning/Evening Star, etc."
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.OPERATOR}
            title="Math Operator"
            icon={<Calculator className="h-4 w-4" />}
            description="Add, Subtract, Multiply, Divide, Average, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operation: 'add' }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="signals">
        <AccordionTrigger className="py-2">Signal Confidence</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.BULLISH_CONFIDENCE_BOOST}
            title="Bullish Confidence Boost"
            icon={<Plus className="h-4 w-4 text-green-500" />}
            description="Increases bullish confidence by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 10 }}
          />
          <BlockItem
            type={BlockType.BEARISH_CONFIDENCE_BOOST}
            title="Bearish Confidence Boost"
            icon={<Minus className="h-4 w-4 text-red-500" />}
            description="Increases bearish confidence by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 10 }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="output">
        <AccordionTrigger className="py-2">Output Signals</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.TRIGGER}
            title="Signal"
            icon={<Target className="h-4 w-4" />}
            description="Bullish, Bearish, or Neutral signal"
            onBlockDrop={onBlockDrop}
            properties={{ signal: 'bullish', confidence: 75 }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="debugging">
        <AccordionTrigger className="py-2">Debugging</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.CONSOLE_LOG}
            title="Console Log"
            icon={<Terminal className="h-4 w-4" />}
            description="Debug logging for agent execution"
            onBlockDrop={onBlockDrop}
            properties={{ message: 'Debug message' }}
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default BlockPalette;
