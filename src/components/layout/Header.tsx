import { UserCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import AuthButton from "@/components/auth/AuthButton";
import { useSidebar } from "@/components/ui/sidebar";
import ModelSelector from "@/components/chat/ModelSelector";
import { Plus } from "lucide-react";
import { useNavigate, useLocation, Link } from "react-router-dom";

interface HeaderProps {
  onNewChat: () => void;
  loading?: boolean;
}

const Header = ({ onNewChat, loading }: HeaderProps) => {
  const location = useLocation();
  const isSubscriptionPage = location.pathname.includes('/subscription');
  const isAgentBuilderPage = location.pathname.includes('/agent-builder');

  // Ensure the event is properly handled
  const handleNewChat = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onNewChat();
  };

  const headerContent = (
    <>
      <div className="flex-1 flex items-center justify-start">
        {!loading && !isAgentBuilderPage && (
          <Link
            to="/chat"
            className="flex items-center justify-center bg-[#1A1A1A]/80 text-white/90 hover:text-white border border-white/10 h-8 px-3 rounded-md text-xs font-medium transition-all hover:bg-[#252525]/80"
          >
            <Plus className="w-3 h-3 mr-1" />
            New Chat
          </Link>
        )}
      </div>

      <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center justify-center">
        <Link to="/chat" className="cursor-pointer no-underline">
          <ModelSelector />
        </Link>
      </div>

      <div className="flex-1 flex justify-end">
        <AuthButton />
      </div>
    </>
  );

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 flex items-center justify-between h-14 px-4 ${
      isSubscriptionPage ? 'bg-[#0A0A0A]' : 'bg-[#0A0A0A]/80 backdrop-blur-xl'
    }`}>
      {headerContent}
    </header>
  );
};

export default Header;
