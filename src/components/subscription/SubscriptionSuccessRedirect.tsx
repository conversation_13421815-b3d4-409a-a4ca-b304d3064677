import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Loader2, CheckCircle } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { supabase } from '@/integrations/supabase/client';
import { MESSAGE_LIMITS, PLAN_TYPES, PlanType } from '@/hooks/useUserLimits';

export const SubscriptionSuccessRedirect = () => {
  const navigate = useNavigate();
  const { refetch, subscription } = useSubscription();
  const [isProcessing, setIsProcessing] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [currentPlan, setCurrentPlan] = useState<string>('basic');

  useEffect(() => {
    const processPaymentSuccess = async () => {
      try {
        setIsProcessing(true);

        // First, check if we're in a redirect from Stripe
        const isFromStripe = window.location.pathname.includes('/subscription/success');
        
        if (!isFromStripe) {
          navigate('/subscription');
          return;
        }
        
        // Multiple attempts to refresh subscription data
        let subscriptionData = subscription;
        let attempts = 0;
        const maxAttempts = 5; // Increase max attempts
        
        while (!subscriptionData && attempts < maxAttempts) {
          await refetch();
          
          // Give a brief delay between attempts
          await new Promise(resolve => setTimeout(resolve, 2000)); // Increase delay
          
          subscriptionData = subscription;
          attempts++;
        }

        // If we still don't have subscription data, try a direct API call
        if (!subscriptionData) {
          const { data, error } = await supabase.functions.invoke('stripe', {
            body: { action: 'get-subscription' }
          });
          
          if (!error && data.subscription) {
            // Map price ID to plan name
            let plan: PlanType = PLAN_TYPES.basic; // Default to basic
            const priceId = data.subscription.stripe_price_id;
            
            if (priceId) {
              if (priceId.includes('basic')) {
                plan = PLAN_TYPES.basic;
              } else if (priceId.includes('pro')) {
                plan = PLAN_TYPES.pro;
              } else if (priceId.includes('premium')) {
                plan = PLAN_TYPES.premium;
              }
            }
            
            subscriptionData = {
              ...data.subscription,
              plan
            };
          }
        }

        // Determine the subscription plan
        const detectedPlan = subscriptionData?.plan || PLAN_TYPES.basic; // Default to basic if we can't determine
        setCurrentPlan(detectedPlan);
        
        // Update user profile with subscription information
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          // Check if user profile exists
          const { data: profileExists, error: profileCheckError } = await supabase
            .from('profiles')
            .select('id')
            .eq('id', session.user.id)
            .maybeSingle();
          
          // If profile doesn't exist, create it
          if (!profileExists) {
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: session.user.id,
                subscription_type: null,
                has_seen_onboarding: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
            
            if (createError) {
              console.error('Error creating profile:', createError);
            }
          }
          
          // Get the message limit for the detected plan
          const messageLimit = MESSAGE_LIMITS[detectedPlan as keyof typeof MESSAGE_LIMITS] || MESSAGE_LIMITS[PLAN_TYPES.basic];
          
          // Update the user's profile with subscription information
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              subscription_type: detectedPlan,
              has_seen_onboarding: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', session.user.id);
          
          if (updateError) {
            console.error('Error updating profile:', updateError);
          }
          
          // Update user tokens
          const { data: existingTokens, error: tokensError } = await supabase
            .from('user_tokens')
            .select('*')
            .eq('user_id', session.user.id)
            .single();
          
          if (tokensError && tokensError.code !== 'PGRST116') {
            throw new Error(`Failed to check existing tokens: ${tokensError.message}`);
          }
          
          const tokenData = {
            tokens_remaining: messageLimit,
            last_reset: new Date().toISOString(),
            last_reset_date: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          
          if (!existingTokens) {
            // Create new tokens record
            const { error: insertError } = await supabase
              .from('user_tokens')
              .insert({
                user_id: session.user.id,
                ...tokenData
              });
              
            if (insertError) {
              throw new Error(`Failed to create tokens record: ${insertError.message}`);
            }
          } else {
            // Update existing tokens
            const { error: updateError } = await supabase
              .from('user_tokens')
              .update(tokenData)
              .eq('user_id', session.user.id);
              
            if (updateError) {
              throw new Error(`Failed to update tokens: ${updateError.message}`);
            }
          }
          
          // Double-check that the tokens were updated correctly
          const { data: verifyTokens, error: verifyError } = await supabase
            .from('user_tokens')
            .select('tokens_remaining')
            .eq('user_id', session.user.id)
            .single();
            
          if (!verifyError && verifyTokens) {
            if (verifyTokens.tokens_remaining !== messageLimit) {
              // Try one more time to update the tokens
              await supabase
                .from('user_tokens')
                .update({
                  tokens_remaining: messageLimit
                })
                .eq('user_id', session.user.id);
            }
          }
        }

        // Success!
        setIsSuccess(true);
        
        // Remove any query params
        window.history.replaceState({}, document.title, window.location.pathname);
        
        // Refresh the onboarding status to hide the onboarding component
        // @ts-ignore - Accessing the function we added to window
        if (window.refreshOnboardingStatus) {
          try {
            // @ts-ignore
            window.refreshOnboardingStatus();
          } catch (error) {
            console.error('Error calling refreshOnboardingStatus:', error);
          }
        }
        
        // Navigate to dashboard after 3 seconds
        setTimeout(() => {
          navigate('/');
        }, 3000);
      } catch (error) {
        console.error('Error in processPaymentSuccess:', error);
        setErrorMessage('Failed to process your subscription. Please contact support.');
      } finally {
        setIsProcessing(false);
      }
    };

    processPaymentSuccess();
  }, [navigate, refetch, subscription]);

  // Get the message limit for the current plan
  const getMessageLimit = (plan: string) => {
    return MESSAGE_LIMITS[plan as keyof typeof MESSAGE_LIMITS] || MESSAGE_LIMITS[PLAN_TYPES.basic];
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-[#0A0A0A]">
      <Card className="max-w-md w-full p-8 bg-[#141414] border-white/10">
        {isProcessing ? (
          <div className="flex flex-col items-center text-center">
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
            <h2 className="text-xl font-semibold mb-2">Processing your subscription...</h2>
            <p className="text-white/60">
              Please wait while we activate your subscription.
            </p>
          </div>
        ) : isSuccess ? (
          <div className="flex flex-col items-center text-center">
            <div className="bg-green-500/10 p-3 rounded-full mb-4">
              <CheckCircle className="h-10 w-10 text-green-500" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Subscription Activated!</h2>
            <p className="text-white/60 mb-4">
              Your {subscription?.plan || currentPlan} subscription has been successfully activated.
            </p>
            <div className="bg-blue-500/10 rounded-lg p-4 mb-6 w-full">
              <h3 className="font-medium text-blue-400 mb-2">Your New Benefits:</h3>
              <ul className="text-left text-white/80 text-sm space-y-2">
                {(subscription?.plan === 'basic' || currentPlan === 'basic') && (
                  <>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> {getMessageLimit('basic')} messages per month</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Warren Buffett Analysis</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Basic Technical Support</li>
                  </>
                )}
                {(subscription?.plan === 'pro' || currentPlan === 'pro') && (
                  <>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> {getMessageLimit('pro')} messages per month</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Warren Buffett Analysis</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Priority Technical Support</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Advanced Trading Strategies</li>
                  </>
                )}
                {(subscription?.plan === 'premium' || currentPlan === 'premium') && (
                  <>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> {getMessageLimit('premium')} messages per month</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Warren Buffett Analysis</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> 24/7 Premium Support</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Custom Trading Strategies</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" /> API Access</li>
                  </>
                )}
              </ul>
            </div>
            <p className="text-white/60 mb-2">
              You will be redirected to the dashboard in a moment.
            </p>
            <div className="w-full bg-white/10 h-1 rounded-full overflow-hidden">
              <div className="bg-green-500 h-full animate-progress" style={{ width: '0%' }} />
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center text-center">
            <div className="bg-red-500/10 p-3 rounded-full mb-4">
              <CheckCircle className="h-10 w-10 text-red-500" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Something went wrong</h2>
            <p className="text-white/60 mb-6">
              {errorMessage || 'There was an error processing your subscription. Please try again.'}
            </p>
            <button
              onClick={() => navigate('/subscription')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Return to Subscription Page
            </button>
          </div>
        )}
      </Card>
    </div>
  );
};