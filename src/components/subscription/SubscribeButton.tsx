import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/hooks/useSubscription';
import { SUBSCRIPTION_TYPES } from '@/hooks/useSubscription';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import { supabase } from '@/integrations/supabase/client';

interface SubscribeButtonProps {
  priceId: string;
  planType: keyof typeof SUBSCRIPTION_TYPES;
  isCurrentPlan?: boolean;
  className?: string;
}

export function SubscribeButton({
  priceId,
  planType,
  isCurrentPlan = false,
  className = ''
}: SubscribeButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { createSubscription, subscription } = useSubscription();
  const { toast } = useToast();

  const handleSubscribe = async () => {
    try {
      setIsLoading(true);

      // Get the session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error("No authentication token found");
      }

      // Check for no-trial parameters in the URL
      const urlParams = new URLSearchParams(window.location.search);
      const basicNoTrial = urlParams.get('basicnotrial') !== null;
      const proNoTrial = urlParams.get('pronotrial') !== null;
      const skipTrial = basicNoTrial || proNoTrial;

      // Use the Supabase edge function to create a checkout session
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'create-checkout-session',
          priceId,
          returnUrl: window.location.origin + '/subscription/manage?success=true',
          skipTrial
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // If we have a URL, redirect directly
      if (data.url) {
        window.location.href = data.url;
        return;
      }

      // Otherwise use the sessionId with Stripe.js
      if (data.sessionId) {
        const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
        if (!stripe) {
          throw new Error("Stripe failed to load");
        }

        const { error: redirectError } = await stripe.redirectToCheckout({ sessionId: data.sessionId });
        if (redirectError) {
          throw new Error(`Stripe redirect failed: ${redirectError.message}`);
        }
      } else {
        throw new Error("No session ID or URL returned from server");
      }
    } catch (error) {
      console.error('Error subscribing:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to process subscription. Please try again.',
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (isCurrentPlan) {
      return 'Current Plan';
    }

    if (!subscription?.subscription_type) {
      return 'Subscribe';
    }

    switch (planType) {
      case SUBSCRIPTION_TYPES.BASIC:
        return 'Downgrade';
      case SUBSCRIPTION_TYPES.PRO:
        return subscription?.subscription_type === SUBSCRIPTION_TYPES.PREMIUM ? 'Downgrade' : 'Upgrade';
      case SUBSCRIPTION_TYPES.PREMIUM:
        return 'Upgrade';
      default:
        return 'Subscribe';
    }
  };

  return (
    <Button
      onClick={handleSubscribe}
      disabled={isLoading || isCurrentPlan}
      className={className}
      variant={isCurrentPlan ? 'outline' : 'default'}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-3.5 w-3.5 animate-spin" />
          Processing...
        </>
      ) : (
        getButtonText()
      )}
    </Button>
  );
}
