import React from 'react';
import { motion } from "framer-motion";
import { Mail, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from '@/contexts/AuthContext';

const NotAcceptingNewUsers = () => {
  const { signOut } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
      // The page will refresh automatically due to auth state change
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm p-4">
      <motion.div
        className="relative w-full max-w-md bg-[#121212]/60 rounded-2xl shadow-2xl border border-white/[0.08] overflow-hidden backdrop-blur-sm"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{
          opacity: 1,
          scale: 1,
          boxShadow: "0 0 0 1px rgba(255, 255, 255, 0.08), 0 10px 40px rgba(0, 0, 0, 0.6), 0 0 25px rgba(0, 0, 0, 0.5)"
        }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.4 }}
      >
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="mx-auto w-12 h-12 mb-4 opacity-90">
              <img
                src="http://thecodingkid.oyosite.com/logo_only.png"
                alt="Osis Logo"
                className="w-full h-full object-contain"
                style={{ filter: "drop-shadow(0 0 10px rgba(255,255,255,0.12))" }}
              />
            </div>
            <h2 className="text-white text-[24px] font-medium mb-2 tracking-tight">
              Sorry, we aren't accepting new users at this time
            </h2>
            <p className="text-white/60 text-[14px] leading-relaxed mb-6">
              We've added your email to our waitlist and will notify you when we're ready to welcome new users.
            </p>

            <div className="flex items-center justify-center mt-4">
              <div className="bg-white/5 rounded-full p-3">
                <Mail className="h-6 w-6 text-white/60" />
              </div>
            </div>

            <Button
              onClick={handleSignOut}
              className="mt-6 bg-white/5 hover:bg-white/10 text-white/70 flex items-center gap-2 mx-auto"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign out and try another account</span>
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default NotAcceptingNewUsers;
