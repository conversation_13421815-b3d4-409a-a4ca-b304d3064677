import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Star, Trophy, Target, TrendingUp, Zap, Award, Crown, Sparkles } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  xpReward: number;
}

// Removed notifications - we only want the main success animation

interface UserProgress {
  level: number;
  xp: number;
  xpToNext: number;
  streak: number;
  totalActions: number;
  portfoliosCreated: number;
  agentsBuilt: number;
  scansCompleted: number;
  tradesExecuted: number;
  blocksAdded: number;
  // Track first-time actions
  hasCompletedFirstScan: boolean;
  hasCompletedFirstBacktest: boolean;
  hasCreatedFirstPortfolio: boolean;
}

interface GamificationContextType {
  userProgress: UserProgress;
  achievements: Achievement[];
  showSuccessAnimation: boolean;
  successAnimationType: string;
  successAnimationData: any;

  // Actions
  addXP: (amount: number, reason?: string) => void;
  incrementStreak: () => void;
  resetStreak: () => void;
  trackAction: (action: string, data?: any) => void;
  triggerSuccessAnimation: (type: string, data?: any) => void;
  hideSuccessAnimation: () => void;
}

const GamificationContext = createContext<GamificationContextType | undefined>(undefined);

export const useGamification = () => {
  const context = useContext(GamificationContext);
  if (!context) {
    throw new Error('useGamification must be used within a GamificationProvider');
  }
  return context;
};

export const GamificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [userProgress, setUserProgress] = useState<UserProgress>({
    level: 1,
    xp: 0,
    xpToNext: 1000,
    streak: 0,
    totalActions: 0,
    portfoliosCreated: 0,
    agentsBuilt: 0,
    scansCompleted: 0,
    tradesExecuted: 0,
    blocksAdded: 0,
    hasCompletedFirstScan: false,
    hasCompletedFirstBacktest: false,
    hasCreatedFirstPortfolio: false
  });

  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [successAnimationType, setSuccessAnimationType] = useState('');
  const [successAnimationData, setSuccessAnimationData] = useState<any>(null);

  // Define achievements
  const [achievements, setAchievements] = useState<Achievement[]>([
    {
      id: 'first_portfolio',
      title: 'First Steps',
      description: 'Create your first AI-optimized portfolio',
      icon: <TrendingUp className="w-4 h-4" />,
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      rarity: 'common',
      xpReward: 100
    },
    {
      id: 'portfolio_master',
      title: 'Portfolio Master',
      description: 'Create 10 different portfolios',
      icon: <Crown className="w-4 h-4" />,
      progress: 0,
      maxProgress: 10,
      unlocked: false,
      rarity: 'epic',
      xpReward: 500
    },
    {
      id: 'first_agent',
      title: 'Agent Creator',
      description: 'Build your first trading agent',
      icon: <Zap className="w-4 h-4" />,
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      rarity: 'common',
      xpReward: 150
    },
    {
      id: 'agent_architect',
      title: 'Agent Architect',
      description: 'Build 5 different trading agents',
      icon: <Award className="w-4 h-4" />,
      progress: 0,
      maxProgress: 5,
      unlocked: false,
      rarity: 'rare',
      xpReward: 300
    },
    {
      id: 'scan_novice',
      title: 'Market Scanner',
      description: 'Complete your first market scan',
      icon: <Target className="w-4 h-4" />,
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      rarity: 'common',
      xpReward: 75
    },
    {
      id: 'scan_expert',
      title: 'Scan Expert',
      description: 'Complete 50 market scans',
      icon: <Star className="w-4 h-4" />,
      progress: 0,
      maxProgress: 50,
      unlocked: false,
      rarity: 'legendary',
      xpReward: 1000
    },
    {
      id: 'streak_warrior',
      title: 'Streak Warrior',
      description: 'Maintain a 7-day trading streak',
      icon: <Sparkles className="w-4 h-4" />,
      progress: 0,
      maxProgress: 7,
      unlocked: false,
      rarity: 'rare',
      xpReward: 400
    },
    {
      id: 'block_builder',
      title: 'Block Builder',
      description: 'Add 25 blocks to your agents',
      icon: <Trophy className="w-4 h-4" />,
      progress: 0,
      maxProgress: 25,
      unlocked: false,
      rarity: 'epic',
      xpReward: 600
    }
  ]);

  // Calculate level from XP
  const calculateLevel = (xp: number) => {
    return Math.floor(xp / 1000) + 1;
  };

  // Calculate XP needed for next level
  const calculateXPToNext = (level: number) => {
    return level * 1000;
  };

  // Add XP and check for level ups
  const addXP = useCallback((amount: number, reason?: string) => {
    setUserProgress(prev => {
      const newXP = prev.xp + amount;
      const newLevel = calculateLevel(newXP);
      const leveledUp = newLevel > prev.level;

      if (leveledUp) {
        // Trigger clean level up success animation
        triggerSuccessAnimation('level', {
          title: 'Level Up!',
          subtitle: `You've reached level ${newLevel}!`,
          value: newLevel
        });
      }

      return {
        ...prev,
        xp: newXP,
        level: newLevel,
        xpToNext: calculateXPToNext(newLevel)
      };
    });
  }, []);

  // Track various actions
  const trackAction = useCallback((action: string, data?: any) => {
    setUserProgress(prev => {
      const newProgress = { ...prev, totalActions: prev.totalActions + 1 };

      switch (action) {
        case 'portfolio_created':
          newProgress.portfoliosCreated += 1;
          addXP(100, 'Portfolio created');
          const isFirstPortfolio = !newProgress.hasCreatedFirstPortfolio;
          if (isFirstPortfolio) {
            newProgress.hasCreatedFirstPortfolio = true;
          }
          triggerSuccessAnimation('portfolio', {
            title: isFirstPortfolio ? 'Your First Portfolio!' : 'Portfolio Created!',
            subtitle: 'Your AI-optimized portfolio is ready',
            isFirstTime: isFirstPortfolio
          });
          break;
        case 'agent_built':
          newProgress.agentsBuilt += 1;
          addXP(150, 'Agent built');
          triggerSuccessAnimation('agent', { title: 'Agent Built!', subtitle: 'Your trading agent is ready to analyze' });
          break;
        case 'scan_completed':
          newProgress.scansCompleted += 1;
          addXP(75, 'Market scan completed');
          const isFirstScan = !newProgress.hasCompletedFirstScan;
          if (isFirstScan) {
            newProgress.hasCompletedFirstScan = true;
          }
          triggerSuccessAnimation('scan', {
            title: isFirstScan ? 'Your First Scan!' : 'Scan Complete!',
            subtitle: 'Market analysis finished',
            isFirstTime: isFirstScan
          });
          break;
        case 'trade_executed':
          newProgress.tradesExecuted += 1;
          addXP(50, 'Trade executed');
          break;
        case 'backtest_completed':
          newProgress.tradesExecuted += 1;
          addXP(100, 'Backtest completed');
          const isFirstBacktest = !newProgress.hasCompletedFirstBacktest;
          if (isFirstBacktest) {
            newProgress.hasCompletedFirstBacktest = true;
          }
          triggerSuccessAnimation('backtest', {
            title: isFirstBacktest ? 'Your First Backtest!' : 'Backtest Complete!',
            subtitle: 'Strategy analysis finished',
            isFirstTime: isFirstBacktest
          });
          break;
        case 'block_added':
          newProgress.blocksAdded += 1;
          addXP(25, 'Block added to agent');
          triggerSuccessAnimation('block', { title: 'Block Added!', subtitle: 'Your agent is getting smarter' });
          break;
      }

      return newProgress;
    });

    // Check achievements
    checkAchievements(action);
  }, [addXP]);

  // Check and unlock achievements
  const checkAchievements = useCallback((action: string) => {
    setAchievements(prev => {
      return prev.map(achievement => {
        let newProgress = achievement.progress;

        switch (achievement.id) {
          case 'first_portfolio':
            if (action === 'portfolio_created') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'portfolio_master':
            if (action === 'portfolio_created') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'first_agent':
            if (action === 'agent_built') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'agent_architect':
            if (action === 'agent_built') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'scan_novice':
            if (action === 'scan_completed') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'scan_expert':
            if (action === 'scan_completed') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'block_builder':
            if (action === 'block_added') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
        }

        const wasUnlocked = achievement.unlocked;
        const isNowUnlocked = newProgress >= achievement.maxProgress;

        if (!wasUnlocked && isNowUnlocked) {
          // Achievement unlocked - trigger clean success animation
          triggerSuccessAnimation('achievement', {
            title: 'Achievement Unlocked!',
            subtitle: achievement.title,
            value: `+${achievement.xpReward} XP`
          });
          addXP(achievement.xpReward, `Achievement: ${achievement.title}`);
        }

        return {
          ...achievement,
          progress: newProgress,
          unlocked: isNowUnlocked
        };
      });
    });
  }, [addXP]);

  // Streak management
  const incrementStreak = useCallback(() => {
    setUserProgress(prev => {
      const newStreak = prev.streak + 1;

      // Check streak achievements
      setAchievements(prevAchievements => {
        return prevAchievements.map(achievement => {
          if (achievement.id === 'streak_warrior') {
            const newProgress = Math.min(achievement.maxProgress, newStreak);
            const wasUnlocked = achievement.unlocked;
            const isNowUnlocked = newProgress >= achievement.maxProgress;

            if (!wasUnlocked && isNowUnlocked) {
              triggerSuccessAnimation('achievement', {
                title: 'Achievement Unlocked!',
                subtitle: 'Streak Warrior',
                value: `+${achievement.xpReward} XP`
              });
              addXP(achievement.xpReward, 'Achievement: Streak Warrior');
            }

            return {
              ...achievement,
              progress: newProgress,
              unlocked: isNowUnlocked
            };
          }
          return achievement;
        });
      });

      return { ...prev, streak: newStreak };
    });
  }, [addXP]);

  const resetStreak = useCallback(() => {
    setUserProgress(prev => ({ ...prev, streak: 0 }));
  }, []);

  // Success animation management
  const triggerSuccessAnimation = useCallback((type: string, data?: any) => {
    setSuccessAnimationType(type);
    setSuccessAnimationData(data);
    setShowSuccessAnimation(true);
  }, []);

  const hideSuccessAnimation = useCallback(() => {
    setShowSuccessAnimation(false);
    setSuccessAnimationType('');
    setSuccessAnimationData(null);
  }, []);

  const value: GamificationContextType = {
    userProgress,
    achievements,
    showSuccessAnimation,
    successAnimationType,
    successAnimationData,
    addXP,
    incrementStreak,
    resetStreak,
    trackAction,
    triggerSuccessAnimation,
    hideSuccessAnimation
  };

  return (
    <GamificationContext.Provider value={value}>
      {children}
    </GamificationContext.Provider>
  );
};
