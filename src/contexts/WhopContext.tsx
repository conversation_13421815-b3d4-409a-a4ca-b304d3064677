import React, { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { createHash } from 'crypto';

type WhopContextType = {
  isWhopUser: boolean;
  whopUser: any | null;
  whopAccessToken: string | null;
  whopSubscription: any | null;
  isLoadingWhop: boolean;
};

const WhopContext = createContext<WhopContextType>({
  isWhopUser: false,
  whopUser: null,
  whopAccessToken: null,
  whopSubscription: null,
  isLoadingWhop: true,
});

export const WhopProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isWhopUser, setIsWhopUser] = useState(false);
  const [whopUser, setWhopUser] = useState<any | null>(null);
  const [whopAccessToken, setWhopAccessToken] = useState<string | null>(null);
  const [whopSubscription, setWhopSubscription] = useState<any | null>(null);
  const [isLoadingWhop, setIsLoadingWhop] = useState(false);
  const { toast } = useToast();

  // Helper function to create a secure password from Whop ID
  const createSecurePassword = (whopId: string) => {
    // Add a salt and hash the Whop ID to make it more secure
    const salt = import.meta.env.WHOP_CLIENT_SECRET?.slice(0, 10) || 'default_salt';
    return `whop_${whopId}_${salt}`;
  };

  // Helper function to map Whop plan to subscription type
  const mapWhopPlanToSubscriptionType = (plan: any) => {
    if (!plan) return 'free';
    
    // You can customize this mapping based on your Whop plans
    switch (plan.name?.toLowerCase()) {
      case 'premium':
        return 'premium';
      case 'pro':
        return 'pro';
      default:
        return 'free';
    }
  };

  // Helper function to map Whop plan to message limit
  const mapWhopPlanToMessageLimit = (plan: any) => {
    if (!plan) return 3; // Free tier limit
    
    // You can customize this mapping based on your Whop plans
    switch (plan.name?.toLowerCase()) {
      case 'premium':
        return 250;
      case 'pro':
        return 100;
      default:
        return 3;
    }
  };

  useEffect(() => {
    const checkWhopAuth = async () => {
      try {
        // Only set loading if we have a code to process
        const params = new URLSearchParams(window.location.search);
        const code = params.get('code');
        
        if (code) {
          setIsLoadingWhop(true);
          // Exchange code for access token
          const response = await axios.post('https://api.whop.com/v5/oauth/token', {
            code,
            client_id: import.meta.env.WHOP_CLIENT_ID,
            client_secret: import.meta.env.WHOP_CLIENT_SECRET,
            redirect_uri: import.meta.env.WHOP_REDIRECT_URI
          });

          if (response.data.access_token) {
            setWhopAccessToken(response.data.access_token);
            
            // Get user info from Whop
            const userResponse = await axios.get('https://api.whop.com/v5/me', {
              headers: {
                Authorization: `Bearer ${response.data.access_token}`
              }
            });

            if (userResponse.data) {
              setWhopUser(userResponse.data);
              setIsWhopUser(true);

              // Get user's Whop memberships/subscriptions
              const membershipsResponse = await axios.get('https://api.whop.com/v5/me/memberships', {
                headers: {
                  Authorization: `Bearer ${response.data.access_token}`
                }
              });

              // Find the active membership/subscription
              const activeMembership = membershipsResponse.data?.data?.find(
                (m: any) => m.status === 'active'
              );
              setWhopSubscription(activeMembership);

              // Create secure password from Whop ID
              const password = createSecurePassword(userResponse.data.id);

              // Try to sign in first
              const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
                email: userResponse.data.email,
                password: password
              });

              if (signInError?.message.includes('Invalid login credentials')) {
                // If sign in fails, create new account
                const { data: { user: newUser }, error: signUpError } = await supabase.auth.signUp({
                  email: userResponse.data.email,
                  password: password,
                  options: {
                    data: {
                      full_name: userResponse.data.name || '',
                      whop_id: userResponse.data.id
                    }
                  }
                });

                if (signUpError) throw signUpError;

                if (newUser) {
                  // Create profile for new user
                  const subscriptionType = mapWhopPlanToSubscriptionType(activeMembership?.plan);
                  const messageLimit = mapWhopPlanToMessageLimit(activeMembership?.plan);

                  const { error: profileError } = await supabase
                    .from('profiles')
                    .upsert({
                      id: newUser.id,
                      full_name: userResponse.data.name || '',
                      subscription_type: subscriptionType,
                      subscription_id: activeMembership?.id || null,
                      subscription_status: activeMembership?.status || 'inactive',
                      daily_message_limit: messageLimit,
                      tokens_used_today: 0,
                      last_token_reset: new Date().toISOString(),
                      updated_at: new Date().toISOString()
                    });

                  if (profileError) throw profileError;

                  toast({
                    title: "Account Created",
                    description: "Your account has been created and linked to your Whop subscription.",
                  });

                  // Clean up URL and redirect
                  window.history.replaceState({}, document.title, '/');
                  window.location.href = '/';
                }
              } else if (signInData.user) {
                // User exists and signed in successfully, update their profile
                const subscriptionType = mapWhopPlanToSubscriptionType(activeMembership?.plan);
                const messageLimit = mapWhopPlanToMessageLimit(activeMembership?.plan);

                const { error: profileError } = await supabase
                  .from('profiles')
                  .upsert({
                    id: signInData.user.id,
                    full_name: userResponse.data.name || '',
                    subscription_type: subscriptionType,
                    subscription_id: activeMembership?.id || null,
                    subscription_status: activeMembership?.status || 'inactive',
                    daily_message_limit: messageLimit,
                    updated_at: new Date().toISOString()
                  });

                if (profileError) throw profileError;

                toast({
                  title: "Success",
                  description: "Your Whop subscription has been linked to your account.",
                });

                // Clean up URL and redirect
                window.history.replaceState({}, document.title, '/');
                window.location.href = '/';
              }
            }
          }
        } else {
          // If no code, we're done - not a Whop user
          setIsWhopUser(false);
          setIsLoadingWhop(false);
        }
      } catch (error: any) {
        // If there's an error, we're not a Whop user
        setIsWhopUser(false);
        setWhopUser(null);
        setWhopAccessToken(null);
        setWhopSubscription(null);
        
        toast({
          title: "Error",
          description: error.message || "Failed to authenticate with Whop",
          variant: "destructive"
        });
      } finally {
        setIsLoadingWhop(false);
      }
    };

    checkWhopAuth();
  }, [toast]);

  return (
    <WhopContext.Provider 
      value={{ 
        isWhopUser, 
        whopUser, 
        whopAccessToken,
        whopSubscription,
        isLoadingWhop 
      }}
    >
      {children}
    </WhopContext.Provider>
  );
};

export const useWhop = () => useContext(WhopContext); 