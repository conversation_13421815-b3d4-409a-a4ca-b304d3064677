import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

export default function WhopCallback() {
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    // The WhopContext will handle the actual OAuth flow
    // This component just shows a loading state and handles errors
    const params = new URLSearchParams(window.location.search);
    const error = params.get('error');
    const errorDescription = params.get('error_description');

    if (error) {
      toast({
        title: "Authentication Error",
        description: errorDescription || "Failed to authenticate with <PERSON><PERSON>",
        variant: "destructive"
      });
      navigate('/');
    }
  }, [navigate, toast]);

  return (
    <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0EA5E9] mx-auto mb-4"></div>
        <h2 className="text-xl font-medium text-white mb-2">Authenticating with Whop</h2>
        <p className="text-white/60">Please wait while we verify your subscription...</p>
      </div>
    </div>
  );
} 