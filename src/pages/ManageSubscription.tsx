import { useState, useEffect } from 'react';
import { Check, Crown, Bot, TrendingUp, ExternalLink, CreditCard } from 'lucide-react';
import { useUserLimits, PLAN_TYPES } from '@/hooks/useUserLimits';
import { useToast } from '@/hooks/use-toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { loadStripe } from '@stripe/stripe-js';

// Plans configuration matching the new 3-plan structure
const plans = [
  {
    id: PLAN_TYPES.basic,
    name: 'Basic',
    price: '$5.99',
    period: '/week',
    priceId: 'price_1ROYLKDebmd1GpTvct491Kw6',
    description: 'Essential features to get started',
    icon: Bot,
    features: [
      'All 7 AI Agents',
      'Stock and Crypto Analysis',
      'Instant Trading Information',
      '100 messages per month',
      '5 year Portfolio Backtesting',
      'Create 1 Portfolio'
    ],
    limitations: [
      'No Agent Builder access',
      'No Discover page access',
      'No Stock Search access',
      'No Stock Scanner access'
    ]
  },
  {
    id: PLAN_TYPES.pro,
    name: 'Pro',
    price: '$9.99',
    period: '/week',
    priceId: 'price_1ROYKjDebmd1GpTv5oYNMKMv',
    description: 'Full access to all premium features',
    icon: TrendingUp,
    popular: true,
    features: [
      'Everything in Basic',
      'Access to Agent Builder',
      'Discover Community Agents',
      'Advanced Stock Search',
      'Stock Scanner Tools',
      '200 messages per month',
      'Priority Support'
    ]
  },
  {
    id: PLAN_TYPES.premium,
    name: 'Premium',
    price: '$14.99',
    period: '/week',
    priceId: 'price_1ROYPremiumWeekly123456', // TODO: Add actual premium price ID
    description: 'Ultimate trading experience',
    icon: Crown,
    features: [
      'Everything in Pro',
      'Advanced AI Models',
      'Real-time Market Data',
      'Custom Indicators',
      'API Access',
      '300 messages per month',
      'Dedicated Support',
      'White-label Options'
    ]
  }
];

export default function ManageSubscription() {
  const { messagesUsed, messagesLimit, messagesRemaining, planType, isLoading: isLoadingLimits, handlePlanUpgrade } = useUserLimits();
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    };
    getCurrentUser();
  }, []);

  // Handle success redirect from Stripe
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const success = urlParams.get('success');

    if (success === 'true') {
      toast({
        title: "Subscription Updated",
        description: "Your subscription has been updated successfully.",
      });

      // Refresh user limits
      handlePlanUpgrade();

      // Clean up URL
      navigate('/subscription/manage', { replace: true });
    }
  }, [location, navigate, toast, handlePlanUpgrade]);

  // Get current plan
  const getCurrentPlan = () => {
    if (isLoadingLimits) return null;
    return plans.find(p => p.id === planType) || plans[0];
  };

  // Handle plan selection and Stripe checkout
  const handleSelectPlan = async (plan: any) => {
    if (isProcessing || !currentUser) return;

    // Check if premium plan has a valid price ID
    if (plan.id === PLAN_TYPES.premium && plan.priceId.includes('123456')) {
      toast({
        title: 'Coming Soon',
        description: 'Premium plan will be available soon. Please choose Pro for now.',
        variant: 'default'
      });
      return;
    }

    // Don't do anything if this is already the current plan
    if (plan.id === planType) {
      return;
    }

    setIsProcessing(true);

    try {
      // Get the session token for authorization
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error("No authentication token found");
      }

      // Create checkout session
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'create-checkout-session',
          priceId: plan.priceId,
          returnUrl: window.location.origin + '/subscription/manage?success=true',
          skipTrial: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      const { sessionId } = data;

      if (!sessionId) {
        throw new Error('No session ID returned from server');
      }

      // Redirect to Stripe Checkout
      const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
      if (!stripe) throw new Error("Stripe failed to load");

      const { error } = await stripe.redirectToCheckout({ sessionId });
      if (error) throw new Error(`Stripe redirect failed: ${error.message}`);

    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to start checkout process. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Open Stripe customer portal for subscription management
  const handleManageSubscription = async () => {
    if (!currentUser) return;

    setIsLoading(true);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error("No authentication token found");
      }

      // Create customer portal session
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'create-customer-portal-session',
          returnUrl: window.location.origin + '/subscription/manage'
        })
      });

      if (!response.ok) {
        throw new Error(`Server returned ${response.status}`);
      }

      const { url } = await response.json();

      if (url) {
        window.location.href = url;
      } else {
        throw new Error('No portal URL returned');
      }

    } catch (error) {
      console.error('Error opening customer portal:', error);
      toast({
        title: 'Error',
        description: 'Failed to open subscription management. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate message usage percentage
  const messageUsagePercentage = messagesLimit > 0 ? (messagesUsed / messagesLimit) * 100 : 0;
  const currentPlan = getCurrentPlan();

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col relative">
      {/* Loading Overlay */}
      {(isLoading || isProcessing) && (
        <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-[#141414]/90 p-8 rounded-xl border border-white/[0.08] text-center shadow-lg backdrop-blur-md">
            <div className="flex justify-center mb-4">
              <div className="w-10 h-10 border-2 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
            </div>
            <h3 className="text-lg font-medium text-white mb-2 font-sans">Processing Your Request</h3>
            <p className="text-white/60 text-sm font-sans">
              Please wait while we update your subscription...
            </p>
          </div>
        </div>
      )}

      <div className="flex-1 px-8 py-12 overflow-y-auto">
        {/* Clean Header */}
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h1 className="text-3xl font-normal text-white mb-4 font-sans">
            Manage Subscription
          </h1>
          <p className="text-white/60 text-base font-sans">
            Current plan: <span className="text-white">{currentPlan?.name || 'Basic'}</span>
          </p>
        </div>

        {/* Message Usage Card */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-white/[0.02] rounded-xl p-6 border border-white/[0.06]">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium text-white font-sans">
                Message Usage
              </h2>
              <button
                onClick={handleManageSubscription}
                className="flex items-center gap-2 px-4 py-2 bg-white/[0.03] hover:bg-white/[0.06] border border-white/[0.08] rounded-lg text-white/80 text-sm font-sans transition-colors"
              >
                <CreditCard className="w-4 h-4" />
                Manage Subscription
                <ExternalLink className="w-3 h-3" />
              </button>
            </div>

            {isLoadingLimits ? (
              <div className="h-16 flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
              </div>
            ) : (
              <>
                <div className="flex justify-between items-center mb-3">
                  <span className="text-white/70 text-sm font-sans">
                    {messagesUsed} of {messagesLimit} used
                  </span>
                  <span className="text-white/70 text-sm font-sans">
                    {messagesRemaining} remaining
                  </span>
                </div>

                <Progress
                  value={messageUsagePercentage}
                  className="h-2 bg-white/10"
                  indicatorClassName={
                    messageUsagePercentage > 90 ? "bg-red-500" :
                    messageUsagePercentage > 75 ? "bg-amber-500" :
                    "bg-emerald-500"
                  }
                />

                <p className="mt-3 text-white/50 text-sm font-sans">
                  Resets at the beginning of each billing cycle
                </p>
              </>
            )}
          </div>
        </div>

        {/* Plans Section */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              const Icon = plan.icon;
              const isSpotlight = plan.popular;
              const isCurrent = currentPlan?.id === plan.id;

              return (
                <div
                  key={plan.id}
                  className={`relative rounded-xl border transition-all duration-200 ${
                    isSpotlight
                      ? 'bg-white/[0.02] border-white/[0.12] scale-105'
                      : isCurrent
                      ? 'bg-white/[0.04] border-white/[0.12]'
                      : 'bg-white/[0.01] border-white/[0.06] hover:border-white/[0.10]'
                  }`}
                >
                  {/* Popular Badge */}
                  {isSpotlight && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-white text-black rounded-full text-xs font-medium">
                        Most Popular
                      </div>
                    </div>
                  )}

                  {/* Current Plan Badge */}
                  {isCurrent && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-green-500 text-white rounded-full text-xs font-medium">
                        Current Plan
                      </div>
                    </div>
                  )}

                  <div className="p-6">
                    {/* Plan Header */}
                    <div className="text-center mb-6">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Icon className="w-5 h-5 text-white/80" />
                        <h3 className="text-xl font-medium text-white font-sans">{plan.name}</h3>
                      </div>
                      <p className="text-white/50 text-sm font-sans mb-4">{plan.description}</p>

                      <div className="flex items-baseline justify-center gap-1">
                        <span className="text-3xl font-normal text-white font-sans">
                          {plan.price}
                        </span>
                        <span className="text-white/50 text-sm font-sans">{plan.period}</span>
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                          <span className="text-white/70 text-sm font-sans">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Limitations for Basic Plan */}
                    {plan.limitations && (
                      <div className="space-y-2 mb-6 p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]">
                        <p className="text-white/60 text-xs font-sans font-medium mb-2">Limitations:</p>
                        {plan.limitations.map((limitation, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-white/30 rounded-full flex-shrink-0"></div>
                            <span className="text-white/50 text-xs font-sans">{limitation}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Action Button */}
                    <button
                      onClick={() => handleSelectPlan(plan)}
                      disabled={isCurrent || isProcessing || (plan.id === PLAN_TYPES.premium && plan.priceId.includes('123456'))}
                      className={`w-full py-2.5 text-sm font-medium rounded-lg transition-all duration-200 font-sans ${
                        isCurrent
                          ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                          : (plan.id === PLAN_TYPES.premium && plan.priceId.includes('123456'))
                          ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                          : isSpotlight
                          ? 'bg-white hover:bg-white/95 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.03),0_1px_3px_rgba(0,0,0,0.08)]'
                          : 'bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08]'
                      }`}
                    >
                      {isProcessing ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                          Processing...
                        </div>
                      ) : isCurrent ? (
                        'Current Plan'
                      ) : (plan.id === PLAN_TYPES.premium && plan.priceId.includes('123456')) ? (
                        'Coming Soon'
                      ) : (
                        `Upgrade to ${plan.name}`
                      )}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="max-w-4xl mx-auto text-center">
          <p className="text-white/50 text-sm font-sans">
            7-day free trial • Cancel anytime • Secure payments by Stripe
          </p>
        </div>
      </div>
    </div>
  );
}