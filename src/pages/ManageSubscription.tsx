import { useState, useEffect } from 'react';
import { MessageSquare, Check } from 'lucide-react';
import { useSubscription, SUBSCRIPTION_TYPES } from '@/hooks/useSubscription';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useSubscriptionType } from '@/hooks/useSubscriptionType';
import { useToast } from '@/hooks/use-toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { useWhop } from '@/contexts/WhopContext';
import { motion } from 'framer-motion';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';
import { LoadingMessage } from '@/components/chat/LoadingStates';
import { loadStripe } from '@stripe/stripe-js';

// Paywall pricing configuration
const PAYWALL_VERSIONS = {
  paywall1: {
    weekly: 5.99
  },
  pro: {
    weekly: 9.99
  }
};

// Plans configuration with consistent IDs
const plans = [
  {
    id: {
      weekly: 'price_1ROYLKDebmd1GpTvct491Kw6'
    },
    name: 'Basic Plan',
    type: SUBSCRIPTION_TYPES.basic,
    price: {
      weekly: PAYWALL_VERSIONS.paywall1.weekly
    },
    period: 'per week',
    description: 'Entry level plan',
    features: [
      'All 7 AI Agents',
      'Stock and Crypto Analysis',
      'Instant Trading Information',
      '100 messages per month',
      '5 year Portfolio Backtesting',
      'Create 1 Portfolio'
    ]
  },
  {
    id: {
      weekly: 'price_1ROYKjDebmd1GpTv5oYNMKMv'
    },
    name: 'Investor',
    type: SUBSCRIPTION_TYPES.pro,
    price: {
      weekly: PAYWALL_VERSIONS.pro.weekly
    },
    period: 'per week',
    description: 'Everything you need to start investing',
    features: [
      'All 7 AI Agents',
      'Stock and Crypto Analysis',
      'Instant Trading Information',
      '200 messages per month',
      '20 year Portfolio Backtesting',
      'Create 3+ Portfolios'
    ]
  }
];

// Helper to map plan types to display names
const planTypeToDisplayName = {
  [SUBSCRIPTION_TYPES.basic]: 'Basic',
  [SUBSCRIPTION_TYPES.pro]: 'Investor'
};

export default function ManageSubscription() {
  const { subscription, isLoadingSubscription, refetch } = useSubscription();
  const { messagesUsed, messagesLimit, messagesRemaining, isLoading: isLoadingLimits, handlePlanUpgrade } = useUserLimits();
  const { subscriptionType, canUpgrade } = useSubscriptionType();
  const { isWhopUser, whopSubscription } = useWhop();
  const [selectedPlan, setSelectedPlan] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const [isAnnualPlan, setIsAnnualPlan] = useState(false);
  const [symbol, setSymbol] = useState('');
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [analysisResults, setAnalysisResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tradingStrategy, setTradingStrategy] = useState<{
    direction: 'LONG' | 'SHORT';
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    entryPoints: Array<{ price: number; timestamp: number; label: string }>;
    exitPoints: Array<{ price: number; timestamp: number; label: string }>;
    stopLoss: number;
    supportLevels: number[];
    resistanceLevels: number[];
    takeProfitTargets: Array<{ price: number; timestamp: number; label: string }>;
    riskRewardRatio: string;
  }>({
    direction: 'LONG',
    confidence: 'MEDIUM',
    entryPoints: [],
    exitPoints: [],
    stopLoss: 0,
    supportLevels: [],
    resistanceLevels: [],
    takeProfitTargets: [],
    riskRewardRatio: '1:1'
  });
  const [symbolTypes, setSymbolTypes] = useState({});

  // Set initial annual/monthly state based on subscription
  useEffect(() => {
    if (subscription?.interval === 'year') {
      setIsAnnualPlan(true);
    } else {
      setIsAnnualPlan(false);
    }
  }, [subscription]);

  // Handle Stripe redirect with query parameters
  useEffect(() => {
    // Skip if we're a Whop user
    if (isWhopUser) return;

    const handleStripeRedirect = async () => {
      // Create a URL object to easily parse query parameters
      const url = new URL(window.location.href);
      const sessionId = url.searchParams.get('session_id');
      const success = url.searchParams.get('success');
      const customerId = url.searchParams.get('customer_id');

      // Only process if we have a session ID
      if (sessionId) {
        setIsLoading(true);

        try {
          // Handle the checkout redirect
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              action: 'handle-checkout-redirect',
              sessionId,
              customerId
            })
          });

          const { session, error } = await response.json();

          if (error) {
            throw new Error(error);
          }

          if (session?.status === 'complete') {
            toast({
              title: "Subscription Updated",
              description: "Your subscription has been updated successfully.",
            });

            // Check if user profile exists
            const { data: { session: authSession } } = await supabase.auth.getSession();
            if (authSession?.user) {
              const { data: profileExists, error: profileCheckError } = await supabase
                .from('profiles')
                .select('id')
                .eq('id', authSession.user.id)
                .maybeSingle();

              // If profile doesn't exist, create it
              if (!profileExists) {
                const { error: createError } = await supabase
                  .from('profiles')
                  .insert({
                    id: authSession.user.id,
                    subscription_type: null,
                    has_seen_onboarding: false,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  });

                if (createError) {
                  console.error('Error creating profile:', createError);
                }
              }
            }

            // Refresh the onboarding status to hide the onboarding component
            // @ts-ignore - Accessing the function we added to window
            if (window.refreshOnboardingStatus) {
              try {
                // @ts-ignore
                window.refreshOnboardingStatus();
              } catch (error) {
                console.error('Error calling refreshOnboardingStatus:', error);
              }
            }
          }

          // Refresh subscription data and limits
          await refetch();
          await handlePlanUpgrade();

          // Remove query parameters from URL
          navigate('/subscription/manage', { replace: true });
        } catch (error) {
          console.error('Error handling Stripe redirect:', error);
          toast({
            title: "Update Failed",
            description: "There was an error processing your subscription update. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    handleStripeRedirect();
  }, [location, navigate, toast, setIsLoading, refetch, handlePlanUpgrade, isWhopUser]);

  // If subscription or limits data changes, update the UI
  useEffect(() => {
    // Skip if we're a Whop user
    if (isWhopUser) return;

    if (!subscription || isLoadingSubscription || isLoadingLimits) return;

    // Use subscription_type to determine selected plan
    if (subscription.subscription_type) {
      // Find plan by type
      const plan = plans.find(p => p.type === subscription.subscription_type);
      if (plan) {
        setSelectedPlan(plan.id.weekly);
        return;
      }
    }

    // Fallback to free plan
    setSelectedPlan('price_free');
  }, [subscription, isLoadingSubscription, isLoadingLimits, isWhopUser, plans]);

  // Refresh data function
  const refreshData = async () => {
    setIsLoading(true);

    try {
      // Refresh subscription and limits data
      await refetch();
      // Use handlePlanUpgrade for a more thorough refresh with forced reset
      await handlePlanUpgrade();

      toast({
        title: "Data Refreshed",
        description: "Your subscription information has been updated.",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Refresh Failed",
        description: "Could not refresh your subscription data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get current plan display name
  const getCurrentPlanName = () => {
    if (isWhopUser) {
      // Return plan name based on Whop subscription
      return whopSubscription?.plan?.name || 'Free';
    }

    if (!subscription || isLoadingSubscription) return 'Free';

    // Use subscription_type if available
    if (subscription.subscription_type) {
      return planTypeToDisplayName[subscription.subscription_type] || 'Free';
    }

    return 'Free';
  };

  // Handle plan selection and update
  const handleSelectPlan = async (planType: string) => {
    // If using Whop, redirect to Whop's subscription management
    if (isWhopUser) {
      window.location.href = 'https://whop.com/hub';
      return;
    }

    // Don't do anything if this is already the current plan
    if (isCurrentPlan(planType)) {
      return;
    }

    setIsLoading(true);

    try {
      const planToSelect = plans.find(p => p.type === planType);
      if (!planToSelect) {
        throw new Error("Invalid plan selected");
      }

      // For any paid plan selection, use Stripe checkout
      if (planToSelect.name !== 'Free') {
        try {
          const actionToUse = getCurrentPlanName() === 'Free' ? 'create-checkout-session' : 'create-checkout-session';

          // Check for no-trial parameters in the URL
          const urlParams = new URLSearchParams(window.location.search);
          const basicNoTrial = urlParams.get('basicnotrial') !== null;
          const proNoTrial = urlParams.get('pronotrial') !== null;
          const skipTrial = true;

          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              action: actionToUse,
              priceId: planToSelect.id.weekly,
              returnUrl: window.location.origin + '/subscription/manage?success=true',
              skipTrial
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server returned ${response.status}: ${errorText}`);
          }

          const responseData = await response.json();

          const { sessionId, error } = responseData;

          if (error) {
            throw new Error(error);
          }

          if (!sessionId) {
            throw new Error('No session ID returned from server');
          }

          // Redirect to Stripe checkout
          const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
          if (!stripe) {
            throw new Error("Stripe failed to load");
          }

          const { error: redirectError } = await stripe.redirectToCheckout({ sessionId });
          if (redirectError) {
            throw new Error(`Stripe redirect failed: ${redirectError.message}`);
          }
        } catch (error) {
          throw error;
        }
      }
      // Current plan is paid and downgrading to Free - show message to use Manage Subscription
      else if (getCurrentPlanName() !== 'Free' && planToSelect.name === 'Free') {
        // Instead of handling cancellation here, show guidance message
        toast({
          title: "Downgrade Not Available Here",
          description: "To downgrade to the Free plan, please click the 'Manage Subscription' button and cancel your subscription through Stripe.",
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('Error selecting plan:', error);
      toast({
        title: "Plan Change Failed",
        description: `Error: ${error.message || 'Unknown error occurred'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to check if a plan is the current plan
  const isCurrentPlan = (planType: string) => {
    if (!subscription || isLoadingSubscription) {
      return planType === SUBSCRIPTION_TYPES.basic;
    }

    return subscription.subscription_type === planType;
  };

  // Helper function to check if a plan is lower than current plan
  const isLowerTier = (planType: string) => {
    if (!subscription || isLoadingSubscription) return false;

    // Check if the current plan is Pro and the selected plan is Basic
    if (subscription.subscription_type === SUBSCRIPTION_TYPES.pro && planType === SUBSCRIPTION_TYPES.basic) {
      return true;
    }

    return false;
  };

  // Calculate message usage percentage
  const messageUsagePercentage = messagesLimit > 0 ? (messagesUsed / messagesLimit) * 100 : 0;
  const currentPlanName = getCurrentPlanName();

  const shouldShowChart = () => {
    return symbol && symbolTypes?.[symbol] !== 'CRYPTO';
  };

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk relative">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center backdrop-blur-sm animate-fade-in">
          <div className="bg-[#141414]/90 p-8 rounded-xl border border-[#303035]/60 text-center shadow-lg backdrop-blur-md">
            <div className="flex justify-center mb-4">
              <svg className="animate-spin h-10 w-10" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Processing Your Request</h3>
            <p className="text-white/60 text-sm">
              Please wait while we update your subscription...
            </p>
          </div>
        </div>
      )}

      <div className="flex-1 px-8 py-12 overflow-y-auto">
        {/* Clean Header */}
        <div className="max-w-2xl mx-auto text-center mb-12">
          <h1 className="text-3xl font-normal text-white mb-4 font-sans">
            Manage Subscription
          </h1>
          <p className="text-white/60 text-base font-sans">
            Current plan: <span className="text-white">{currentPlanName}</span>
          </p>
        </div>

        {/* Message Usage Card */}
        {!isWhopUser && (
          <div className="max-w-2xl mx-auto mb-12">
            <div className="bg-white/[0.02] rounded-xl p-6 border border-white/[0.06]">
              <h2 className="text-lg font-medium text-white mb-4 font-sans">
                Message Usage
              </h2>

              {isLoadingLimits ? (
                <div className="h-16 flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-white/70 text-sm font-sans">
                      {messagesUsed} of {messagesLimit} used
                    </span>
                    <span className="text-white/70 text-sm font-sans">
                      {messagesRemaining} remaining
                    </span>
                  </div>

                  <Progress
                    value={messageUsagePercentage}
                    className="h-2 bg-white/10"
                    indicatorClassName={
                      messageUsagePercentage > 90 ? "bg-red-500" :
                      messageUsagePercentage > 75 ? "bg-amber-500" :
                      "bg-emerald-500"
                    }
                  />

                  <p className="mt-3 text-white/50 text-sm font-sans">
                    Resets at the beginning of each billing cycle
                  </p>
                </>
              )}
            </div>
          </div>
        )}

        {/* Plans Section - Only show for basic users */}
        {canUpgrade() && (
          <div className="max-w-2xl mx-auto mb-12">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {plans.map((plan) => (
              <div
                key={plan.id.weekly}
                className={`relative rounded-xl p-6 border transition-all duration-200 ${
                  isCurrentPlan(plan.type)
                    ? 'bg-white/[0.04] border-white/[0.12]'
                    : 'bg-white/[0.01] border-white/[0.06] hover:border-white/[0.10]'
                }`}
              >
                {/* Current Plan Badge */}
                {isCurrentPlan(plan.type) && (
                  <div className="absolute -top-2 left-4">
                    <div className="px-2 py-1 bg-white text-black rounded-full text-xs font-medium">
                      Current
                    </div>
                  </div>
                )}

                <div className="mb-4">
                  <h3 className="text-lg font-medium text-white mb-2 font-sans">{plan.name}</h3>
                  <div className="flex items-baseline gap-1">
                    <span className="text-2xl font-normal text-white font-sans">
                      ${plan.price.weekly}
                    </span>
                    <span className="text-white/50 text-sm font-sans">/week</span>
                  </div>
                </div>

                <div className="space-y-2 mb-6">
                  {plan.features.slice(0, 4).map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                      <span className="text-white/70 text-sm font-sans">{feature}</span>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => handleSelectPlan(plan.type)}
                  disabled={isLoading || isCurrentPlan(plan.type) || isLowerTier(plan.type)}
                  className={`w-full py-2.5 text-sm font-medium rounded-lg transition-all duration-200 font-sans ${
                    isCurrentPlan(plan.type)
                      ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                      : isLowerTier(plan.type)
                        ? 'bg-white/[0.02] text-white/30 cursor-not-allowed border border-white/[0.04]'
                        : 'bg-white hover:bg-white/95 text-black shadow-sm hover:shadow-md'
                  }`}
                >
                  {isLoading ? 'Processing...' : isCurrentPlan(plan.type) ? 'Current Plan' : isLowerTier(plan.type) ? 'Not Available' : `Upgrade to ${plan.name}`}
                </button>
              </div>
              ))}
            </div>
          </div>
        )}

        {/* Success Message for Pro/Premium Users */}
        {!canUpgrade() && (
          <div className="max-w-2xl mx-auto mb-12">
            <div className="bg-white/[0.02] rounded-xl p-6 border border-white/[0.06] text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-500/20 rounded-full flex items-center justify-center">
                <Check className="w-8 h-8 text-green-400" />
              </div>
              <h2 className="text-lg font-medium text-white mb-3 font-sans">You're All Set!</h2>
              <p className="text-white/60 mb-4 font-sans">
                You're currently on the <span className="text-white font-medium">{subscriptionType === 'pro' ? 'Pro' : 'Premium'}</span> plan with full access to all features.
              </p>
            </div>
          </div>
        )}

        {/* Manage Subscription Button */}
        <div className="max-w-2xl mx-auto text-center mb-12">
          <button
            onClick={isWhopUser
              ? () => window.location.href = 'https://whop.com/hub'
              : () => window.open('https://billing.stripe.com/p/login/fZeaGq9Iw2hAg929AA', '_blank')}
            disabled={isLoading}
            className="px-6 py-2.5 bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] rounded-lg transition-colors duration-200 font-sans disabled:opacity-50"
          >
            {isLoading ? 'Processing...' : 'Manage Subscription'}
          </button>
          <p className="text-white/50 text-sm mt-3 font-sans">
            Cancel anytime • 30-day money-back guarantee
          </p>
        </div>

        {/* Whop User Message */}
        {isWhopUser && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white/[0.02] rounded-xl p-6 border border-white/[0.06]">
              <h2 className="text-lg font-medium text-white mb-3 font-sans">Whop Subscription Active</h2>
              <p className="text-white/60 mb-4 font-sans">
                You are subscribed through Whop. Manage your subscription in the Whop Hub.
              </p>
              <button
                onClick={() => window.location.href = 'https://whop.com/hub'}
                className="bg-white hover:bg-white/95 text-black px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-sans"
              >
                Go to Whop Hub
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Global Styles - Adding consistent styling with ChatInterface */}
      <style>
        {`
          /* Define fade-in animation */
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }

          .animate-fade-in {
            animation: fadeIn 0.15s ease-out forwards;
          }

          /* Fix for flashing focus states */
          *:focus {
            outline: none !important;
          }

          /* Remove default focus rings and overlays */
          *:focus-visible {
            outline: none !important;
            box-shadow: none !important;
          }

          /* Fix transitions to prevent flashing */
          button, input, textarea, select {
            transition: border-color 0.15s, background-color 0.15s !important;
            outline: none !important;
          }

          /* Ensure no overlay appears on focus/blur */
          button:focus, input:focus, textarea:focus, select:focus,
          button:active, input:active, textarea:active, select:active {
            box-shadow: none !important;
            outline: none !important;
            border-color: #303035 !important;
          }

          /* Fix for flashing box when clicking outside */
          div[role="dialog"]:not(:focus-within),
          button:not(:focus-within),
          input:not(:focus-within),
          select:not(:focus-within) {
            transition: none !important;
          }

          /* Fix for button hover states */
          button:hover {
            transition: background-color 0.15s ease !important;
          }

          /* Prevent any flashing on blur */
          *:not(:focus-visible) {
            transition: border-color 0.15s ease, background-color 0.15s ease !important;
          }

          /* Prevent any animations or transitions on form controls when they lose focus */
          button:not(:focus):not(:hover),
          input:not(:focus),
          textarea:not(:focus),
          select:not(:focus) {
            transition: none !important;
          }

          /* Original styles from before */
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          /* Add subtle transition effects */
          textarea, button {
            transition: all 0.15s ease;
          }

          /* Darker placeholder text on focus */
          textarea:focus::placeholder {
            color: rgba(255, 255, 255, 0.2);
          }
        `}
      </style>
    </div>
  );
}