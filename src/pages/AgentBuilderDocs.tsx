import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Bot, Zap, Settings, Target, Shield, Clock, TrendingUp, BarChart2,
  Volume2, Gauge, ArrowRight, CheckCircle, AlertTriangle, Star
} from 'lucide-react';

const AgentBuilderDocs: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <Bot className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold">AI Agent Builder Documentation</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Learn how to build powerful day trading strategies using our drag-and-drop AI agent builder
          </p>
        </div>

        {/* Quick Start */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Quick Start Guide
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-primary font-bold">1</span>
                </div>
                <h3 className="font-semibold">Drag Blocks</h3>
                <p className="text-sm text-muted-foreground">Drag blocks from the palette onto the canvas</p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-primary font-bold">2</span>
                </div>
                <h3 className="font-semibold">Connect & Configure</h3>
                <p className="text-sm text-muted-foreground">Connect blocks and configure their settings</p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-primary font-bold">3</span>
                </div>
                <h3 className="font-semibold">Test & Deploy</h3>
                <p className="text-sm text-muted-foreground">Backtest your strategy and deploy it live</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* How It Works */}
        <Card>
          <CardHeader>
            <CardTitle>How the Fuck This Actually Works</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">The Basic Concept</h3>
              <p className="text-muted-foreground">
                Think of it like building a flowchart for your trading decisions. Each block represents a piece of your trading logic:
              </p>
              <ul className="space-y-2 ml-4">
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Technical Analysis Blocks:</strong> RSI, MACD, moving averages, etc.</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk Management Blocks:</strong> Position sizing, stop losses, take profits</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Time Filters:</strong> Only trade during specific hours or days</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Logic Blocks:</strong> Combine multiple signals with AND/OR logic</span>
                </li>
              </ul>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">The Flow</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <Badge variant="outline">When Run</Badge>
                  <ArrowRight className="h-4 w-4" />
                  <Badge variant="outline">Technical Analysis</Badge>
                  <ArrowRight className="h-4 w-4" />
                  <Badge variant="outline">Conditions</Badge>
                  <ArrowRight className="h-4 w-4" />
                  <Badge variant="outline">Signal Output</Badge>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Your agent starts with a "When Run" block, processes through your technical analysis and conditions,
                then outputs a trading signal (bullish, bearish, or neutral).
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Block Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Block Categories</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Technical Analysis */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-500" />
                  <h3 className="font-semibold">Technical Analysis</h3>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div><strong>Moving Averages:</strong> SMA, EMA, WMA with custom periods</div>
                    <div className="text-xs text-blue-600 mt-1">
                      <strong>Output:</strong> Number (price value) | Example: 150.25
                    </div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div><strong>Momentum:</strong> RSI, Stochastic, Williams %R, CCI</div>
                    <div className="text-xs text-blue-600 mt-1">
                      <strong>Output:</strong> Number (0-100 for RSI/Stochastic) | Example: 73.2
                    </div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div><strong>Trend:</strong> MACD, ADX, Parabolic SAR, Ichimoku</div>
                    <div className="text-xs text-blue-600 mt-1">
                      <strong>Output:</strong> Number (indicator value) | Example: 2.45 (MACD), 65.8 (ADX)
                    </div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div><strong>Volume:</strong> VWAP, OBV, Volume Profile, MFI</div>
                    <div className="text-xs text-blue-600 mt-1">
                      <strong>Output:</strong> Number (price/volume value) | Example: 149.87 (VWAP), 1250000 (OBV)
                    </div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div><strong>Volatility:</strong> Bollinger Bands, ATR, Keltner Channels</div>
                    <div className="text-xs text-blue-600 mt-1">
                      <strong>Output:</strong> Number (price/volatility value) | Example: 2.45 (ATR), 152.30 (Upper Band)
                    </div>
                  </div>
                </div>
              </div>

              {/* Risk Management */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-500" />
                  <h3 className="font-semibold">Risk Management</h3>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div><strong>Position Sizing:</strong> Fixed amount, % of account, volatility-based</div>
                    <div className="text-xs text-green-600 mt-1">
                      <strong>Output:</strong> Number (dollar amount or shares) | Example: 1000 (shares), 5000.00 (dollars)
                    </div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div><strong>Stop Loss:</strong> Percentage, ATR-based, trailing stops</div>
                    <div className="text-xs text-green-600 mt-1">
                      <strong>Output:</strong> Number (price level) | Example: 145.50
                    </div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div><strong>Take Profit:</strong> Risk/reward ratios, multiple targets</div>
                    <div className="text-xs text-green-600 mt-1">
                      <strong>Output:</strong> Number (price level) | Example: 158.75
                    </div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div><strong>Risk Limits:</strong> Daily loss limits, max positions</div>
                    <div className="text-xs text-green-600 mt-1">
                      <strong>Output:</strong> Boolean (true/false) | Example: true (within limits), false (limit exceeded)
                    </div>
                  </div>
                </div>
              </div>

              {/* Price Action */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <BarChart2 className="h-5 w-5 text-purple-500" />
                  <h3 className="font-semibold">Price Action</h3>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <div><strong>Candlestick Patterns:</strong> Doji, hammer, engulfing, etc.</div>
                    <div className="text-xs text-purple-600 mt-1">
                      <strong>Output:</strong> Boolean (true/false) OR Signal (-1, 0, 1) | Example: true (pattern detected), 1 (bullish), -1 (bearish), 0 (neutral)
                    </div>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <div><strong>Chart Patterns:</strong> Triangles, flags, head & shoulders</div>
                    <div className="text-xs text-purple-600 mt-1">
                      <strong>Output:</strong> Boolean (true/false) | Example: true (pattern forming), false (no pattern)
                    </div>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <div><strong>Breakouts:</strong> Support/resistance breaks with volume</div>
                    <div className="text-xs text-purple-600 mt-1">
                      <strong>Output:</strong> Signal (-1, 0, 1) | Example: 1 (upward breakout), -1 (downward breakout), 0 (no breakout)
                    </div>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <div><strong>Gaps:</strong> Gap up/down detection and analysis</div>
                    <div className="text-xs text-purple-600 mt-1">
                      <strong>Output:</strong> Number (gap size %) | Example: 2.5 (2.5% gap up), -1.8 (-1.8% gap down), 0 (no gap)
                    </div>
                  </div>
                </div>
              </div>

              {/* Time-Based */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold">Time-Based</h3>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <div><strong>Time Filters:</strong> Trade only during specific hours</div>
                    <div className="text-xs text-orange-600 mt-1">
                      <strong>Output:</strong> Boolean (true/false) | Example: true (within time window), false (outside time window)
                    </div>
                  </div>
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <div><strong>Session Filters:</strong> Pre-market, regular hours, after-hours</div>
                    <div className="text-xs text-orange-600 mt-1">
                      <strong>Output:</strong> Boolean (true/false) | Example: true (in session), false (outside session)
                    </div>
                  </div>
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <div><strong>Economic Calendar:</strong> Avoid trading around news events</div>
                    <div className="text-xs text-orange-600 mt-1">
                      <strong>Output:</strong> Boolean (true/false) | Example: true (safe to trade), false (news event nearby)
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Configuring Blocks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <h3 className="font-semibold">Every Block is Customizable</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-muted/50 p-3 rounded-lg text-center">
                  <Settings className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-sm font-medium">Click the Gear Icon</div>
                  <div className="text-xs text-muted-foreground">Opens configuration panel</div>
                </div>
                <div className="bg-muted/50 p-3 rounded-lg text-center">
                  <Target className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-sm font-medium">Adjust Parameters</div>
                  <div className="text-xs text-muted-foreground">Periods, thresholds, methods</div>
                </div>
                <div className="bg-muted/50 p-3 rounded-lg text-center">
                  <CheckCircle className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-sm font-medium">Auto-Save</div>
                  <div className="text-xs text-muted-foreground">Changes saved automatically</div>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <h3 className="font-semibold">Common Configuration Options</h3>
              <div className="space-y-2 text-sm">
                <div><strong>Timeframes:</strong> 1min, 5min, 15min, 1hour, 4hour, daily</div>
                <div><strong>Periods:</strong> Number of bars to calculate indicators (e.g., RSI 14-period)</div>
                <div><strong>Thresholds:</strong> Overbought/oversold levels, breakout confirmation levels</div>
                <div><strong>Methods:</strong> Different calculation methods for the same indicator</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Complete Block Output Reference */}
        <Card>
          <CardHeader>
            <CardTitle>Complete Block Output Reference</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Every block outputs specific data types. Here's the complete reference for all blocks:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Logic & Control Blocks */}
              <div className="space-y-3">
                <h4 className="font-semibold text-slate-700">Logic & Control Blocks</h4>
                <div className="space-y-2 text-sm">
                  <div className="bg-slate-50 p-2 rounded">
                    <div><strong>When Run:</strong> Triggers the agent</div>
                    <div className="text-xs text-slate-600">Output: Boolean (true) | Example: true</div>
                  </div>
                  <div className="bg-slate-50 p-2 rounded">
                    <div><strong>AND Block:</strong> All inputs must be true</div>
                    <div className="text-xs text-slate-600">Output: Boolean | Example: true (all true), false (any false)</div>
                  </div>
                  <div className="bg-slate-50 p-2 rounded">
                    <div><strong>OR Block:</strong> Any input can be true</div>
                    <div className="text-xs text-slate-600">Output: Boolean | Example: true (any true), false (all false)</div>
                  </div>
                  <div className="bg-slate-50 p-2 rounded">
                    <div><strong>Compare Block:</strong> Compares two values</div>
                    <div className="text-xs text-slate-600">Output: Boolean | Example: true (50 {'>'} 30), false (20 {'>'} 70)</div>
                  </div>
                </div>
              </div>

              {/* Signal Blocks */}
              <div className="space-y-3">
                <h4 className="font-semibold text-red-700">Signal Output Blocks</h4>
                <div className="space-y-2 text-sm">
                  <div className="bg-red-50 p-2 rounded">
                    <div><strong>Bullish Signal:</strong> Buy recommendation</div>
                    <div className="text-xs text-red-600">Output: Signal (1) | Example: 1</div>
                  </div>
                  <div className="bg-red-50 p-2 rounded">
                    <div><strong>Bearish Signal:</strong> Sell recommendation</div>
                    <div className="text-xs text-red-600">Output: Signal (-1) | Example: -1</div>
                  </div>
                  <div className="bg-red-50 p-2 rounded">
                    <div><strong>Neutral Signal:</strong> Hold/no action</div>
                    <div className="text-xs text-red-600">Output: Signal (0) | Example: 0</div>
                  </div>
                  <div className="bg-red-50 p-2 rounded">
                    <div><strong>Confidence Boost:</strong> Increases signal strength</div>
                    <div className="text-xs text-red-600">Output: Number (0.0-1.0) | Example: 0.75 (75% confidence)</div>
                  </div>
                </div>
              </div>

              {/* Market Condition Blocks */}
              <div className="space-y-3">
                <h4 className="font-semibold text-indigo-700">Market Condition Blocks</h4>
                <div className="space-y-2 text-sm">
                  <div className="bg-indigo-50 p-2 rounded">
                    <div><strong>Trend Detection:</strong> Market direction</div>
                    <div className="text-xs text-indigo-600">Output: Signal (-1, 0, 1) | Example: 1 (uptrend), -1 (downtrend), 0 (sideways)</div>
                  </div>
                  <div className="bg-indigo-50 p-2 rounded">
                    <div><strong>Volatility Regime:</strong> Market volatility level</div>
                    <div className="text-xs text-indigo-600">Output: Number (0-100) | Example: 25.5 (low vol), 85.2 (high vol)</div>
                  </div>
                  <div className="bg-indigo-50 p-2 rounded">
                    <div><strong>Market Sentiment:</strong> Overall market mood</div>
                    <div className="text-xs text-indigo-600">Output: Number (-1.0 to 1.0) | Example: 0.65 (bullish), -0.40 (bearish)</div>
                  </div>
                  <div className="bg-indigo-50 p-2 rounded">
                    <div><strong>Market Breadth:</strong> How many stocks are participating</div>
                    <div className="text-xs text-indigo-600">Output: Number (0-100) | Example: 78.5 (broad participation)</div>
                  </div>
                </div>
              </div>

              {/* Entry/Exit Logic Blocks */}
              <div className="space-y-3">
                <h4 className="font-semibold text-teal-700">Entry/Exit Logic Blocks</h4>
                <div className="space-y-2 text-sm">
                  <div className="bg-teal-50 p-2 rounded">
                    <div><strong>Multi-Timeframe:</strong> Combines multiple timeframes</div>
                    <div className="text-xs text-teal-600">Output: Signal (-1, 0, 1) | Example: 1 (all timeframes align bullish)</div>
                  </div>
                  <div className="bg-teal-50 p-2 rounded">
                    <div><strong>Confluence:</strong> Multiple signals agree</div>
                    <div className="text-xs text-teal-600">Output: Boolean | Example: true (3+ signals agree), false (conflicting)</div>
                  </div>
                  <div className="bg-teal-50 p-2 rounded">
                    <div><strong>Scale In/Out:</strong> Gradual position building</div>
                    <div className="text-xs text-teal-600">Output: Number (0.0-1.0) | Example: 0.33 (33% of position)</div>
                  </div>
                  <div className="bg-teal-50 p-2 rounded">
                    <div><strong>Partial Profits:</strong> Take profits gradually</div>
                    <div className="text-xs text-teal-600">Output: Number (0.0-1.0) | Example: 0.50 (take 50% profits)</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Type Legend */}
        <Card>
          <CardHeader>
            <CardTitle>Data Type Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="font-semibold text-blue-700 mb-2">Boolean</h4>
                <p className="text-sm text-blue-600">true or false</p>
                <p className="text-xs text-blue-500 mt-1">Used for: Conditions, filters, pattern detection</p>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <h4 className="font-semibold text-green-700 mb-2">Number</h4>
                <p className="text-sm text-green-600">Any numeric value</p>
                <p className="text-xs text-green-500 mt-1">Used for: Prices, indicators, percentages, amounts</p>
              </div>
              <div className="bg-purple-50 p-3 rounded-lg">
                <h4 className="font-semibold text-purple-700 mb-2">Signal</h4>
                <p className="text-sm text-purple-600">-1 (bearish), 0 (neutral), 1 (bullish)</p>
                <p className="text-xs text-purple-500 mt-1">Used for: Trading decisions, trend direction</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Connecting Blocks */}
        <Card>
          <CardHeader>
            <CardTitle>Connecting Blocks (The Important Shit)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <h3 className="font-semibold">How Connections Work</h3>
              <p className="text-muted-foreground">
                Blocks have input handles (left side) and output handles (right side). Data flows from left to right.
              </p>

              <div className="bg-muted/50 p-4 rounded-lg space-y-3">
                <h4 className="font-medium">Connection Rules & Handle Colors:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <strong>Blue handles:</strong> Numbers & Booleans
                    </div>
                    <p className="text-xs text-muted-foreground ml-5">Technical indicators, prices, true/false values</p>
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <strong>Green handles:</strong> Bullish signals
                    </div>
                    <p className="text-xs text-muted-foreground ml-5">Buy signals, positive conditions, signal value = 1</p>
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <strong>Red handles:</strong> Bearish signals
                    </div>
                    <p className="text-xs text-muted-foreground ml-5">Sell signals, negative conditions, signal value = -1</p>
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                      <strong>Gray handles:</strong> Neutral signals
                    </div>
                    <p className="text-xs text-muted-foreground ml-5">Hold signals, no action, signal value = 0</p>
                  </div>
                </div>
                <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                  <strong>Important:</strong> You can only connect handles of compatible types. Numbers connect to numbers, signals connect to signal inputs.
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Example Flow:</h4>
                <div className="bg-slate-50 p-4 rounded-lg">
                  <div className="text-sm font-mono">
                    When Run → RSI(14) → Condition(RSI {'>'} 70) → Bearish Signal
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    This creates a simple overbought signal when RSI goes above 70
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Example Strategies */}
        <Card>
          <CardHeader>
            <CardTitle>Example Trading Strategies</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Simple RSI Strategy */}
            <div className="space-y-3">
              <h3 className="font-semibold text-green-600">Simple RSI Mean Reversion</h3>
              <div className="bg-green-50 p-4 rounded-lg space-y-2">
                <div className="text-sm font-mono">
                  When Run → RSI(14, 1hour) → Condition(RSI &lt; 30) → Bullish Signal
                </div>
                <p className="text-xs text-muted-foreground">
                  Buy when RSI drops below 30 (oversold condition)
                </p>
                <div className="mt-2 p-2 bg-white/50 rounded text-xs">
                  <strong>Data Flow:</strong> true → 28.5 → true → 1 (bullish signal)
                </div>
              </div>
            </div>

            {/* MACD + Volume Strategy */}
            <div className="space-y-3">
              <h3 className="font-semibold text-blue-600">MACD + Volume Confirmation</h3>
              <div className="bg-blue-50 p-4 rounded-lg space-y-2">
                <div className="text-sm font-mono">
                  When Run → MACD(12,26,9) → Condition(MACD {'>'} Signal) → Volume Confirmation → Bullish Signal
                </div>
                <p className="text-xs text-muted-foreground">
                  Buy when MACD crosses above signal line with volume confirmation
                </p>
                <div className="mt-2 p-2 bg-white/50 rounded text-xs">
                  <strong>Data Flow:</strong> true → 1.25 → true → true → 1 (bullish signal)
                </div>
              </div>
            </div>

            {/* Complex Multi-Timeframe */}
            <div className="space-y-3">
              <h3 className="font-semibold text-purple-600">Multi-Timeframe Trend Following</h3>
              <div className="bg-purple-50 p-4 rounded-lg space-y-2">
                <div className="text-sm font-mono">
                  When Run → EMA(20, 4hour) → EMA(50, 1hour) → Confluence → Time Filter(9:30-16:00) → Bullish Signal
                </div>
                <p className="text-xs text-muted-foreground">
                  Only trade when both 4-hour and 1-hour trends align, during market hours
                </p>
                <div className="mt-2 p-2 bg-white/50 rounded text-xs">
                  <strong>Data Flow:</strong> true → 152.30 → 151.85 → true → true → 1 (bullish signal)
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pro Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              Pro Tips & Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Do This:</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Always include risk management blocks</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Use time filters to avoid low-volume periods</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Combine multiple indicators for confirmation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Backtest your strategies before going live</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold text-red-600">Don't Do This:</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't over-optimize on historical data</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't use too many conflicting indicators</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't ignore risk management</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't trade without stop losses</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Troubleshooting */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting Common Issues</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="border-l-4 border-yellow-500 pl-4">
                <h4 className="font-semibold">Block Won't Connect</h4>
                <p className="text-sm text-muted-foreground">
                  Make sure you're connecting compatible handle types. Some blocks only accept specific input types.
                </p>
              </div>

              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-semibold">Agent Shows Errors</h4>
                <p className="text-sm text-muted-foreground">
                  Check that all required blocks are connected and configured. Every agent needs at least a "When Run" block and a final output.
                </p>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold">Strategy Not Triggering</h4>
                <p className="text-sm text-muted-foreground">
                  Verify your time filters and market conditions. Your strategy might be waiting for the right conditions.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Ready to build your first trading agent? Head to the Agent Builder and start dragging blocks!
          </p>
        </div>
      </div>
    </div>
  );
};

export default AgentBuilderDocs;
