import React, { useState, useEffect } from 'react';
import { Search, Filter, Star, Download, Calendar, TrendingUp, Grid, List } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useResponsive } from '@/hooks/useResponsive';
import { discoverAgents, importAgent, type PublishedAgent, type AgentCategory, type DiscoverFilters } from '@/services/discoverService';
import AgentCard from '@/components/discover/AgentCard';
import AgentFilters from '@/components/discover/AgentFilters';
import PublishAgentModal from '@/components/discover/PublishAgentModal';

const Discover: React.FC = () => {
  const { toast } = useToast();
  const { isMobile, classes } = useResponsive();
  const [agents, setAgents] = useState<PublishedAgent[]>([]);
  const [categories, setCategories] = useState<AgentCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [importing, setImporting] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showPublishModal, setShowPublishModal] = useState(false);
  
  // Filter states
  const [filters, setFilters] = useState<DiscoverFilters>({
    search: '',
    category: '',
    sortBy: 'newest',
    sortOrder: 'desc',
    limit: 20,
    offset: 0
  });

  // Pagination state
  const [pagination, setPagination] = useState({
    total: 0,
    hasMore: false
  });

  // Load agents
  const loadAgents = async (newFilters?: DiscoverFilters, append = false) => {
    try {
      setLoading(!append);
      
      const filtersToUse = newFilters || filters;
      const response = await discoverAgents(filtersToUse);
      
      if (response.success) {
        if (append) {
          setAgents(prev => [...prev, ...response.agents]);
        } else {
          setAgents(response.agents);
        }
        setCategories(response.categories);
        setPagination({
          total: response.pagination.total,
          hasMore: response.pagination.hasMore
        });
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to load agents"
        });
      }
    } catch (error) {
      console.error('Error loading agents:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadAgents();
  }, []);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<DiscoverFilters>) => {
    const updatedFilters = {
      ...filters,
      ...newFilters,
      offset: 0 // Reset pagination when filters change
    };
    setFilters(updatedFilters);
    loadAgents(updatedFilters);
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    handleFilterChange({ search: searchTerm });
  };

  // Handle load more
  const handleLoadMore = () => {
    const newFilters = {
      ...filters,
      offset: agents.length
    };
    setFilters(newFilters);
    loadAgents(newFilters, true);
  };

  // Handle import agent
  const handleImportAgent = async (publishedAgentId: string, customName?: string) => {
    try {
      setImporting(publishedAgentId);
      
      const response = await importAgent({
        publishedAgentId,
        customName
      });
      
      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Agent imported successfully"
        });
        
        // Update download count locally
        setAgents(prev => prev.map(agent => 
          agent.id === publishedAgentId 
            ? { ...agent, downloadCount: agent.downloadCount + 1 }
            : agent
        ));
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to import agent"
        });
      }
    } catch (error) {
      console.error('Error importing agent:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setImporting(null);
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className={`max-w-7xl mx-auto ${isMobile ? 'px-3 py-4' : 'px-8 py-8'}`}>
        {/* Clean Header */}
        <div className={`${isMobile ? 'mb-6' : 'mb-8'}`}>
          <div className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'} ${isMobile ? 'mb-4' : 'mb-6'}`}>
            <div>
              <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-medium text-white mb-2`} style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                Discover Agents
              </h1>
              <p className="text-white/50 text-sm" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                Explore and import AI trading agents created by the community
              </p>
            </div>
            <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
              <Button
                onClick={() => setShowPublishModal(true)}
                className={`bg-white text-black hover:bg-white/90 border-0 shadow-[0_0_20px_rgba(255,255,255,0.2)] hover:shadow-[0_0_30px_rgba(255,255,255,0.3)] transition-all duration-200 ${isMobile ? 'px-3 py-2 text-sm' : ''}`}
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                {isMobile ? 'Publish' : 'Publish Agent'}
              </Button>
              <div className={`flex items-center gap-1 bg-white/[0.02] border border-white/[0.08] rounded-lg ${isMobile ? 'p-0.5' : 'p-1'}`}>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={`${viewMode === 'grid'
                    ? "bg-white/[0.08] text-white hover:bg-white/[0.12] border-0"
                    : "text-white/60 hover:text-white hover:bg-white/[0.04] border-0"
                  } ${isMobile ? 'px-2 py-1' : ''}`}
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={`${viewMode === 'list'
                    ? "bg-white/[0.08] text-white hover:bg-white/[0.12] border-0"
                    : "text-white/60 hover:text-white hover:bg-white/[0.04] border-0"
                  } ${isMobile ? 'px-2 py-1' : ''}`}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Clean Search and Filters */}
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center gap-4'} ${isMobile ? 'mb-6' : 'mb-8'}`}>
            <div className={`relative ${isMobile ? 'w-full' : 'flex-1 max-w-md'}`}>
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 w-4 h-4" />
              <Input
                placeholder="Search agents..."
                value={filters.search || ''}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 h-10 bg-white/[0.02] border-white/[0.08] text-white placeholder:text-white/40 focus:border-white/[0.15] focus:ring-0 hover:border-white/[0.12] transition-all duration-200 rounded-md"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              />
            </div>
            <Select value={filters.sortBy} onValueChange={(value) => handleFilterChange({ sortBy: value as any })}>
              <SelectTrigger className={`${isMobile ? 'w-full' : 'w-48'} h-10 bg-white/[0.02] border-white/[0.08] text-white hover:border-white/[0.12] focus:border-white/[0.15] transition-all duration-200 rounded-md`} style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-[#1A1A1A] border-white/[0.08] text-white">
                <SelectItem value="newest" className="text-white hover:bg-white/[0.04] focus:bg-white/[0.04]">Newest</SelectItem>
                <SelectItem value="popular" className="text-white hover:bg-white/[0.04] focus:bg-white/[0.04]">Most Popular</SelectItem>
                <SelectItem value="rating" className="text-white hover:bg-white/[0.04] focus:bg-white/[0.04]">Highest Rated</SelectItem>
                <SelectItem value="downloads" className="text-white hover:bg-white/[0.04] focus:bg-white/[0.04]">Most Downloaded</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Main Content */}
        <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'lg:grid-cols-4 gap-8'}`}>
          {/* Filters Sidebar */}
          <div className={`${isMobile ? 'order-2' : 'lg:col-span-1'}`}>
            <AgentFilters
              categories={categories}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>

          {/* Agents Grid/List */}
          <div className={`${isMobile ? 'order-1' : 'lg:col-span-3'}`}>
            {loading && agents.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white/20 mx-auto mb-4"></div>
                  <p className="text-white/50" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Loading agents...</p>
                </div>
              </div>
            ) : agents.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-white/40 mb-4">
                  <Search className="w-16 h-16 mx-auto mb-6 opacity-30" />
                  <h3 className="text-xl font-medium mb-2 text-white/60" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>No agents found</h3>
                  <p className="text-white/40" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Try adjusting your search criteria or filters</p>
                </div>
              </div>
            ) : (
              <>
                <div className={viewMode === 'grid'
                  ? `grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'}`
                  : `${isMobile ? 'space-y-3' : 'space-y-4'}`
                }>
                  {agents.map((agent) => (
                    <AgentCard
                      key={agent.id}
                      agent={agent}
                      viewMode={viewMode}
                      onImport={handleImportAgent}
                      importing={importing === agent.id}
                    />
                  ))}
                </div>

                {/* Clean Load More Button */}
                {pagination.hasMore && (
                  <div className="text-center mt-8">
                    <Button
                      onClick={handleLoadMore}
                      disabled={loading}
                      variant="outline"
                      className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04] hover:border-white/[0.2] bg-transparent transition-all duration-200"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    >
                      {loading ? 'Loading...' : 'Load More'}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Publish Agent Modal */}
      <PublishAgentModal
        isOpen={showPublishModal}
        onClose={() => setShowPublishModal(false)}
        categories={categories}
        onSuccess={() => {
          setShowPublishModal(false);
          loadAgents(); // Refresh the list
        }}
      />
    </div>
  );
};

export default Discover;
