import { useState, useEffect, useRef } from 'react';
import { useUserLimits, PLAN_TYPES } from '@/hooks/useUserLimits';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useResponsive } from '@/hooks/useResponsive';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { ArrowUp, Briefcase, ChevronRight, Loader2, Plus, Newspaper, ExternalLink } from 'lucide-react';
import { useGamification } from '@/contexts/GamificationContext';
// AnimatedButton removed - using plain white button instead
import MiniPortfolioChart from "@/components/portfolio/MiniPortfolioChart";
import { WelcomeHeading } from "@/components/ui/WelcomeHeading";
import ReactECharts from 'echarts-for-react';
import type { EChartsOption } from 'echarts';
import { Dialog, DialogContent } from "@/components/ui/dialog";
// PromptCarousel replaced with inline compact carousel

interface PortfolioStock {
  ticker: string;
  allocation: number;
  name?: string;
  logo?: string;
  domain?: string;
}

interface Portfolio {
  name: string;
  description: string;
  stocks: PortfolioStock[];
}

// Add interface for saved portfolio
interface SavedPortfolio {
  id: string;
  name: string;
  description: string;
  stocks: PortfolioStock[];
  created_at: string;
}

// Add custom animation styles for the gradient heading
const animationStyles = `
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .gradient-text {
    background: linear-gradient(90deg, rgba(255,255,255,0.95), rgba(200,200,200,0.8), rgba(170,170,170,0.9));
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 8s ease infinite;
  }
`;

// Update the animation styles for better horizontal transitions
const transitionStyles = `
  @keyframes tabClick {
    0% {
      transform: scale(0.98);
    }
    60% {
      transform: scale(0.97);
    }
    100% {
      transform: scale(1);
    }
  }

  .tab-click {
    animation: tabClick 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .tabs-content {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .animate-in-right {
    animation: slideInRight 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-in-left {
    animation: slideInLeft 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  @keyframes slideInRight {
    0% {
      opacity: 0;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInLeft {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .tab-item-animation > * {
    opacity: 0;
    animation: fadeIn 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .tab-item-animation > *:nth-child(1) { animation-delay: 0.1s; }
  .tab-item-animation > *:nth-child(2) { animation-delay: 0.15s; }
  .tab-item-animation > *:nth-child(3) { animation-delay: 0.2s; }
  .tab-item-animation > *:nth-child(4) { animation-delay: 0.25s; }
  .tab-item-animation > *:nth-child(5) { animation-delay: 0.3s; }
`;

// Add new chart component for the main performance visualization
const PortfolioPerformanceChart = (props: {
  data: any,
  portfolioValue: number,
  portfolioReturn: number,
  investmentAmount: number,
  investmentDate: string,
  portfolio?: any // Pass the generated portfolio
}) => {
  const [chartData, setChartData] = useState<{date: string, value: number}[]>([]);
  const [isLoadingChart, setIsLoadingChart] = useState<boolean>(false);
  const [chartError, setChartError] = useState<string | null>(null);

  // Check if we have investment information
  const hasInvestment = props.investmentAmount > 0 && props.investmentDate;
  console.log('PortfolioPerformanceChart props:', props);
  console.log('Has investment:', hasInvestment);

  // Use the actual portfolio value and return if available, otherwise use zeros
  const displayValue = props.portfolioValue > 0 ? props.portfolioValue : (hasInvestment ? props.investmentAmount : 0);
  const displayReturn = props.portfolioReturn !== 0 ? props.portfolioReturn : 0;

  // Determine chart color based on investment status and return
  // Always use red for negative returns and green for positive
  const chartColor = !hasInvestment ? 'rgba(128, 128, 128, 0.9)' : (displayReturn >= 0 ? 'rgba(78, 184, 151, 0.9)' : 'rgba(229, 128, 128, 0.9)');

  // Console log will be moved after isPositive is defined

  // Fetch backtesting data from Polygon via Supabase edge function
  useEffect(() => {
    const fetchBacktestData = async () => {
      if (!props.portfolio || !props.portfolio.stocks) {
        // Generate empty chart data if we don't have portfolio info
        const emptyData = generateEmptyChartData();
        setChartData(emptyData);
        return;
      }

      setIsLoadingChart(true);
      setChartError(null);

      try {
        // Get the current session for authentication
        const { data: sessionData } = await supabase.auth.getSession();

        // Calculate start date for backtesting (5 years ago)
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date();
        startDate.setFullYear(startDate.getFullYear() - 5); // 5 years of historical data
        const backtestStartDate = startDate.toISOString().split('T')[0];

        console.log(`Fetching backtest data from ${backtestStartDate} to ${endDate}`);

        // Call the Supabase edge function to get chart data
        const { data, error } = await supabase.functions.invoke('portfolio-chart', {
          body: JSON.stringify({
            action: 'getChartData',
            stocks: props.portfolio.stocks,
            startDate: backtestStartDate,
            endDate: endDate
          }),
          headers: sessionData?.session ? {
            Authorization: `Bearer ${sessionData.session.access_token}`
          } : undefined
        });

        if (error) throw error;

        if (data?.chartData && data.chartData.length > 0) {
          console.log('Received backtest data:', data.chartData);

          // For backtesting, we start with a hypothetical $10,000 investment
          const initialInvestment = 10000;

          // Scale the chart data based on initial investment
          const scaledData = scaleChartData(data.chartData, initialInvestment);
          setChartData(scaledData);
        } else {
          console.log('No backtest data received, using fallback');
          const fallbackData = generateFallbackChartData();
          setChartData(fallbackData);
        }
      } catch (error) {
        console.error('Error fetching backtest data:', error);
        setChartError('Failed to load backtest data');
        const fallbackData = generateFallbackChartData();
        setChartData(fallbackData);
      } finally {
        setIsLoadingChart(false);
      }
    };

    fetchBacktestData();
  }, [props.portfolio?.stocks]);



  // Generate empty chart data (flat line at zero)
  const generateEmptyChartData = () => {
    const today = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(today.getMonth() - 6);

    // Create a flat line at zero for the last 6 months
    const dataPoints = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(sixMonthsAgo);
      date.setMonth(sixMonthsAgo.getMonth() + i);
      dataPoints.push({
        date: date.toISOString().split('T')[0],
        value: 0
      });
    }
    return dataPoints;
  };

  // Generate fallback chart data if API fails
  const generateFallbackChartData = () => {
    if (!props.investmentDate || props.investmentAmount <= 0) {
      return generateEmptyChartData();
    }

    // We have investment info - start chart from investment date
    const investmentDate = new Date(props.investmentDate);
    const today = new Date();

    // Calculate the number of months between investment date and today
    const monthsDiff = (today.getFullYear() - investmentDate.getFullYear()) * 12 +
                       (today.getMonth() - investmentDate.getMonth());

    // Ensure we have at least 2 data points
    const numPoints = Math.max(2, monthsDiff + 1);

    // Create data points from investment date to today
    const dataPoints = [];

    // Start with the investment amount on the investment date
    dataPoints.push({
      date: props.investmentDate,
      value: props.investmentAmount
    });

    // If we only have one month or less, add a small variation for today
    if (monthsDiff <= 1) {
      dataPoints.push({
        date: today.toISOString().split('T')[0],
        value: props.portfolioValue
      });
      return dataPoints;
    }

    // Otherwise, generate points for each month with some variation
    for (let i = 1; i < numPoints; i++) {
      const pointDate = new Date(investmentDate);
      pointDate.setMonth(investmentDate.getMonth() + i);

      // If we've gone past today, stop
      if (pointDate > today) break;

      // Calculate a value based on the portfolio return over time
      const monthlyReturn = props.portfolioReturn / numPoints;
      const cumulativeReturn = (1 + (monthlyReturn / 100)) ** i;
      const pointValue = props.investmentAmount * cumulativeReturn;

      dataPoints.push({
        date: pointDate.toISOString().split('T')[0],
        value: Math.round(pointValue * 100) / 100
      });
    }

    // Ensure the last point matches the current portfolio value
    if (dataPoints.length > 1) {
      dataPoints[dataPoints.length - 1].value = props.portfolioValue;
    }

    return dataPoints;
  };

  // Scale chart data based on investment amount
  const scaleChartData = (data: any[], investmentAmount: number) => {
    if (!data || data.length === 0 || investmentAmount <= 0) return data;

    // Get the initial value from the first data point
    const initialValue = data[0].value;

    // Scale all values proportionally to the investment amount
    return data.map(point => ({
      date: point.date,
      value: (point.value / initialValue) * investmentAmount
    }));
  };

  // State for time frame selection
  const [timeFrame, setTimeFrame] = useState<'1M' | '3M' | '6M' | '1Y' | '5Y'>('1Y');

  // Update filtered chart data when timeframe changes
  useEffect(() => {
    console.log(`Timeframe changed to ${timeFrame}`);
    // The filteredChartData will be automatically recalculated when timeFrame changes
    // because getFilteredChartData is called during render and depends on timeFrame
  }, [timeFrame]);

  // Function to filter chart data based on selected time frame
  const getFilteredChartData = () => {
    if (!chartData || chartData.length === 0) return [];

    const now = new Date();
    let filterDate = new Date();

    switch (timeFrame) {
      case '1M':
        filterDate.setMonth(now.getMonth() - 1);
        break;
      case '3M':
        filterDate.setMonth(now.getMonth() - 3);
        break;
      case '6M':
        filterDate.setMonth(now.getMonth() - 6);
        break;
      case '1Y':
        filterDate.setFullYear(now.getFullYear() - 1);
        break;
      case '5Y':
        filterDate.setFullYear(now.getFullYear() - 5);
        break;
      default:
        // If we have investment date, use that as the start date
        if (props.investmentDate) {
          filterDate = new Date(props.investmentDate);
        } else {
          // Otherwise use the earliest date in the chart data
          const dates = chartData.map(point => new Date(point.date));
          filterDate = new Date(Math.min(...dates.map(d => d.getTime())));
        }
    }

    // Filter data points after the filter date
    return chartData.filter(point => new Date(point.date) >= filterDate);
  };

  // Get filtered data based on selected time frame
  const filteredChartData = getFilteredChartData();

  // Check if chart data shows negative performance (first value > last value)
  const hasNegativeChartTrend = filteredChartData && filteredChartData.length > 1 &&
    filteredChartData[0].value > filteredChartData[filteredChartData.length - 1].value;

  // Use chart trend for visual consistency if we have data, otherwise use return value
  const isPositive = filteredChartData && filteredChartData.length > 1 ?
    !hasNegativeChartTrend : displayReturn >= 0;

  // Now we can log all values including isPositive
  console.log('Chart display values:', { displayValue, displayReturn, isPositive, chartColor });

  // Calculate start and end values for display (for backtesting)
  const initialInvestment = 10000; // Standard initial investment for backtesting
  const startValue = filteredChartData.length > 0 ? filteredChartData[0].value : (chartData.length > 0 ? chartData[0].value : initialInvestment);
  const endValue = filteredChartData.length > 0 ? filteredChartData[filteredChartData.length - 1].value : (chartData.length > 0 ? chartData[chartData.length - 1].value : initialInvestment);
  const percentageIncrease = startValue > 0 ? ((endValue - startValue) / startValue) * 100 : 0;

  // Calculate annualized return
  const calculateAnnualizedReturn = () => {
    if (filteredChartData.length < 2) return 0;

    const firstDate = new Date(filteredChartData[0].date);
    const lastDate = new Date(filteredChartData[filteredChartData.length - 1].date);
    const yearsDiff = (lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25);

    if (yearsDiff <= 0) return percentageIncrease;

    // Formula: (1 + totalReturn)^(1/years) - 1
    const totalReturn = endValue / startValue;
    const annualizedReturn = (Math.pow(totalReturn, 1 / yearsDiff) - 1) * 100;

    return annualizedReturn;
  };

  const annualizedReturn = calculateAnnualizedReturn();

  // Force re-render when props change
  useEffect(() => {
    console.log('Chart props changed, forcing re-render');
  }, [props.portfolioValue, props.portfolioReturn, props.investmentAmount, props.investmentDate]);

  return (
    <div className="relative h-[480px] w-full overflow-hidden">
      {/* Gradient overlay for the chart - adjusted to be less intrusive */}
      <div className="absolute inset-0 z-10" style={{ background: 'linear-gradient(to top, #0D0D0D 2%, rgba(13, 13, 13, 0.05) 50%, rgba(13, 13, 13, 0.02) 100%)' }}></div>

      {/* Performance metrics */}
      <div className="absolute top-2 left-2 z-20">
        <div className="flex flex-col">
          <div className={`flex items-center ${isPositive ? 'text-[#4EB897]' : 'text-[#E58080]'}`}>
            <span className="text-3xl font-medium">{isPositive ? '+' : '-'}{Math.abs(percentageIncrease).toFixed(2)}%</span>
          </div>
          <div className="flex flex-col mt-1">
            <div className={`flex items-center ${isPositive ? 'text-[#4EB897]/80' : 'text-[#E58080]/80'}`}>
              <span className="text-xs font-medium">{isPositive ? '+' : '-'}{Math.abs(annualizedReturn).toFixed(2)}%</span>
              <span className="text-white/50 text-xs ml-2">Annualized</span>
            </div>
          </div>
        </div>
      </div>

      {/* Timeframe selector */}
      <div className="absolute top-2 right-2 z-20 flex space-x-0.5">
        {['1M', '3M', '6M', '1Y', '5Y'].map((period) => (
          <button
            key={period}
            onClick={() => setTimeFrame(period as any)}
            className={`px-2 py-0.5 text-sm font-medium rounded-md ${period === timeFrame ? 'bg-[#222] text-white' : 'text-white/50 hover:text-white/80'}`}
          >
            {period}
          </button>
        ))}
      </div>

      {/* Loading indicator */}
      {isLoadingChart && (
        <div className="absolute inset-0 bg-black/30 flex items-center justify-center z-30">
          <div className="animate-spin h-8 w-8 border-4 border-white/20 border-t-white/80 rounded-full"></div>
        </div>
      )}

      {/* Error message */}
      {chartError && (
        <div className="absolute inset-0 flex items-center justify-center z-30">
          <div className="bg-red-900/20 text-red-400 px-4 py-2 rounded text-sm">{chartError}</div>
        </div>
      )}

      {/* SVG Chart Path - adjusted to ensure full visibility */}
      <div className="absolute inset-0 pt-16"> {/* Reduced padding-top to show more of the chart */}
        <svg className="w-full h-[calc(100%-16px)]" preserveAspectRatio="none" viewBox="0 0 800 600" style={{ marginBottom: '0px' }}>
          {/* Generate SVG path from real data */}
          {(() => {
            // Generate SVG path from chart data
            const generatePath = () => {
              if (!filteredChartData || filteredChartData.length === 0) {
                // Default flat line at the bottom if no data
                return 'M0,580 L800,580';
              }

              // Find min and max values for scaling
              const values = filteredChartData.map(point => point.value);

              // Find the actual min and max
              const actualMin = Math.min(...values);
              const actualMax = Math.max(...values);

              // Calculate the data range
              const dataRange = actualMax - actualMin;

              // Exaggerate the y-axis by significantly reducing the range we display
              // This will make movements appear much larger

              // Calculate the midpoint of the data
              const midPoint = (actualMax + actualMin) / 2;

              // Create a wider range around the midpoint to avoid excessive zooming
              // Use 85% of the actual data range - ensuring the chart is fully visible with no cutoff
              const scaleFactor = 0.85; // Higher value ensures less zooming and better visibility
              const adjustedRange = dataRange * scaleFactor;

              // Set min and max values based on the midpoint and adjusted range
              // Use a completely balanced approach to ensure full visibility
              // Add extra padding (30%) on both sides to prevent any cutoff
              const minValue = midPoint - (adjustedRange * 0.65);
              const paddedMax = midPoint + (adjustedRange * 0.65);

              // Calculate the display range (this will be much smaller than before, making movements appear larger)
              const valueRange = paddedMax - minValue;

              // Scale function to map values to SVG coordinates (y-axis is inverted in SVG)
              const scaleY = (value: number) => {
                if (valueRange === 0) return 580; // Default to bottom if no range
                // Map to 580 (bottom) to 40 (top) with extra padding
                // Use the full height of the chart by ensuring the lowest value is at the bottom
                // Use full height (580) with extra padding (40px) at top and bottom
                return 580 - ((value - minValue) / valueRange) * 540;
              };

              // Make sure we're using the paddedMax for scaling
              const scaleValue = (value: number) => {
                if (value === actualMax) return scaleY(paddedMax);
                return scaleY(value);
              };

              // Scale X coordinates based on the number of points
              const scaleX = (index: number) => {
                return (index / (filteredChartData.length - 1)) * 800;
              };

              // Generate the path
              let path = '';

              if (filteredChartData.length === 1) {
                // If only one point, draw a horizontal line
                const y = scaleValue(filteredChartData[0].value);
                path = `M0,${y} L800,${y}`;
              } else {
                // Start at the first point
                path = `M${scaleX(0)},${scaleValue(filteredChartData[0].value)}`;

                // Add points with bezier curves for smoothing
                for (let i = 1; i < filteredChartData.length; i++) {
                  const x = scaleX(i);
                  const y = scaleValue(filteredChartData[i].value);
                  const prevX = scaleX(i - 1);
                  const prevY = scaleValue(filteredChartData[i - 1].value);

                  // Calculate control points for the curve
                  const cpX1 = prevX + (x - prevX) / 3;
                  const cpX2 = prevX + 2 * (x - prevX) / 3;

                  // Use cubic bezier curve for smooth transitions
                  path += ` C${cpX1},${prevY} ${cpX2},${y} ${x},${y}`;
                }
              }

              return path;
            };

            // Generate the fill path (same as the line path but closed at the bottom)
            const generateFillPath = (linePath: string) => {
              if (!filteredChartData || filteredChartData.length === 0) {
                return 'M0,580 L800,580 L800,600 L0,600 Z';
              }

              return `${linePath} L800,600 L0,600 Z`;
            };

            const linePath = generatePath();
            const fillPath = generateFillPath(linePath);

            return (
              <>
                {/* Create a smooth line using SVG path with data points */}
                {/* Use the same isPositive value that's used for the percentage display */}
                <path
                  d={linePath}
                  stroke={!isPositive ? "rgba(229, 128, 128, 0.9)" : "rgba(78, 184, 151, 0.9)"}
                  strokeWidth="3"
                  fill="none"
                />
                {/* Add gradient fill underneath the path */}
                <path
                  d={fillPath}
                  fill={!isPositive ? `url(#chartGradientNegative)` : `url(#chartGradientPositive)`}
                  opacity="0.3"
                />
              </>
            );
          })()}

          <defs>
            <linearGradient id="chartGradientPositive" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgba(78, 184, 151, 0.7)" />
              <stop offset="100%" stopColor="rgba(78, 184, 151, 0)" />
            </linearGradient>
            <linearGradient id="chartGradientNegative" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgba(229, 128, 128, 0.7)" />
              <stop offset="100%" stopColor="rgba(229, 128, 128, 0)" />
            </linearGradient>
            <linearGradient id="chartGradientGrey" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgba(128, 128, 128, 0.5)" />
              <stop offset="100%" stopColor="rgba(128, 128, 128, 0)" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
};

const PortfolioManager = () => {
  const [portfolioDescription, setPortfolioDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPortfolio, setGeneratedPortfolio] = useState<Portfolio | null>(null);
  const [portfolioName, setPortfolioName] = useState('');
  // Removed isBacktesting state as it's no longer needed

  // Add responsive hook
  const { classes } = useResponsive();
  const [backtestResults, setBacktestResults] = useState<any>(null);
  const [savedPortfolios, setSavedPortfolios] = useState<SavedPortfolio[]>([]);
  const [isLoadingSaved, setIsLoadingSaved] = useState(false);
  const [activeTab, setActiveTab] = useState("create");
  const [isLoadingPortfolio, setIsLoadingPortfolio] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { trackAction } = useGamification(); // Add mobile detection
  const [clickedTab, setClickedTab] = useState<string | null>(null);
  const [previousTab, setPreviousTab] = useState<string | null>("create");
  const [isBacktestModalOpen, setIsBacktestModalOpen] = useState(false);
  const [portfolioNews, setPortfolioNews] = useState<any[]>([]);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const shuffledNewsRef = useRef<any[]>([]); // Store shuffled news to keep it stable across renders

  // Portfolio investment tracking - initialize with zeros
  const [investmentAmount, setInvestmentAmount] = useState<number>(0);
  const [investmentDate, setInvestmentDate] = useState<string>("");
  const [portfolioValue, setPortfolioValue] = useState<number>(0);
  const [portfolioReturn, setPortfolioReturn] = useState<number>(0);

  // Chat-related state
  const [chatMessage, setChatMessage] = useState('');
  const [isChatProcessing, setIsChatProcessing] = useState(false);
  const [chatResponse, setChatResponse] = useState<string | null>(null);
  const [isChatEnabled, setIsChatEnabled] = useState(true); // Add state to track if chat is enabled
  const [isAnimatingOut, setIsAnimatingOut] = useState(false); // Track animation state
  const chatInputRef = useRef<HTMLTextAreaElement>(null);

  // Get user subscription plan and limits
  const { planType, refreshLimits } = useUserLimits();

  // Log the current plan type for debugging
  useEffect(() => {
    console.log(`Current plan type from useUserLimits: ${planType}`);
  }, [planType]);

  // Function to fetch real stock performance data from Polygon API via our edge function
  const getStockPerformance = (ticker: string): { yearly: number, monthly: number } => {
    // For now, we'll still use the deterministic random as a fallback
    // This will be replaced with real API data in a future update
    const seed = ticker.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const yearly = (Math.sin(seed) * 30) + 15; // Range from -15% to +45%
    const monthly = (Math.cos(seed) * 15) + 5; // Range from -10% to +20%

    return {
      yearly: parseFloat(yearly.toFixed(2)),
      monthly: parseFloat(monthly.toFixed(2))
    };
  };

  // Function to fetch real portfolio performance data
  const fetchPortfolioPerformance = async (portfolioId: string) => {
    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Find the portfolio in our saved portfolios
      const portfolio = savedPortfolios.find(p => p.id === portfolioId);
      if (!portfolio || !portfolio.stocks) return null;

      // Calculate start date for 1 year of data
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1); // 1 year of historical data
      const chartStartDate = startDate.toISOString().split('T')[0];

      // Call the Supabase edge function to get chart data
      const { data, error } = await supabase.functions.invoke('portfolio-chart', {
        body: JSON.stringify({
          action: 'getChartData',
          stocks: portfolio.stocks,
          startDate: chartStartDate,
          endDate: endDate
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) throw error;

      if (data?.chartData && data.chartData.length > 0) {
        // Calculate the yearly return
        const firstValue = data.chartData[0].value;
        const lastValue = data.chartData[data.chartData.length - 1].value;
        const yearlyReturn = ((lastValue - firstValue) / firstValue) * 100;

        return {
          chartData: data.chartData,
          yearlyReturn: parseFloat(yearlyReturn.toFixed(2))
        };
      }

      return null;
    } catch (error) {
      console.error('Error fetching portfolio performance:', error);
      return null;
    }
  };

  // Utility functions removed to clean up unused code

  // Update the getStockLogoUrl function to accept domain from Gemini
  const getStockLogoUrl = (ticker: string, domain?: string): string => {
    // If we already have the domain from Gemini, use it directly
    if (domain) {
      return `https://logo.uplead.com/${domain}`;
    }

    // Map ETFs to their provider's domain if no domain provided
    const etfProviderMap: {[key: string]: string} = {
      'VTI': 'vanguard.com',
      'VXUS': 'vanguard.com',
      'BND': 'vanguard.com',
      'VNQ': 'vanguard.com',
      'SCHD': 'schwab.com',
      'SPY': 'spdrs.com',
      'QQQ': 'invesco.com',
      'ARKK': 'ark-funds.com',
      'ARKQ': 'ark-funds.com',
      'ARKW': 'ark-funds.com',
      'ARKG': 'ark-funds.com',
      'ARKF': 'ark-funds.com',
      'ARKX': 'ark-funds.com',
      'DIA': 'spdrs.com',
      'IEMG': 'ishares.com',
      'VOO': 'vanguard.com',
      'VEA': 'vanguard.com',
      'AGG': 'ishares.com'
    };

    // Fallback company domain mapping for common stocks
    const companyDomainMap: {[key: string]: string} = {
      'AAPL': 'apple.com',
      'MSFT': 'microsoft.com',
      'GOOGL': 'google.com',
      'GOOG': 'google.com',
      'AMZN': 'amazon.com',
      'META': 'meta.com',
      'TSLA': 'tesla.com',
      'NVDA': 'nvidia.com',
      'JPM': 'jpmorganchase.com',
      'BRK.B': 'berkshirehathaway.com',
      'BRK.A': 'berkshirehathaway.com',
      'V': 'visa.com',
      'PYPL': 'paypal.com',
      'JNJ': 'jnj.com',
      'WMT': 'walmart.com',
      'PG': 'pg.com',
      'MA': 'mastercard.com'
    };

    // Determine the domain to use for logo fetching
    let resolvedDomain: string;

    // First check if it's an ETF with a specific provider
    if (etfProviderMap[ticker]) {
      resolvedDomain = etfProviderMap[ticker];
    }
    // Then check if it's a common company with a custom domain
    else if (companyDomainMap[ticker]) {
      resolvedDomain = companyDomainMap[ticker];
    }
    // For everything else, just assume the ticker matches the domain
    else {
      // Clean ticker by removing any dots (for stock classes) and converting to lowercase
      resolvedDomain = ticker.replace(/\./g, '').toLowerCase() + '.com';
    }

    // Return the URL using the uplead.com API
    return `https://logo.uplead.com/${resolvedDomain}`;
  };

  // Load saved portfolios and refresh limits when the component mounts
  useEffect(() => {
    // Refresh user limits to ensure we have the correct plan type
    refreshLimits().then(result => {
      console.log('Refreshed user limits:', result);
    }).catch(error => {
      console.error('Error refreshing user limits:', error);
    });

    // Load saved portfolios
    loadSavedPortfolios();

    // Log mobile status for debugging
    console.log('PortfolioManager mounted, isMobile:', isMobile);

    // Empty dependency array to ensure this only runs once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Add a specific effect for mobile devices
  useEffect(() => {
    if (isMobile) {
      console.log('Mobile device detected - setting up mobile-specific behavior');
      // Any mobile-specific setup can go here
    }
  }, [isMobile]);

  // Modify the generatePortfolio function to get company domains from Gemini
  const generatePortfolio = async () => {
    // Add debug toast to confirm the function is being called
    console.log('Generate portfolio function called');
    console.log('Is mobile device:', isMobile);

    // Prevent multiple calls
    if (isGenerating) {
      console.log('Already generating, ignoring duplicate call');
      return;
    }

    // Show a toast to confirm the button was clicked (helpful for debugging)
    toast({
      title: "Generating Portfolio",
      description: "Starting portfolio generation...",
    });

    if (!portfolioDescription.trim()) {
      toast({
        title: "Description Required",
        description: "Please describe the portfolio you want to create.",
        variant: "destructive"
      });
      return;
    }

    // Check if user has reached their portfolio limit based on current plan type
    // We'll use the current planType from state to avoid potential refresh loops
    const portfolioLimit = planType === PLAN_TYPES.pro ? 3 : 1;
    console.log(`Portfolio limit check: ${savedPortfolios.length}/${portfolioLimit} (${planType} plan)`);

    if (savedPortfolios.length >= portfolioLimit) {
      toast({
        title: "Portfolio Limit Reached",
        description: `You have reached your limit of ${portfolioLimit} portfolio${portfolioLimit > 1 ? 's' : ''}. Please upgrade to the Pro plan for up to 3 portfolios.`,
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Call the Supabase edge function to generate a portfolio using Gemini
      const { data, error } = await supabase.functions.invoke('portfolio-manager', {
        body: JSON.stringify({
          action: 'generate',
          description: portfolioDescription,
          includeDomains: true, // Ask Gemini to include company domains for logos
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) throw error;

      if (data && data.portfolio) {
        // Add logos to each stock using domains from Gemini if available
        const portfolioWithLogos = {
          ...data.portfolio,
          stocks: data.portfolio.stocks.map((stock: PortfolioStock) => ({
            ...stock,
            logo: getStockLogoUrl(stock.ticker, stock.domain) // Pass domain if provided by Gemini
          }))
        };

        setGeneratedPortfolio(portfolioWithLogos);
        setPortfolioName(data.portfolio.name || 'My Portfolio');

        // Trigger gamification for portfolio creation
        trackAction('portfolio_created');

        // Automatically save the portfolio
        try {
          // Get a fresh session token to ensure it's valid
          const { data: freshSessionData, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error('Error getting fresh session for auto-save:', sessionError);
            throw new Error('Authentication error');
          }

          if (!freshSessionData?.session) {
            console.error('No valid session found for auto-save');
            throw new Error('Not authenticated');
          }

          // Call the Supabase edge function to save the portfolio
          const { data: saveData, error: saveError } = await supabase.functions.invoke('portfolio-manager', {
            body: JSON.stringify({
              action: 'save',
              portfolio: {
                name: data.portfolio.name || 'My Portfolio',
                description: portfolioDescription,
                stocks: data.portfolio.stocks,
                investmentAmount: investmentAmount > 0 ? investmentAmount : undefined,
                investmentDate: investmentDate ? investmentDate : undefined
              }
            }),
            headers: {
              Authorization: `Bearer ${freshSessionData.session.access_token}`
            }
          });

          if (saveError) {
            console.error('Error auto-saving portfolio:', saveError);

            // Check if this is a portfolio limit error
            if (saveData?.limitReached) {
              toast({
                title: "Portfolio Limit Reached",
                description: saveData.error || `You have reached your portfolio limit. Please upgrade to the Pro plan for more portfolios.`,
                variant: "destructive"
              });
            } else {
              toast({
                title: "Save Failed",
                description: "Failed to save portfolio. Please try again.",
                variant: "destructive"
              });
            }
          } else {
            // Reload the saved portfolios
            loadSavedPortfolios();

            // Show a subtle toast notification
      toast({
        title: "Portfolio Saved",
              description: "Your portfolio has been automatically saved."
            });
          }
        } catch (saveError) {
          console.error('Error auto-saving portfolio:', saveError);
          toast({
            title: "Save Failed",
            description: "Failed to save portfolio. Please try again.",
            variant: "destructive"
          });
        }
      } else {
        throw new Error('Failed to generate portfolio');
      }
    } catch (error) {
      console.error('Error generating portfolio:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate portfolio. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Auto-save functionality is now integrated into the generatePortfolio function

  const loadSavedPortfolios = async () => {
    setIsLoadingSaved(true);
    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Call the Supabase edge function to list portfolios
      const { data, error } = await supabase.functions.invoke('portfolio-manager', {
        body: JSON.stringify({
          action: 'list'
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) throw error;

      setSavedPortfolios(data?.portfolios || []);
    } catch (error) {
      console.error('Error loading portfolios:', error);
      toast({
        title: "Loading Failed",
        description: "Failed to load saved portfolios.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingSaved(false);
    }
  };

  // Update the loadPortfolio function to handle domains from Gemini
  const loadPortfolio = async (portfolioId: string) => {
    setIsLoadingPortfolio(true);
    try {
      // Switch to the create tab immediately
      setActiveTab("create");

      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Call the Supabase edge function to get a portfolio
      const { data, error } = await supabase.functions.invoke('portfolio-manager', {
        body: JSON.stringify({
          action: 'get',
          portfolio: { id: portfolioId }
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) throw error;

      if (data?.portfolio) {
        // Clear any previous backtest results
        setBacktestResults(null);

        // Format the portfolio data
        const formattedStocks = data.portfolio.stocks.map((stock: any) => ({
          ticker: stock.ticker,
          allocation: stock.allocation,
          name: stock.name || stock.ticker,
          domain: stock.domain, // Store domain if available
          logo: getStockLogoUrl(stock.ticker, stock.domain)
        }));

        setGeneratedPortfolio({
          name: data.portfolio.name,
          description: data.portfolio.description,
          stocks: formattedStocks
        });
        setPortfolioName(data.portfolio.name);
        setPortfolioDescription(data.portfolio.description);

        // Load investment details if available
        if (data.portfolio.investment_amount) {
          setInvestmentAmount(data.portfolio.investment_amount);
        }

        if (data.portfolio.investment_date) {
          setInvestmentDate(data.portfolio.investment_date);
        }

        // Calculate portfolio performance if we have investment details
        if (data.portfolio.investment_amount && data.portfolio.investment_date) {
          calculatePortfolioPerformance(data.portfolio.investment_amount, data.portfolio.investment_date);
        }

        toast({
          title: "Portfolio Loaded",
          description: "Portfolio loaded successfully."
        });
      }
    } catch (error) {
      console.error('Error loading portfolio:', error);
      toast({
        title: "Loading Failed",
        description: "Failed to load portfolio details.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingPortfolio(false);
    }
  };

  // Removed runBacktest function as it's no longer needed

  // Function to load news for the current portfolio - with caching to prevent unnecessary refreshes
  const loadPortfolioNews = async () => {
    if (!generatedPortfolio) return;

    // If we already have news, don't reload
    if (portfolioNews.length > 0) {
      console.log('Using cached news data');
      return;
    }

    setIsLoadingNews(true);
    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Use a fixed ID for generated portfolios to avoid creating new ones
      let portfolioId = 'generated-portfolio';

      // Check if this portfolio is already saved
      const savedPortfolio = savedPortfolios.find(p =>
        p.name === portfolioName &&
        p.description === portfolioDescription
      );

      if (savedPortfolio) {
        portfolioId = savedPortfolio.id;
      }

      // Ensure we have at least 5 stock tickers for news by duplicating if needed
      const getEnoughStockTickers = (): string[] => {
        if (!generatedPortfolio || !generatedPortfolio.stocks || generatedPortfolio.stocks.length === 0) return [];

        // Extract tickers from stocks
        const tickers = generatedPortfolio.stocks.map(stock => stock.ticker);

        // If we have 5 or more unique tickers, return them
        if (tickers.length >= 5) return tickers;

        // Otherwise, duplicate tickers until we have at least 5
        const duplicatedTickers: string[] = [...tickers];
        let currentIndex = 0;

        while (duplicatedTickers.length < 5) {
          duplicatedTickers.push(tickers[currentIndex % tickers.length]);
          currentIndex++;
        }

        return duplicatedTickers;
      };

      // Get the stock tickers with duplicates if needed
      const stockTickers = getEnoughStockTickers();

      // Now fetch news for this portfolio
      const { data: newsData, error: newsError } = await supabase.functions.invoke('portfolio-news', {
        body: JSON.stringify({
          action: 'getPortfolioNews',
          portfolioId,
          stockTickers // Pass the potentially duplicated tickers
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (newsError) throw newsError;

      if (newsData?.news) {
        // Shuffle the news once when loading, not on every render
        shuffledNewsRef.current = [...newsData.news].sort(() => Math.random() - 0.5);
        setPortfolioNews(shuffledNewsRef.current);
      }
    } catch (error) {
      console.error('Error loading portfolio news:', error);
      // Don't show error toast to avoid disrupting the user experience
    } finally {
      setIsLoadingNews(false);
    }
  };

  // Function to track tab click animation
  const handleTabClick = (value: string) => {
    setClickedTab(value);
    setTimeout(() => setClickedTab(null), 300);

    // Track previous tab for animation direction
    setPreviousTab(activeTab);
  };

  // Function to get animation class based on tab transition
  const getAnimationClass = (tabValue: string) => {
    if (previousTab === 'create' && activeTab === 'saved' && tabValue === 'saved') {
      return 'animate-in-right';
    }
    if (previousTab === 'saved' && activeTab === 'create' && tabValue === 'create') {
      return 'animate-in-left';
    }
    return '';
  };

  // Utility functions for stock information

  // Add a function to get sector for a stock (mock data for now)
  const getStockSector = (ticker: string): string => {
    const sectors: {[key: string]: string} = {
      'AAPL': 'Technology',
      'MSFT': 'Technology',
      'GOOGL': 'Technology',
      'GOOG': 'Technology',
      'AMZN': 'Consumer Cyclical',
      'META': 'Technology',
      'TSLA': 'Automotive',
      'NVDA': 'Technology',
      'BRK.B': 'Financial Services',
      'JPM': 'Financial Services',
      'V': 'Financial Services',
      'WMT': 'Consumer Defensive',
      'JNJ': 'Healthcare',
      'PG': 'Consumer Defensive',
      'XOM': 'Energy',
      'VTI': 'ETF - Total Market',
      'VXUS': 'ETF - International',
      'BND': 'ETF - Bonds',
      'VNQ': 'ETF - Real Estate',
      'SCHD': 'ETF - Dividends',
      'SPY': 'ETF - S&P 500',
      'QQQ': 'ETF - Technology',
      'DIA': 'ETF - Dow Jones',
      'SMCI': 'Technology',
    };

    return sectors[ticker] || 'Other';
  };

  // Create a separate component for portfolio cards to fix the React Hooks error
  // Update the Portfolio type to include id and five_year_performance
  interface PortfolioWithId extends Portfolio {
    id: string;
    five_year_performance?: number;
  }

  const PortfolioCard = ({ portfolio, loadPortfolio }: { portfolio: PortfolioWithId, loadPortfolio: (id: string) => void }) => {
    const [portfolioPerformance, setPortfolioPerformance] = useState<number | null>(null);
    const [isLoadingPerformance, setIsLoadingPerformance] = useState(false);

    // State for chart data
    const [chartData, setChartData] = useState<{date: string, value: number}[]>([]);

    // Use the saved five_year_performance value if available
    useEffect(() => {
      const getPortfolioPerformance = async () => {
        setIsLoadingPerformance(true);
        try {
          // Check if we already have the five_year_performance value from Supabase
          if (portfolio.five_year_performance !== undefined && portfolio.five_year_performance !== null) {
            console.log(`Using saved 5-year performance: ${portfolio.five_year_performance}%`);
            setPortfolioPerformance(portfolio.five_year_performance);

            // Generate a simple chart based on the performance value
            generateSimpleChart(portfolio.five_year_performance);
          } else {
            console.log('No saved performance value, calculating from portfolio stocks');

            // If we don't have a saved performance value but we have stocks,
            // we'll use the fallback mechanism which is consistent with the main portfolio view
            if (portfolio.stocks && portfolio.stocks.length > 0) {
              // Use the same deterministic fallback as in the main portfolio view
              const tickerString = portfolio.stocks.map(s => s.ticker).join('');
              const hash = tickerString.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
              const fallbackPerformance = Math.round(((Math.sin(hash) * 50) + 30) * 100) / 100;

              console.log(`Using calculated fallback performance: ${fallbackPerformance}%`);
              setPortfolioPerformance(fallbackPerformance);
              generateSimpleChart(fallbackPerformance);
            } else {
              console.log('No stocks available, using null performance');
              setPortfolioPerformance(null);
              setChartData([]);
            }
          }
        } catch (error) {
          console.error('Error processing portfolio performance:', error);
          setPortfolioPerformance(null);
          setChartData([]);
        } finally {
          setIsLoadingPerformance(false);
        }
      };

      // Generate a simple chart based on the performance value
      const generateSimpleChart = (performance: number) => {
        // Create a simple chart with 20 points that trends up or down based on the performance
        const chartData = [];
        const now = new Date();
        const fiveYearsAgo = new Date();
        fiveYearsAgo.setFullYear(now.getFullYear() - 5);

        // Calculate the time interval between points
        const timeInterval = (now.getTime() - fiveYearsAgo.getTime()) / 19; // 20 points - 1 interval

        // Calculate the value change per interval
        const startValue = 100;
        const endValue = startValue * (1 + (performance / 100));
        const valueStep = (endValue - startValue) / 19;

        // Generate the points with some random variation
        for (let i = 0; i < 20; i++) {
          const date = new Date(fiveYearsAgo.getTime() + (i * timeInterval));
          const baseValue = startValue + (i * valueStep);

          // Add some random variation (±5% of the step size)
          const variation = (Math.random() - 0.5) * 0.1 * Math.abs(valueStep);
          const value = baseValue + variation;

          chartData.push({
            date: date.toISOString().split('T')[0],
            value: Math.max(0, value) // Ensure value is not negative
          });
        }

        // Ensure the last point matches the expected performance
        chartData[chartData.length - 1].value = endValue;

        setChartData(chartData);
      };

      getPortfolioPerformance();
    }, [portfolio.id, portfolio.five_year_performance]);

    // Determine if we should show real performance or fallback
    const showRealPerformance = portfolioPerformance !== null;

    // For fallback, use a deterministic value based on portfolio stocks
    // Use the same calculation as in the main portfolio view and the edge function
    const fallbackPerformance = portfolio.stocks && portfolio.stocks.length > 0
      ? (Math.sin(portfolio.stocks.map(s => s.ticker).join('').split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) * 50) + 30
      : (Math.sin(portfolio.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) * 20) + 10;

    // Use real performance if available, otherwise fallback
    const displayPerformance = showRealPerformance ? portfolioPerformance : fallbackPerformance;
    const isPositivePerformance = displayPerformance >= 0;

    return (
      <div className="bg-gradient-to-b from-[#0D0D0D] to-[#090909] shadow-[0_4px_20px_rgba(0,0,0,0.25),inset_0_1px_0_rgba(255,255,255,0.06)] border border-[#222224]/30 rounded-xl overflow-hidden hover:shadow-[0_6px_24px_rgba(0,0,0,0.3),inset_0_1px_0_rgba(255,255,255,0.08)] transition-all duration-300">
        <div className="p-4 pb-2 border-b border-[#1A1A1A]/20 bg-gradient-to-r from-[#0F0F0F] to-[#0C0C0C]">
          <h3 className="text-lg font-medium text-white/95 tracking-tight">{portfolio.name || 'Untitled Portfolio'}</h3>
        </div>

        <div className="p-3">
          {portfolio.stocks && Array.isArray(portfolio.stocks) && portfolio.stocks.length > 0 ? (
            <div className="space-y-2">
              {/* Portfolio chart - adjusted height for better visibility */}
              <div className="h-[120px] mb-3 relative">
                <MiniPortfolioChart
                  performance={displayPerformance}
                  chartData={chartData}
                  showBadge={false}
                  height={120}
                />
                {/* Performance badge */}
                <div className="absolute top-2 right-2 bg-[#0D0D0D] backdrop-blur-sm text-sm font-medium px-2.5 py-0.5 rounded-md border border-[#333]/40 shadow-[0_1px_3px_rgba(0,0,0,0.3)]"
                  style={{
                    color: isPositivePerformance ? 'rgba(78, 184, 151, 1)' : 'rgba(229, 128, 128, 1)',
                    background: 'linear-gradient(to bottom, rgba(20,20,20,0.95), rgba(10,10,10,0.95))'
                  }}>
                  <span className="font-bold tracking-wide">{isPositivePerformance ? '+' : ''}{displayPerformance.toFixed(1)}%</span>
                </div>
              </div>

              {/* Portfolio performance indicator */}
              <div className="flex justify-between items-center mb-3 pb-2 border-b border-[#1A1A1A]/20">
                <span className="text-xs text-white/70">5-Year Performance:</span>
                {isLoadingPerformance ? (
                  <div className="h-4 w-16 bg-white/10 animate-pulse rounded"></div>
                ) : (
                  <span className={`text-sm font-medium ${isPositivePerformance ? 'text-[#4EB897]' : 'text-[#E58080]'}`}>
                    {isPositivePerformance ? '+' : ''}{displayPerformance.toFixed(2)}%
                    {!showRealPerformance && <span className="text-xs text-white/30 ml-1">(est.)</span>}
                  </span>
                )}
              </div>

              {portfolio.stocks.map((stock, index) => {
                // Get performance data
                const performance = getStockPerformance(stock.ticker);
                const isPositive = performance.yearly > 0;
                // Clean color palette - only approved colors
                const stockColors = [
                  '#10B981CC', '#059669CC', '#34D399CC', '#6EE7B7CC', '#A7F3D0CC',
                  '#D1FAE5CC', '#9CA3AFCC', '#6B7280CC', '#4B5563CC', '#374151CC',
                  '#F3F4F6CC', '#E5E7EBCC', '#D1D5DBCC', '#9CA3AFCC', '#6B7280CC', '#4B5563CC'
                ];
                const stockColor = stockColors[index % stockColors.length];

                return (
                  <div
                    key={stock.ticker}
                    className="flex items-center justify-between py-2 border-b border-[#1A1A1A]/10 last:border-0"
                  >
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: stockColor }}
                      ></div>
                      <span className="text-xs font-medium text-white/90">{stock.ticker}</span>
                      <span className="text-xs text-white/50 ml-2 max-w-[120px] truncate">{stock.name}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="text-right font-medium text-white/80 text-xs">
                        {stock.allocation}%
                      </div>
                      <div className={`text-right font-medium text-xs ${isPositive ? 'text-[#4EB897]' : 'text-[#E58080]'}`}>
                        {isPositive ? '+' : ''}{performance.yearly.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="flex flex-col h-full">
              <div className="flex-1 h-[140px] relative">
                <MiniPortfolioChart
                  performance={displayPerformance}
                  chartData={chartData}
                />
              </div>
            </div>
          )}
        </div>

        <div className="px-3 py-3 bg-gradient-to-b from-[#0A0A0A] to-[#070707] border-t border-[#1A1A1A]/20">
          <button
            onClick={() => loadPortfolio(portfolio.id)}
            className="w-full bg-gradient-to-b from-[#151515] to-[#0D0D0D] text-white/95 border border-[#232323]/60 rounded-md py-2 text-xs font-medium shadow-[inset_0_1px_0_0_rgba(255,255,255,0.03),inset_0_-1px_3px_rgba(0,0,0,0.2),0_1px_2px_rgba(0,0,0,0.3)] hover:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05),inset_0_-1px_2px_rgba(0,0,0,0.3),0_2px_4px_rgba(0,0,0,0.4)] hover:bg-gradient-to-b hover:from-[#181818] hover:to-[#101010] transition-all duration-200 flex items-center justify-center group"
          >
            <ChevronRight className="w-3.5 h-3.5 mr-1.5 opacity-80 group-hover:translate-x-0.5 transition-transform duration-200" />
            Load Portfolio
          </button>
        </div>
      </div>
    );
  };



  // Move the AllocationChart component inside the PortfolioManager to access functions directly
  const AllocationChart = ({ stocks }: { stocks: PortfolioStock[] }) => {
    // Calculate total allocation
    const total = stocks.reduce((sum, stock) => sum + stock.allocation, 0);

    // We'll show individual stocks directly rather than grouping by sector
    const stockAllocation = stocks.map(stock => ({
      ticker: stock.ticker,
      name: stock.name || stock.ticker,
      allocation: stock.allocation,
      domain: stock.domain,
      sector: getStockSector(stock.ticker),
      performance: getStockPerformance(stock.ticker).yearly
    })).sort((a, b) => b.allocation - a.allocation);

    // Clean color palette - only white, green, red, black, and grey tones
    // Each color maintains visual hierarchy while staying within the approved palette
    const stockColors = [
      '#10B981CC',  // Green primary
      '#059669CC',  // Green dark
      '#34D399CC',  // Green light
      '#6EE7B7CC',  // Green lighter
      '#A7F3D0CC',  // Green pale
      '#D1FAE5CC',  // Green very pale
      '#9CA3AFCC',  // Grey medium
      '#6B7280CC',  // Grey dark
      '#4B5563CC',  // Grey darker
      '#374151CC',  // Grey darkest
      '#F3F4F6CC',  // Grey light
      '#E5E7EBCC',  // Grey lighter
      '#D1D5DBCC',  // Grey pale
      '#9CA3AFCC',  // Grey medium (repeat for more options)
      '#6B7280CC',  // Grey dark (repeat)
      '#4B5563CC',  // Grey darker (repeat)
    ];

    // Get a unique color for each stock based on its position in the array
    const getStockColor = (index: number) => {
      return stockColors[index % stockColors.length];
    };

    // Calculate SVG pie chart segments
    let cumulativePercentage = 0;
    const pieSegments = stockAllocation.map((stock, index) => {
      const percentage = (stock.allocation / total) * 100;
      const startAngle = cumulativePercentage * 3.6; // 3.6 degrees per percentage point (360/100)
      cumulativePercentage += percentage;
      const endAngle = cumulativePercentage * 3.6;

      // Calculate SVG arc path
      const startX = 50 + 40 * Math.cos((startAngle - 90) * Math.PI / 180);
      const startY = 50 + 40 * Math.sin((startAngle - 90) * Math.PI / 180);
      const endX = 50 + 40 * Math.cos((endAngle - 90) * Math.PI / 180);
      const endY = 50 + 40 * Math.sin((endAngle - 90) * Math.PI / 180);

      const largeArcFlag = percentage > 50 ? 1 : 0;

      return {
        ...stock,
        percentage,
        color: getStockColor(index),
        path: `M 50 50 L ${startX} ${startY} A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY} Z`
      };
    });

    return (
      <div className="flex flex-col h-full">
        <h3 className="text-white/95 text-base font-medium mb-4 border-b border-white/10 pb-2">Portfolio Allocation</h3>

        <div className="flex flex-col items-center gap-4 flex-1">
          {/* Pie chart centered at the top */}
          <div className="relative w-[180px] h-[180px] mx-auto">
            <div className="absolute inset-0 bg-gradient-to-br from-[#141414]/50 to-[#0A0A0A]/80 rounded-full backdrop-blur-sm"></div>
            <svg viewBox="0 0 100 100" className="drop-shadow-lg relative z-10">
              {pieSegments.map((segment) => (
                <path
                  key={segment.ticker}
                  d={segment.path}
                  fill={segment.color}
                  opacity="0.85"
                  className="hover:opacity-100 transition-opacity cursor-pointer hover:drop-shadow-lg"
                  data-ticker={segment.ticker}
                />
              ))}
              {/* Center hole with subtle glassmorphism */}
              <circle cx="50" cy="50" r="25" fill="url(#centerGradient)" />

              {/* Total value */}
              <text x="50" y="46" textAnchor="middle" fill="white" fontSize="6" fontWeight="medium">
                Total
              </text>
              <text x="50" y="56" textAnchor="middle" fill="white" fontSize="8" fontWeight="bold">
                100%
              </text>

              {/* Define gradients */}
              <defs>
                <linearGradient id="centerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#141414" />
                  <stop offset="100%" stopColor="#0A0A0A" />
                </linearGradient>
              </defs>
            </svg>
          </div>

          {/* Stock list below the pie chart - more compact */}
          <div className="w-full overflow-auto backdrop-blur-sm bg-[#0F0F0F]/30 rounded-lg border border-white/5 p-3">
            <div className="mb-2 pb-1 border-b border-white/10 flex items-center text-xs text-white/60 font-medium">
              <div className="w-20 pl-2">Ticker</div>
              <div className="flex-1 pl-2">Name</div>
              <div className="w-20 text-right pr-3">Alloc.</div>
              <div className="w-20 text-right pr-2">Perf.</div>
            </div>

            <div className="space-y-1">
              {pieSegments.map((segment) => {
                const isPositive = segment.performance > 0;

                return (
                  <div
                    key={segment.ticker}
                    className="flex items-center py-1.5 text-xs border-b border-white/5 hover:bg-white/5 rounded-md px-2 transition-colors"
                  >
                    <div className="w-20 flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2 shadow-sm"
                        style={{ backgroundColor: segment.color }}
                      ></div>
                      <span className="text-white/95 font-medium">{segment.ticker}</span>
                    </div>
                    <div className="flex-1 text-white/80 text-xs pl-2 truncate">{segment.name}</div>
                    <div className="w-20 text-right text-white/95 font-medium pr-3">{segment.percentage.toFixed(1)}%</div>
                    <div className={`w-20 text-right font-medium pr-2 ${isPositive ? 'text-[#4EB897]' : 'text-[#E58080]'}`}>
                      {isPositive ? '+' : ''}{segment.performance.toFixed(1)}%
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Calculate portfolio performance based on investment amount and date
  const calculatePortfolioPerformance = (amount: number, date?: string) => {
    console.log('calculatePortfolioPerformance called with:', { amount, date, hasPortfolio: !!generatedPortfolio });

    if (!amount || !generatedPortfolio) {
      console.log('Missing required data, resetting values to zero');
      // Reset values if no investment info is provided
      setPortfolioValue(0);
      setPortfolioReturn(0);
      return;
    }

    // Default to today if no date is provided
    const investmentDate = date || new Date().toISOString().split('T')[0];
    console.log(`Using investment date: ${investmentDate}`);

    // Update the investment date state if it's not already set
    if (!date && investmentDate) {
      setInvestmentDate(investmentDate);
    }

    try {
      // Parse the investment date
      const investDate = new Date(investmentDate);
      const currentDate = new Date();
      console.log('Dates for calculation:', {
        investDate: investDate.toISOString(),
        currentDate: currentDate.toISOString()
      });

      // Calculate months between investment date and now
      const monthsDiff = (currentDate.getFullYear() - investDate.getFullYear()) * 12 +
                         (currentDate.getMonth() - investDate.getMonth());
      console.log(`Months difference: ${monthsDiff}`);

      // If the date is in the future or less than a month ago, use the initial investment amount
      if (monthsDiff < 0) {
        console.log('Future date detected, using initial amount');
        // Future date - just use the investment amount
        setPortfolioValue(amount);
        setPortfolioReturn(0);
        return;
      } else if (monthsDiff < 1) {
        console.log('Recent investment (< 1 month), applying minimal growth');
        // Less than a month - minimal growth
        const smallGrowth = amount * 1.001; // 0.1% growth for very recent investments
        setPortfolioValue(Math.round(smallGrowth * 100) / 100);
        setPortfolioReturn(0.1);
        return;
      }

      // Calculate a simulated return based on the portfolio composition
      // This is a simplified model - in a real app, you'd use actual market data
      let totalReturn = 0;
      console.log('Calculating returns for stocks:', generatedPortfolio.stocks.map(s => s.ticker).join(', '));

      // Calculate weighted return based on stock allocations
      generatedPortfolio.stocks.forEach(stock => {
        // Get a simulated performance for this stock
        const performance = getStockPerformance(stock.ticker);

        // Convert yearly performance to the period since investment
        const periodReturn = (performance.yearly / 12) * monthsDiff;

        // Add weighted contribution to total return
        const contribution = (periodReturn * (stock.allocation / 100));
        totalReturn += contribution;
        console.log(`${stock.ticker}: ${performance.yearly}% yearly → ${periodReturn.toFixed(2)}% for period → ${contribution.toFixed(2)}% weighted contribution`);
      });

      // Calculate current portfolio value
      const currentValue = amount * (1 + (totalReturn / 100));
      console.log(`Total return: ${totalReturn.toFixed(2)}%, Current value: $${currentValue.toFixed(2)}`);

      // Update state
      const roundedValue = Math.round(currentValue * 100) / 100;
      const roundedReturn = Math.round(totalReturn * 100) / 100;
      console.log(`Setting portfolio value to $${roundedValue} and return to ${roundedReturn}%`);
      setPortfolioValue(roundedValue);
      setPortfolioReturn(roundedReturn);

      console.log(`Portfolio performance updated: $${amount} invested on ${date} is now worth $${roundedValue} (${roundedReturn}% return)`);
    } catch (error) {
      console.error('Error calculating portfolio performance:', error);
    }
  };

  // Load news when portfolio is generated or loaded - but only once
  useEffect(() => {
    if (generatedPortfolio) {
      // Only load news if we don't already have news for this portfolio
      if (portfolioNews.length === 0) {
        loadPortfolioNews();
      } else if (shuffledNewsRef.current.length === 0) {
        // If we already have news but shuffledNewsRef is empty, initialize it
        shuffledNewsRef.current = [...portfolioNews].sort(() => Math.random() - 0.5);
      }

      // If we have investment info, calculate performance
      if (investmentAmount > 0 && investmentDate) {
        calculatePortfolioPerformance(investmentAmount, investmentDate);
      }
    }
  }, [generatedPortfolio, portfolioNews.length]);

  // Recalculate portfolio performance whenever investment info changes
  useEffect(() => {
    console.log('Investment info changed:', { investmentAmount, investmentDate });
    if (generatedPortfolio && investmentAmount > 0 && investmentDate) {
      console.log('Recalculating portfolio performance due to investment info change');
      calculatePortfolioPerformance(investmentAmount, investmentDate);
    }
  }, [investmentAmount, investmentDate]);

  // Function to process chat messages with Gemini
  const processChatMessage = async () => {
    if (!chatMessage.trim() || !generatedPortfolio) return;

    setIsChatProcessing(true);
    setChatResponse(null);

    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Find the portfolio ID if it's a saved portfolio
      const savedPortfolio = savedPortfolios.find(p => p.name === portfolioName);
      const portfolioId = savedPortfolio?.id;

      // Create a prompt for Gemini that includes the current portfolio details
      const portfolioDetails = {
        id: portfolioId, // Include the ID if it's a saved portfolio
        name: portfolioName,
        description: portfolioDescription,
        investmentAmount: investmentAmount,
        investmentDate: investmentDate,
        currentValue: portfolioValue,
        currentReturn: portfolioReturn,
        stocks: generatedPortfolio.stocks.map(stock => ({
          ticker: stock.ticker,
          name: stock.name || stock.ticker,
          allocation: stock.allocation
        }))
      };

      // Call the Supabase edge function to process the chat message
      const { data, error } = await supabase.functions.invoke('portfolio-manager', {
        body: JSON.stringify({
          action: 'chat',
          message: chatMessage,
          portfolio: portfolioDetails
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) throw error;

      if (data?.response) {
        setChatResponse(data.response.message);

        // Check for investment information in the response
        if (data.response.investmentInfo) {
          const { amount, date } = data.response.investmentInfo;

          // Show loading indicator while updating portfolio performance
          setIsLoadingPortfolio(true);

          console.log('Investment info received from Gemini:', { amount, date });

          if (amount && amount > 0) {
            console.log(`Setting investment amount to $${amount}`);
            setInvestmentAmount(amount);

            toast({
              title: "Investment Updated",
              description: `Investment amount set to $${amount.toLocaleString()}`
            });
          }

          if (date) {
            // Format the date to ISO string (YYYY-MM-DD)
            const formattedDate = new Date(date).toISOString().split('T')[0];
            console.log(`Setting investment date to ${formattedDate}`);
            setInvestmentDate(formattedDate);

            toast({
              title: "Investment Date Updated",
              description: `Investment date set to ${new Date(formattedDate).toLocaleDateString()}`
            });
          }

          // Force a delay to ensure state updates have propagated
          setTimeout(() => {
            // Get the current values from state (they may have been updated by now)
            const currentAmount = amount || investmentAmount;
            const currentDate = date || investmentDate;

            console.log('Calculating portfolio performance with:', {
              amount: currentAmount,
              date: currentDate
            });

            // Calculate new portfolio value and return based on investment info
            if (currentAmount > 0 && currentDate) {
              calculatePortfolioPerformance(currentAmount, currentDate);

              // After a short delay, reload the saved portfolios to get updated investment details
              setTimeout(() => {
                console.log('Reloading saved portfolios to get updated investment details');
                loadSavedPortfolios();
              }, 1000);
            } else {
              console.error('Missing investment information for calculation');
              toast({
                title: "Investment Error",
                description: "Could not process investment information. Please try again.",
                variant: "destructive"
              });
            }

            // Save investment details to Supabase
            // First check if this is a saved portfolio
            const savedPortfolio = savedPortfolios.find(p => p.name === portfolioName);

            if (savedPortfolio) {
              console.log('Saving investment details to Supabase for existing portfolio:', savedPortfolio.id);
              // Update the existing portfolio with investment details
              supabase.functions.invoke('portfolio-manager', {
                body: JSON.stringify({
                  action: 'save',
          portfolio: {
                    id: savedPortfolio.id,
            name: portfolioName,
            description: portfolioDescription,
                    stocks: generatedPortfolio.stocks,
                    investmentAmount: amount || investmentAmount,
                    investmentDate: date || investmentDate
          }
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
              }).then((response) => {
                const { error: saveError } = response;

                if (saveError) {
                  console.error('Error saving investment details to existing portfolio:', saveError);
                  toast({
                    title: "Update Failed",
                    description: "Failed to update portfolio with investment details. Please try again.",
                    variant: "destructive"
                  });
                } else {
                  console.log('Investment details saved to Supabase for existing portfolio', response);
                  // Refresh the list of saved portfolios to get updated investment details
                  loadSavedPortfolios();
                  toast({
                    title: "Portfolio Updated",
                    description: "Your portfolio has been updated with investment details."
                  });
                }
              }).catch(error => {
                console.error('Error saving investment details to existing portfolio:', error);
                toast({
                  title: "Update Failed",
                  description: "Failed to update portfolio with investment details. Please try again.",
                  variant: "destructive"
                });
              });
            } else {
              // This is a generated portfolio that hasn't been saved yet
              // Save it as a new portfolio with the investment details
              console.log('Saving new portfolio with investment details');
              supabase.functions.invoke('portfolio-manager', {
                body: JSON.stringify({
                  action: 'save',
                  portfolio: {
                    name: portfolioName,
                    description: portfolioDescription,
                    stocks: generatedPortfolio.stocks,
                    investmentAmount: amount || investmentAmount,
                    investmentDate: date || investmentDate
                  }
                }),
                headers: sessionData?.session ? {
                  Authorization: `Bearer ${sessionData.session.access_token}`
                } : undefined
              }).then((response) => {
                const { data: saveData, error: saveError } = response;

                if (saveError) {
                  console.error('Error saving new portfolio with investment details:', saveError);

                  // Check if this is a portfolio limit error
                  if (saveData?.limitReached) {
                    toast({
                      title: "Portfolio Limit Reached",
                      description: saveData.error || `You have reached your portfolio limit. Please upgrade to the Pro plan for more portfolios.`,
                      variant: "destructive"
                    });
                  } else {
                    toast({
                      title: "Save Failed",
                      description: "Failed to save portfolio. Please try again.",
                      variant: "destructive"
                    });
                  }
                } else {
                  console.log('New portfolio saved with investment details', response);
                  // Refresh the list of saved portfolios
                  loadSavedPortfolios();
                  // Show success message
                  toast({
                    title: "Portfolio Saved",
                    description: "Your portfolio has been saved with your investment details."
                  });
                }
              }).catch(error => {
                console.error('Error saving new portfolio with investment details:', error);
                toast({
                  title: "Save Failed",
                  description: "Failed to save portfolio. Please try again.",
                  variant: "destructive"
                });
              });
            }

            // Hide loading indicator after a short delay to ensure UI updates are visible
            setTimeout(() => setIsLoadingPortfolio(false), 1000);
          }, 500);
        }

        // If there are portfolio changes, apply them
        if (data.response.portfolioChanges) {
          const updatedPortfolio = {
            ...generatedPortfolio,
            stocks: data.response.portfolioChanges.stocks || generatedPortfolio.stocks
          };

          setGeneratedPortfolio(updatedPortfolio);

          toast({
            title: "Portfolio Updated",
            description: "Your portfolio has been updated based on your request."
          });

          // Recalculate portfolio performance with the updated portfolio
          calculatePortfolioPerformance(investmentAmount, investmentDate);

          // Save the updated portfolio to Supabase
          console.log('Saving updated portfolio to Supabase after Gemini chat edit');

          // Find the portfolio ID if it's a saved portfolio
          const savedPortfolio = savedPortfolios.find(p => p.name === portfolioName);

          if (savedPortfolio) {
            // Update existing portfolio
            console.log('Updating existing portfolio in Supabase:', savedPortfolio.id);
            supabase.functions.invoke('portfolio-manager', {
              body: JSON.stringify({
                action: 'save',
                portfolio: {
                  id: savedPortfolio.id,
                  name: portfolioName,
                  description: portfolioDescription,
                  stocks: updatedPortfolio.stocks,
                  investmentAmount: investmentAmount,
                  investmentDate: investmentDate
                }
              }),
              headers: sessionData?.session ? {
                Authorization: `Bearer ${sessionData.session.access_token}`
              } : undefined
            }).then((response) => {
              const { error: saveError } = response;

              if (saveError) {
                console.error('Error updating portfolio after Gemini chat edit:', saveError);
                toast({
                  title: "Update Failed",
                  description: "Failed to update portfolio. Please try again.",
                  variant: "destructive"
                });
              } else {
                console.log('Portfolio updated in Supabase after Gemini chat edit', response);
                // Refresh the list of saved portfolios
                loadSavedPortfolios();
                toast({
                  title: "Portfolio Updated",
                  description: "Your portfolio has been updated."
                });
              }
            }).catch(error => {
              console.error('Error updating portfolio in Supabase after Gemini chat edit:', error);
              toast({
                title: "Update Failed",
                description: "Failed to update portfolio. Please try again.",
                variant: "destructive"
              });
            });
          } else {
            // Save as a new portfolio
            console.log('Saving new portfolio to Supabase after Gemini chat edit');
            supabase.functions.invoke('portfolio-manager', {
              body: JSON.stringify({
                action: 'save',
                portfolio: {
                  name: portfolioName,
                  description: portfolioDescription,
                  stocks: updatedPortfolio.stocks,
                  investmentAmount: investmentAmount,
                  investmentDate: investmentDate
                }
              }),
              headers: sessionData?.session ? {
                Authorization: `Bearer ${sessionData.session.access_token}`
              } : undefined
            }).then((response) => {
              const { data: saveData, error: saveError } = response;

              if (saveError) {
                console.error('Error saving new portfolio after Gemini chat edit:', saveError);

                // Check if this is a portfolio limit error
                if (saveData?.limitReached) {
                  toast({
                    title: "Portfolio Limit Reached",
                    description: saveData.error || `You have reached your portfolio limit. Please upgrade to the Pro plan for more portfolios.`,
                    variant: "destructive"
                  });
                } else {
                  toast({
                    title: "Save Failed",
                    description: "Failed to save portfolio. Please try again.",
                    variant: "destructive"
                  });
                }
              } else {
                console.log('New portfolio saved to Supabase after Gemini chat edit', response);
                // Refresh the list of saved portfolios
                loadSavedPortfolios();
                // Show success message
                toast({
                  title: "Portfolio Saved",
                  description: "Your updated portfolio has been saved."
                });
              }
            }).catch(error => {
              console.error('Error saving new portfolio to Supabase after Gemini chat edit:', error);
              toast({
                title: "Save Failed",
                description: "Failed to save portfolio. Please try again.",
                variant: "destructive"
              });
            });
          }
        }
      }
    } catch (error) {
      console.error('Error processing chat message:', error);
      toast({
        title: "Processing Failed",
        description: "Failed to process your request. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsChatProcessing(false);
      setChatMessage('');

      // Check if the message contains a request to close the chat
      const closeKeywords = ['close', 'exit', 'done', 'finish', 'thank', 'thanks'];
      if (closeKeywords.some(keyword => chatMessage.toLowerCase().includes(keyword))) {
        // Just clear the message but don't hide the chat
        setChatMessage('');
      } else {
        // Re-focus the input after processing is complete
        setTimeout(() => {
          if (chatInputRef.current) {
            chatInputRef.current.focus();
            // Ensure cursor is at the end of the input
            chatInputRef.current.selectionStart = chatInputRef.current.value.length;
            chatInputRef.current.selectionEnd = chatInputRef.current.value.length;
          }
        }, 0);
      }
    }
  };

  // Format relative time for news articles
  const getRelativeTime = (timestamp: string) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return diffDay === 1 ? '1d ago' : `${diffDay}d ago`;
    } else if (diffHour > 0) {
      return diffHour === 1 ? '1h ago' : `${diffHour}h ago`;
    } else if (diffMin > 0) {
      return diffMin === 1 ? '1m ago' : `${diffMin}m ago`;
    } else {
      return 'Just now';
    }
  };

  // Similarly move the NewsPanel inside PortfolioManager for consistency
  const NewsPanel = ({ stocks }: { stocks: PortfolioStock[] }) => {
    // Navigate to portfolio news page
    const handleViewAllClick = () => {
      navigate('/portfolio-news');
    };

    // Reference to the news container
    const newsContainerRef = useRef<HTMLDivElement>(null);

    // Ensure we have at least 5 stock tickers for news by duplicating if needed
    const getEnoughStockTickers = (): string[] => {
      if (!stocks || stocks.length === 0) return [];

      // Extract tickers from stocks
      const tickers = stocks.map(stock => stock.ticker);

      // If we have 5 or more unique tickers, return them
      if (tickers.length >= 5) return tickers;

      // Otherwise, duplicate tickers until we have at least 5
      const duplicatedTickers: string[] = [...tickers];
      let currentIndex = 0;

      while (duplicatedTickers.length < 5) {
        duplicatedTickers.push(tickers[currentIndex % tickers.length]);
        currentIndex++;
      }

      return duplicatedTickers;
    };

    // Get the stock count with duplicates if needed
    const stockCount = getEnoughStockTickers().length;

    // Effect to adjust the height of the news panel to match the allocation chart
    // Only run once on mount and on window resize
    useEffect(() => {
      const adjustHeight = () => {
        // Get the allocation chart container
        const allocationContainer = document.querySelector('.allocation-chart-container');
        const newsContainer = newsContainerRef.current;

        if (allocationContainer && newsContainer) {
          // Get the exact height of the allocation chart
          const allocationHeight = allocationContainer.clientHeight;

          // Get the header height dynamically
          const newsHeader = newsContainer.parentElement?.querySelector('h3');
          const headerHeight = newsHeader ? newsHeader.offsetHeight + 12 : 40; // Add margin/padding or use default

          // Set the news container to exactly match the allocation height minus header
          newsContainer.style.height = `${allocationHeight - headerHeight}px`;

          // Also limit the number of news items displayed based on container height
          const newsItemHeight = 45; // Approximate height of each news item in pixels
          const maxItems = Math.floor((allocationHeight - headerHeight) / newsItemHeight);
          // Store this value for the component to use
          newsContainer.dataset.maxItems = String(Math.max(5, maxItems)); // Ensure at least 5 items
        }
      };

      // Adjust on mount and when window resizes
      setTimeout(adjustHeight, 100); // Slight delay to ensure rendering is complete
      window.addEventListener('resize', adjustHeight);

      // Clean up event listeners
      return () => {
        window.removeEventListener('resize', adjustHeight);
      };
    }, []);

  return (
      <div className="flex flex-col h-full">
        <h3 className="text-white/95 text-base font-medium mb-3 border-b border-white/10 pb-2 flex items-center justify-between">
          <span>Portfolio News</span>
          <button
            onClick={handleViewAllClick}
            className="text-white/50 hover:text-white/90 transition-colors text-xs flex items-center"
          >
            <span>View All</span>
            <ExternalLink className="w-3 h-3 ml-1" />
          </button>
        </h3>

        <div ref={newsContainerRef} className="flex-1 overflow-y-auto backdrop-blur-sm bg-[#0F0F0F]/30 rounded-lg border border-white/5 p-3 custom-scrollbar news-container">
          {isLoadingNews ? (
              // Loading skeleton
              [...Array(10)].map((_, index) => (
                <div key={index} className="mb-2 pb-2 border-b border-[#1A1A1A]/30 last:border-0 last:mb-0 last:pb-0 p-2">
                  <Skeleton className="h-4 w-full mb-2" />
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-3 w-20" />
                    <div className="flex items-center">
                      <Skeleton className="h-3 w-10 mr-2" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                </div>
              ))
            ) : portfolioNews.length > 0 ? (
            // Display a limited number of news items based on available space - using pre-shuffled news
            (shuffledNewsRef.current.length > 0 ? shuffledNewsRef.current : portfolioNews).slice(0, newsContainerRef.current?.dataset.maxItems ? parseInt(newsContainerRef.current.dataset.maxItems) : Math.min(stockCount, 8)).map((item: any, index: number) => (
              <div
                key={item.id || index}
                className="mb-0.5 pb-0.5 border-b border-[#1A1A1A]/30 last:border-0 last:mb-0 last:pb-0 hover:bg-white/10 rounded-md p-1 transition-all duration-150 cursor-pointer group"
                onClick={() => item.url && window.open(item.url, '_blank')}
              >
                <h4 className="text-white/90 text-xs font-medium mb-0.5 truncate flex items-center">
                  <span className="flex-1 group-hover:text-white transition-colors">{item.title}</span>
                  <ExternalLink className="w-2.5 h-2.5 ml-1 flex-shrink-0 opacity-40 group-hover:opacity-80 transition-opacity" />
                </h4>
                <div className="flex justify-between items-center">
                  <span className="text-white/60 text-[10px] truncate max-w-[80px]">{item.source || 'Financial News'}</span>
                  <div className="flex items-center">
                    <span className="text-white/50 text-[10px] mr-1">{getRelativeTime(item.published_utc)}</span>
                    <span className="bg-[#1A1A1A] px-1.5 py-0.5 rounded-full text-[10px] text-white/70 border border-white/5">{item.ticker}</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            // No news found
            <div className="flex flex-col items-center justify-center h-full py-6">
              <Newspaper className="w-8 h-8 text-white/20 mb-2" />
              <p className="text-white/40 text-xs text-center">No news available for this portfolio</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Backtest Modal Component - Simple version
  const BacktestModal = () => {
    if (!backtestResults) return null;

    // Extract data from backtest results
    const chartData = backtestResults.chartData || [];
    const startValue = chartData.length > 0 ? chartData[0].value : 0;
    const endValue = chartData.length > 0 ? chartData[chartData.length - 1].value : 0;
    const percentageIncrease = startValue > 0 ? ((endValue - startValue) / startValue) * 100 : 0;
    const isPositive = percentageIncrease >= 0;

    // Create chart options for the backtest chart
    const getChartOptions = (): EChartsOption => {
      // Calculate percentage return over time instead of absolute values
      const initialValue = chartData.length > 0 ? chartData[0].value : 0;
      const percentageData = chartData.map((item: any) => {
        const percentReturn = initialValue > 0 ? ((item.value - initialValue) / initialValue) * 100 : 0;
        return {
          date: item.date,
          value: percentReturn
        };
      });

      return {
        backgroundColor: 'transparent',
        grid: {
          left: 5,
          right: 5,
          top: 10,
          bottom: 5,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(10, 10, 10, 0.8)',
          borderColor: 'rgba(30, 30, 30, 0.8)',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)'
          },
          formatter: (params: any) => {
            const dataPoint = params[0];
            const date = new Date(chartData[dataPoint.dataIndex].date);
            const formattedDate = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
            const value = dataPoint.value;
            return `${formattedDate}: ${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
          }
        },
        xAxis: {
          type: 'category',
          data: chartData.map((item: any) => {
            const date = new Date(item.date);
            return date.getFullYear().toString();
          }),
          axisLine: {
            show: false // Hide the x-axis line
          },
          axisLabel: {
            show: false // Hide the x-axis labels
          },
          axisTick: {
            show: false // Hide the x-axis ticks
          },
          splitLine: {
            show: false // Hide the x-axis grid lines
          },
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.5)',
            fontSize: 10,
            formatter: (value: number) => {
              return `${value > 0 ? '+' : ''}${value.toFixed(0)}%`;
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.05)'
            }
          }
        },
        series: [
          {
            data: percentageData.map((item: {value: number}) => item.value),
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: isPositive ? 'rgba(78, 184, 151, 0.8)' : 'rgba(229, 128, 128, 0.8)',
              width: 3
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: isPositive ? 'rgba(78, 184, 151, 0.2)' : 'rgba(229, 128, 128, 0.2)'
                  },
                  {
                    offset: 1,
                    color: isPositive ? 'rgba(78, 184, 151, 0)' : 'rgba(229, 128, 128, 0)'
                  }
                ]
              }
            }
          }
        ]
      };
    };

    return (
      <Dialog open={isBacktestModalOpen} onOpenChange={setIsBacktestModalOpen}>
        <DialogContent className="bg-[#0D0D0D] border border-[#1A1A1A]/30 text-white max-w-4xl p-0 overflow-hidden">
          <div className="flex flex-col h-[550px]">
            {/* Header */}
            <div className="flex justify-between items-center p-4 border-b border-[#1A1A1A]/30">
              <div>
                <h3 className="text-lg font-medium text-white/90">Backtest Results</h3>
                <div className="flex items-center mt-1">
                  <span className="text-white/50 text-xs">5-Year Return:</span>
                  <span className={`text-sm font-medium ml-2 ${isPositive ? 'text-[#4EB897]' : 'text-[#E58080]'}`}>
                    {isPositive ? '+' : ''}{percentageIncrease.toFixed(2)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Chart */}
            <div className="flex-1 w-full overflow-hidden">
              <ReactECharts
                option={getChartOptions()}
                style={{ height: '100%', width: '100%' }}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // This component is not used and was causing a warning
  // Removed ChatBar component to fix the warning

  // Toggle chat visibility with animation
  const toggleChat = () => {
    if (isChatEnabled) {
      // Starting to animate out
      setIsAnimatingOut(true);
      // After animation completes, actually disable the chat
      setTimeout(() => {
        setIsChatEnabled(false);
        setIsAnimatingOut(false);
      }, 350); // Match animation duration
    } else {
      // Enable immediately with slide-in animation
      setIsChatEnabled(true);

      // Focus the input after a short delay
      setTimeout(() => {
        if (chatInputRef.current) {
          chatInputRef.current.focus();
        }
      }, 50);
    }
  };

  return (
    <div className={`min-h-screen bg-[#0A0A0A] text-white flex flex-col ${classes.container}`}>
      <style>{animationStyles}</style>
      <style>{transitionStyles}</style>
      <style>
        {`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          /* Ensure news container fills available space */
          .panel-container {
            display: flex;
            flex-direction: column;
            height: 100%;
          }

          /* Ensure news items are evenly distributed */
          .news-container {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }

          .chat-response {
            background: linear-gradient(to bottom, rgba(20, 20, 20, 0.7), rgba(10, 10, 10, 0.7));
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }

          /* Chat box animation */
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          @keyframes slideOutDown {
            from {
              opacity: 1;
              transform: translateY(0);
            }
            to {
              opacity: 0;
              transform: translateY(30px);
            }
          }

          .slide-in-up {
            animation: slideInUp 0.35s cubic-bezier(0.16, 1, 0.3, 1) forwards;
          }

          .slide-out-down {
            animation: slideOutDown 0.35s cubic-bezier(0.16, 1, 0.3, 1) forwards;
          }

          @keyframes slideLeft {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%);
            }
          }

          .animate-slide-left {
            animation: slideLeft 50s linear infinite;
          }
        `}
      </style>

      <div className="fixed top-2 right-4 z-10">
        <Button
          onClick={() => navigate('/portfolio-news')}
          className="bg-[#1A1A1C]/70 backdrop-blur-md text-white/90 border border-[#232323]/70 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:bg-[#1F1F1F]/90 transition-colors"
        >
          View Portfolio News
        </Button>
      </div>

      <Tabs
        defaultValue={activeTab}
        value={activeTab}
        onValueChange={(value) => {
          handleTabClick(value);
          setActiveTab(value);
        }}
        className="w-full flex-1 flex flex-col"
      >
        <div className={`flex justify-center ${isMobile ? 'mt-8' : 'mt-12'}`}>
          <TabsList className="bg-[#0D0D0D]/90 backdrop-blur-md border border-[#1A1A1A]/30 rounded-full p-0.5 overflow-hidden shadow-[0_2px_8px_rgba(0,0,0,0.2)]">
            <TabsTrigger
              value="create"
              className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} rounded-full data-[state=active]:bg-[#141414]/80 data-[state=active]:backdrop-blur-lg data-[state=active]:text-white/95 text-white/70 data-[state=active]:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] transition-all duration-200 relative ${clickedTab === 'create' ? 'tab-click' : ''}`}
            >
              <Plus className="w-3.5 h-3.5 mr-1.5 opacity-90" />
              <span className={isMobile ? 'hidden sm:inline' : 'inline'}>Create Portfolio</span>
              {isMobile && <span className="sm:hidden">Create</span>}
            </TabsTrigger>
            <TabsTrigger
              value="saved"
              className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} rounded-full data-[state=active]:bg-[#141414]/80 data-[state=active]:backdrop-blur-lg data-[state=active]:text-white/95 text-white/70 data-[state=active]:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] transition-all duration-200 relative ${clickedTab === 'saved' ? 'tab-click' : ''}`}
            >
              <Briefcase className="w-3.5 h-3.5 mr-1.5 opacity-90" />
              <span className={isMobile ? 'hidden sm:inline' : 'inline'}>Saved Portfolios</span>
              {isMobile && <span className="sm:hidden">Saved</span>}
            </TabsTrigger>
        </TabsList>
        </div>

        <div className="tabs-content flex-1">
          <TabsContent
            value="create"
            className={`flex-1 flex flex-col justify-start items-center px-4 pt-16 ${getAnimationClass('create')}`}
          >
            {!generatedPortfolio ? (
              <div className={`${isMobile ? 'max-w-full px-3' : 'max-w-2xl'} w-full mx-auto relative`}>
                {/* Enhanced Header with Depth */}
                <div className={`text-center ${isMobile ? 'mb-8' : 'mb-12'}`}>
                  <div className="flex items-center justify-center gap-3 mb-6">
                    <div className="flex-shrink-0 p-2 rounded-xl bg-white/[0.02] border border-white/[0.08]">
                      <img
                        src="http://thecodingkid.oyosite.com/logo_only.png"
                        alt="Osis Logo"
                        className="w-8 h-8 object-contain"
                      />
                    </div>
                  </div>
                  <div className="space-y-3">
                    <WelcomeHeading
                      text="What portfolio should we build?"
                      className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-light text-white font-sans tracking-tight leading-relaxed`}
                      speed={80}
                    />

                  </div>
                </div>



                {/* Enhanced Input Area with Depth */}
                <div className="space-y-6">
                  <div className="relative">
                    <div className={`bg-white/[0.02] border border-white/[0.08] rounded-2xl ${isMobile ? 'p-3' : 'p-6'} hover:border-white/[0.12] transition-all duration-300 shadow-[0_4px_20px_rgba(0,0,0,0.15)] hover:shadow-[0_6px_24px_rgba(0,0,0,0.2)]`}>
                      <textarea
                        className={`w-full bg-transparent border-none text-white ${isMobile ? 'text-base' : 'text-lg'} resize-none focus:outline-none leading-relaxed placeholder:text-white/40 font-sans`}
                        value={portfolioDescription}
                        onChange={(e) => setPortfolioDescription(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            generatePortfolio();
                          }
                        }}
                        rows={isMobile ? 3 : 4}
                        style={{ minHeight: isMobile ? '120px' : '140px', maxHeight: isMobile ? '180px' : '220px' }}
                        disabled={isGenerating}
                        placeholder="Build a tech-focused portfolio with growth potential..."
                      />

                      {/* Cohesive Bottom Section with Compact Carousel and Generate Button */}
                      <div className={`${isMobile ? 'flex flex-col gap-4' : 'flex items-end justify-between gap-6'} mt-4 pt-4 border-t border-white/[0.06]`}>
                        {/* Sliding Carousel on the left */}
                        <div className={`${isMobile ? 'w-full' : 'flex-1'} overflow-hidden`}>
                          <div className="text-xs text-white/40 mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                            Quick Ideas
                          </div>
                          <div className="relative overflow-hidden">
                            <div
                              className="flex gap-3 animate-slide-left"
                              style={{
                                animation: 'slideLeft 20s linear infinite',
                                width: 'max-content'
                              }}
                            >
                              {[
                                "Tech-focused growth portfolio",
                                "ESG sustainable investing",
                                "Dividend aristocrats",
                                "Emerging markets exposure",
                                "AI and automation plays",
                                "Clean energy transition",
                                "Value investing approach",
                                "Small-cap growth stocks",
                                "International diversification"
                              ].concat([
                                "Tech-focused growth portfolio",
                                "ESG sustainable investing",
                                "Dividend aristocrats",
                                "Emerging markets exposure",
                                "AI and automation plays",
                                "Clean energy transition"
                              ]).map((prompt, index) => (
                                <button
                                  key={index}
                                  onClick={() => setPortfolioDescription(prompt)}
                                  className="flex-shrink-0 px-3 py-1.5 bg-white/[0.04] hover:bg-white/[0.08] border border-white/[0.08] hover:border-white/[0.12] rounded-lg text-white/60 hover:text-white/80 text-xs transition-all duration-200 whitespace-nowrap"
                                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                                >
                                  {prompt}
                                </button>
                              ))}
                            </div>
                            {/* Subtle fade out effect with dark grey */}
                            <div className="absolute top-0 bottom-0 right-0 w-12 pointer-events-none bg-gradient-to-l from-[#1A1A1A] via-[#1A1A1A]/80 to-transparent"></div>
                          </div>
                        </div>

                        {/* Generate button on the right */}
                        <div className={`${isMobile ? 'w-full' : 'flex-shrink-0'}`}>
                          <button
                            onClick={generatePortfolio}
                            disabled={isGenerating || !portfolioDescription.trim() || savedPortfolios.length >= (planType === PLAN_TYPES.pro ? 3 : 1)}
                            className={`${isMobile ? 'w-full px-6 py-3' : 'px-8 py-3'} bg-white text-black rounded-lg text-sm font-semibold transition-all duration-200 disabled:cursor-not-allowed border border-white/20 shadow-[0_0_30px_rgba(255,255,255,0.6)] hover:shadow-[0_0_40px_rgba(255,255,255,0.8)] disabled:bg-white disabled:text-black`}
                            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                          >
                            {isGenerating ? (
                              <div className="flex items-center justify-center gap-2">
                                <Loader2 className="w-4 h-4 animate-spin" />
                                Building Portfolio...
                              </div>
                            ) : (
                              'Generate Portfolio'
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Carousel now integrated inside chat box */}
              </div>
            ) : (
              <div className="w-full max-w-5xl space-y-4 py-6 tab-item-animation pb-32"> {/* Added bottom padding for chat input */}
                {/* Main dashboard container - Mobile Responsive */}
                <div className={`mx-auto ${isMobile ? 'w-full px-3' : 'max-w-5xl'}`}>
                  {/* Enhanced Portfolio Header */}
                  <div className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'} mb-8`}>
                    <div className="space-y-2">
                      <WelcomeHeading
                        text="Past Results"
                        className={`gradient-text ${isMobile ? 'text-2xl' : 'text-3xl'} font-light tracking-tight`}
                        speed={80}
                      />
                      <p className="text-white/60 text-sm font-sans">
                        AI-optimized allocation based on your preferences
                      </p>
                    </div>
                    <div className="flex items-center gap-3">
                      <button
                        onClick={toggleChat}
                        className={`group flex items-center gap-1.5 ${isMobile ? 'px-2.5 py-1.5 text-xs' : 'px-4 py-2.5 text-sm'} rounded-xl border transition-all duration-200 font-sans font-medium ${
                          isChatEnabled
                            ? 'bg-green-500/10 border-green-500/30 text-green-400 hover:bg-green-500/15'
                            : 'bg-white/[0.02] border-white/[0.08] text-white/80 hover:bg-white/[0.04] hover:border-white/[0.12] hover:text-white'
                        }`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transition-transform group-hover:scale-110">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                        {isChatEnabled ? 'Editing' : 'Edit Portfolio'}
                      </button>
                    </div>
                  </div>

                  {/* Enhanced Performance Chart */}
                  <div className="bg-gradient-to-br from-[#0D0D0D] to-[#0A0A0A] border border-[#1A1A1A]/30 rounded-2xl overflow-hidden shadow-[0_4px_16px_rgba(0,0,0,0.2)] mb-8 relative" style={{ height: 'min(520px, 60vh)' }}>
                    {isLoadingPortfolio && (
                      <div className="absolute inset-0 bg-black/50 z-30 flex items-center justify-center backdrop-blur-sm">
                        <div className="flex items-center gap-3 text-white/80">
                          <Loader2 className="w-6 h-6 animate-spin text-green-400" />
                          <span className="text-sm font-sans">Analyzing portfolio performance...</span>
                        </div>
                      </div>
                    )}
                    <PortfolioPerformanceChart
                      data={null}
                      portfolioValue={portfolioValue}
                      portfolioReturn={portfolioReturn}
                      investmentAmount={investmentAmount}
                      investmentDate={investmentDate}
                      portfolio={generatedPortfolio}
                      key={`chart-${investmentAmount}-${investmentDate}-${portfolioValue}`}
                    />
                  </div>

                  {/* Enhanced Two-Column Layout - Mobile Responsive */}
                  <div className={`grid ${isMobile ? 'grid-cols-1 gap-3' : 'grid-cols-2 gap-6'} mb-8`}>
                    {/* Allocation Chart Panel */}
                    <div className={`bg-gradient-to-br from-[#0D0D0D] to-[#0A0A0A] border border-[#1A1A1A]/30 rounded-2xl ${isMobile ? 'p-4' : 'p-6'} shadow-[0_4px_16px_rgba(0,0,0,0.15)] hover:shadow-[0_6px_20px_rgba(0,0,0,0.2)] transition-all duration-300 flex flex-col`}>
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-2 h-2 rounded-full bg-green-400"></div>
                        <h3 className="text-lg font-medium text-white font-sans">Asset Allocation</h3>
                      </div>
                      <div className="flex-1">
                        <AllocationChart stocks={generatedPortfolio.stocks} />
                      </div>
                    </div>

                    {/* News Panel */}
                    <div className={`bg-gradient-to-br from-[#0D0D0D] to-[#0A0A0A] border border-[#1A1A1A]/30 rounded-2xl ${isMobile ? 'p-4' : 'p-6'} shadow-[0_4px_16px_rgba(0,0,0,0.15)] hover:shadow-[0_6px_20px_rgba(0,0,0,0.2)] transition-all duration-300 flex flex-col`}>
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-2 h-2 rounded-full bg-white/60"></div>
                        <h3 className="text-lg font-medium text-white font-sans">Market News</h3>
                      </div>
                      <div className="flex-1">
                        <NewsPanel stocks={generatedPortfolio.stocks} />
                      </div>
                    </div>
                  </div>

                  {/* Display chat response if available */}
                  {chatResponse && (
                    <div className="chat-response max-w-4xl mx-auto mb-6">
                      <p className="text-white/90 text-sm">{chatResponse}</p>
                    </div>
                  )}
                  </div>

                {/* Backtest Modal */}
                <BacktestModal />

                {/* Compact Chat Input - Mobile Responsive */}
                {generatedPortfolio && (isChatEnabled || isAnimatingOut) && (
                  <div
                    className={`fixed bottom-0 ${isMobile ? 'left-0' : 'left-64'} right-0 z-20 bg-gradient-to-t from-[#0A0A0A]/90 via-[#0A0A0A]/70 to-transparent pb-6 pt-6 ${isChatEnabled && !isAnimatingOut ? 'slide-in-up' : 'slide-out-down'}`}
                  >
                    <div className={`max-w-4xl mx-auto ${classes.container}`}>
                      <div className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 shadow-[0_2px_12px_rgba(0,0,0,0.15)] backdrop-blur-xl hover:border-white/[0.12] focus-within:border-white/[0.15] transition-all duration-300">
                        <div className="relative">
                          <textarea
                            ref={chatInputRef}
                            className="w-full bg-transparent border-none text-white text-base resize-none focus:outline-none leading-relaxed placeholder:text-white/40 font-sans pr-20 pb-8"
                            value={chatMessage}
                            onChange={(e) => setChatMessage(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                processChatMessage();
                              }
                            }}
                            rows={2}
                            style={{ minHeight: '60px', maxHeight: '100px' }}
                            disabled={isChatProcessing || isAnimatingOut}
                            placeholder="Rebalance to be more defensive"
                          />
                          {/* Button positioned lower in bottom right */}
                          <div className="absolute bottom-1 right-1">
                            <button
                              onClick={processChatMessage}
                              disabled={isChatProcessing || !chatMessage.trim()}
                              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                                chatMessage.trim()
                                  ? 'bg-white text-black shadow-[0_0_20px_rgba(255,255,255,0.4)] hover:shadow-[0_0_30px_rgba(255,255,255,0.6)] border border-white/20'
                                  : 'bg-white/20 text-white/60 border border-white/10'
                              }`}
                              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                            >
                              {isChatProcessing ? (
                                <>
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                <>
                                  <span>Update</span>
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                  </svg>
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent
            value="saved"
            className={`flex-1 p-8 pb-24 ${getAnimationClass('saved')}`} /* Added bottom padding for chat bar */
          >
            <div className="max-w-6xl mx-auto">
              <div className="mb-4 text-center">
                <WelcomeHeading
                  text="Saved Portfolios"
                  className="gradient-text text-2xl font-medium"
                  speed={80}
                />
                                  </div>
              {isLoadingSaved ? (
                <div className={`grid ${classes.grid} gap-6`}>
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className={`${classes.chartHeight} bg-[#141414]`} />
                  ))}
                          </div>
              ) : savedPortfolios.length > 0 ? (
                <div className={`grid ${classes.grid} gap-6 tab-item-animation`}>
                  {savedPortfolios.map((portfolio) => (
                    <PortfolioCard
                      key={portfolio.id}
                      portfolio={portfolio}
                      loadPortfolio={loadPortfolio}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 bg-gradient-to-b from-[#0D0D0D] to-[#090909] shadow-[0_4px_20px_rgba(0,0,0,0.25),inset_0_1px_0_rgba(255,255,255,0.06)] border border-[#222224]/30 rounded-xl">

                </div>
              )}
            </div>
        </TabsContent>
        </div>
      </Tabs>

      {/* Add global styles to remove all textarea focus borders */}
      <style>
        {`
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
        `}
      </style>
    </div>
  );
};

export default PortfolioManager;
