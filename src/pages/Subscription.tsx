import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, ArrowLeft, Crown, Zap, TrendingUp, BarChart3, Bot, Sparkles, Star, ArrowRight, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
import { SubscribeButton } from '@/components/subscription/SubscribeButton';
import { useSubscription } from '@/hooks/useSubscription';
import { useToast } from '@/components/ui/use-toast';

const Subscription = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { subscription, isLoadingSubscription, refetch } = useSubscription();
  const [selectedPlan, setSelectedPlan] = useState('pro');

  // Plan configurations
  const plans = [
    {
      id: 'basic',
      name: 'Basic',
      price: '$9',
      period: '/month',
      description: 'Perfect for getting started with AI trading',
      icon: Bot,
      features: [
        '5 AI Trading Agents',
        'Basic Backtesting',
        'Standard Market Scanner',
        'Email Support',
        'Basic Analytics'
      ],
      limitations: [
        'Limited to 100 scans/month',
        'Basic agent complexity',
        'Standard data feeds'
      ]
    },
    {
      id: 'pro',
      name: 'Professional',
      price: '$29',
      period: '/month',
      description: 'Advanced trading with premium features',
      icon: TrendingUp,
      popular: true,
      spotlight: true,
      features: [
        'Unlimited AI Trading Agents',
        'Advanced Backtesting & Analytics',
        'Real-time Market Scanner',
        'Priority Support',
        'Advanced Portfolio Management',
        'Custom Agent Builder',
        'Risk Management Tools',
        'API Access',
        'Advanced Chart Analysis'
      ],
      benefits: [
        'Up to 10x more profitable signals',
        'Real-time market opportunities',
        'Professional-grade analytics',
        'Custom strategy development'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: '$99',
      period: '/month',
      description: 'Complete trading ecosystem for professionals',
      icon: Crown,
      features: [
        'Everything in Professional',
        'White-label Solutions',
        'Dedicated Account Manager',
        'Custom Integrations',
        'Advanced Risk Controls',
        'Institutional Data Feeds',
        'Multi-user Management',
        'Custom Reporting',
        'SLA Guarantee'
      ],
      benefits: [
        'Institutional-grade infrastructure',
        'Dedicated support team',
        'Custom feature development',
        'Enterprise security'
      ]
    }
  ];

  const currentPlan = plans.find(p => p.id === 'basic'); // Simulate current plan

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
      {/* Clean Header */}
      <div className="px-8 py-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl font-normal text-white mb-4 font-sans">
            Choose Your Plan
          </h1>
          <p className="text-white/60 text-base font-sans leading-relaxed">
            Unlock advanced AI trading capabilities
          </p>
        </div>
      </div>

      {/* Plans Section */}
      <div className="flex-1 px-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              const Icon = plan.icon;
              const isSpotlight = plan.spotlight;
              const isCurrent = plan.id === 'basic';

              return (
                <div
                  key={plan.id}
                  className={`relative rounded-xl border transition-all duration-200 ${
                    isSpotlight
                      ? 'bg-white/[0.02] border-white/[0.12] scale-105'
                      : 'bg-white/[0.01] border-white/[0.06] hover:border-white/[0.10]'
                  }`}
                >
                  {/* Popular Badge */}
                  {isSpotlight && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-white text-black rounded-full text-xs font-medium">
                        Most Popular
                      </div>
                    </div>
                  )}

                  <div className="p-6">
                    {/* Plan Header */}
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-medium text-white mb-2 font-sans">{plan.name}</h3>
                      <p className="text-white/50 text-sm font-sans mb-4">{plan.description}</p>

                      <div className="flex items-baseline justify-center gap-1">
                        <span className="text-3xl font-normal text-white font-sans">
                          {plan.price}
                        </span>
                        <span className="text-white/50 text-sm font-sans">{plan.period}</span>
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-3 mb-6">
                      {plan.features.slice(0, 4).map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                          <span className="text-white/70 text-sm font-sans">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Action Button */}
                    <button
                      className={`w-full py-2.5 text-sm font-medium rounded-lg transition-all duration-200 font-sans ${
                        isCurrent
                          ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                          : isSpotlight
                          ? 'bg-white hover:bg-white/95 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.03),0_1px_3px_rgba(0,0,0,0.08)]'
                          : 'bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08]'
                      }`}
                      disabled={isCurrent}
                    >
                      {isCurrent ? 'Current Plan' : `Upgrade to ${plan.name}`}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Simple Footer */}
          <div className="mt-12 text-center">
            <p className="text-white/50 text-sm font-sans">
              30-day money-back guarantee • Cancel anytime
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
