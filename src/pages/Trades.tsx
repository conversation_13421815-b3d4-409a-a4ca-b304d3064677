import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useTrades, TimeFilter, Trade } from '@/hooks/useTrades';
import { fetchLatestPriceData, LatestPriceData } from '@/services/polygonService';
import { ArrowUp, ArrowDown, Calendar, Clock, Info, TrendingUp } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';

const Trades = () => {
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('all');
  const { trades, loading, error, performance } = useTrades(timeFilter);
  const [priceData, setPriceData] = useState<Record<string, LatestPriceData>>({});





  // Function to check if a trade has hit take profit or stop loss and update it in Supabase
  const checkAndUpdateTradeOutcome = async (trade: Trade, currentPrice: number, dataType: string | undefined) => {
    // Skip if trade is already closed or doesn't have entry price, take profit, or stop loss
    if (trade.status === 'closed' || trade.status === 'canceled' ||
        !trade.entry_price || !trade.take_profit || !trade.stop_loss ||
        trade.outcome) {
      return;
    }

    // Log the price check for debugging
    console.log(`Checking trade ${trade.id} for ${trade.ticker}: Current price $${currentPrice} (${dataType || 'unknown'}) against TP: $${trade.take_profit}, SL: $${trade.stop_loss}`);

    let outcome: 'take_profit' | 'stop_loss' | null = null;
    let closePrice = null;
    let profitAmount = null;

    // Calculate investment amount based on confidence
    let investmentAmount = 200; // Default medium
    switch (trade.confidence) {
      case 'LOW':
        investmentAmount = 100;
        break;
      case 'MEDIUM':
        investmentAmount = 200;
        break;
      case 'HIGH':
        investmentAmount = 300;
        break;
    }

    if (trade.direction === 'LONG') {
      // For LONG trades
      if (currentPrice >= trade.take_profit) {
        // Take profit hit
        outcome = 'take_profit';
        closePrice = trade.take_profit;
        profitAmount = investmentAmount * ((trade.take_profit - trade.entry_price) / trade.entry_price);
      } else if (currentPrice <= trade.stop_loss) {
        // Stop loss hit
        outcome = 'stop_loss';
        closePrice = trade.stop_loss;
        profitAmount = investmentAmount * ((trade.stop_loss - trade.entry_price) / trade.entry_price);
      }
    } else if (trade.direction === 'SHORT') {
      // For SHORT trades
      if (currentPrice <= trade.take_profit) {
        // Take profit hit
        outcome = 'take_profit';
        closePrice = trade.take_profit;
        profitAmount = investmentAmount * ((trade.entry_price - trade.take_profit) / trade.entry_price);
      } else if (currentPrice >= trade.stop_loss) {
        // Stop loss hit
        outcome = 'stop_loss';
        closePrice = trade.stop_loss;
        profitAmount = investmentAmount * ((trade.entry_price - trade.stop_loss) / trade.entry_price);
      }
    }

    // If outcome is determined, update the trade in Supabase
    if (outcome) {
      console.log(`Trade ${trade.id} for ${trade.ticker} hit ${outcome} at $${closePrice}. Updating in database...`);
      try {
        const { error } = await supabase
          .from('trades')
          .update({
            status: 'closed',
            outcome,
            close_price: closePrice,
            close_date: new Date().toISOString(),
            profit_amount: profitAmount
          })
          .eq('id', trade.id);

        if (error) {
          console.error('Error updating trade outcome:', error);
        } else {
          console.log(`Successfully updated trade ${trade.id} outcome to ${outcome}`);
        }
      } catch (err) {
        console.error('Error updating trade outcome:', err);
      }
    }
  };

  // Fetch current prices for all tickers in the trades with debouncing
  useEffect(() => {
    // Skip if no trades or still loading
    if (trades.length === 0 || loading) return;

    // Extract unique tickers from trades
    const tickers = [...new Set(trades.map(trade => trade.ticker))];

    if (tickers.length === 0) return;

    // Create a debounced fetch function
    let timeoutId: ReturnType<typeof setTimeout> | undefined;

    const debouncedFetch = () => {
      // Clear any existing timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Set a new timeout
      timeoutId = setTimeout(async () => {
        try {
          const priceResults = await fetchLatestPriceData(tickers);

          // Convert array to object with ticker as key
          const priceMap = priceResults.reduce((acc, item) => {
            acc[item.symbol] = item;
            return acc;
          }, {} as Record<string, LatestPriceData>);

          setPriceData(priceMap);

          // Check each trade to see if it has hit take profit or stop loss
          for (const trade of trades) {
            const currentPriceData = priceMap[trade.ticker];
            if (currentPriceData && !isNaN(currentPriceData.price)) {
              await checkAndUpdateTradeOutcome(trade, currentPriceData.price, currentPriceData.dataType);
            }
          }
        } catch (err) {
          console.error('Error fetching current prices:', err);
        }
      }, 300); // 300ms debounce time
    };

    // Call the debounced function
    debouncedFetch();

    // Cleanup function to clear the timeout if the component unmounts
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [trades, loading]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'MMM d, yyyy h:mm a');
  };

  // Get direction badge color
  const getDirectionColor = (direction: 'LONG' | 'SHORT') => {
    return direction === 'LONG' ? 'bg-emerald-500/20 text-emerald-300' : 'bg-rose-500/20 text-rose-300';
  };

  // Get confidence badge color
  const getConfidenceColor = (confidence: 'LOW' | 'MEDIUM' | 'HIGH') => {
    switch (confidence) {
      case 'HIGH':
        return 'bg-emerald-500/20 text-emerald-300';
      case 'MEDIUM':
        return 'bg-amber-500/20 text-amber-300';
      case 'LOW':
        return 'bg-rose-500/20 text-rose-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'suggested':
        return 'bg-blue-500/20 text-blue-300';
      case 'executed':
        return 'bg-amber-500/20 text-amber-300';
      case 'closed':
        return 'bg-emerald-500/20 text-emerald-300';
      case 'canceled':
        return 'bg-rose-500/20 text-rose-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get color for price change based on direction and entry price
  const getPriceChangeColor = (direction: 'LONG' | 'SHORT', entryPrice: number | undefined, currentPrice: number) => {
    if (!entryPrice) return '';

    // For LONG positions, price going up is good (green), down is bad (red)
    // For SHORT positions, price going down is good (green), up is bad (red)
    if (direction === 'LONG') {
      return currentPrice > entryPrice ? 'text-emerald-400' : 'text-rose-400';
    } else {
      return currentPrice < entryPrice ? 'text-emerald-400' : 'text-rose-400';
    }
  };

  // Get indicator for price change (up/down arrow and percentage)
  const getPriceChangeIndicator = (direction: 'LONG' | 'SHORT', entryPrice: number | undefined, currentPrice: number) => {
    if (!entryPrice) return '';

    const percentChange = ((currentPrice - entryPrice) / entryPrice) * 100;
    const absPercentChange = Math.abs(percentChange).toFixed(2);

    if (direction === 'LONG') {
      if (currentPrice > entryPrice) {
        return `↑ ${absPercentChange}%`;
      } else if (currentPrice < entryPrice) {
        return `↓ ${absPercentChange}%`;
      } else {
        return '0%';
      }
    } else { // SHORT
      if (currentPrice < entryPrice) {
        return `↓ ${absPercentChange}%`; // Down arrow for price, but profit on SHORT
      } else if (currentPrice > entryPrice) {
        return `↑ ${absPercentChange}%`; // Up arrow for price, but loss on SHORT
      } else {
        return '0%';
      }
    }
  };



  return (
    <div className="container mx-auto py-6 space-y-6 pb-20">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold tracking-tight">Trade Tracking</h1>
        <p className="text-muted-foreground text-sm">
          Track win rates and precise trade outcomes with 15-minute delayed stock prices.
        </p>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Win Rate</CardTitle>
            <CardDescription className="text-xs">Success rate</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="flex items-center">
              <div className="text-xl font-bold mr-1 text-emerald-400">
                {loading ? <Skeleton className="h-5 w-12" /> : `${performance.winRate?.toFixed(1)}%`}
              </div>
              <TrendingUp className="h-4 w-4 text-emerald-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Take Profit</CardTitle>
            <CardDescription className="text-xs">Successful trades</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="flex items-center">
              <div className="text-xl font-bold mr-1">
                {loading ? <Skeleton className="h-5 w-12" /> : performance.takeProfitTrades}
              </div>
              <ArrowUp className="h-4 w-4 text-emerald-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Stop Loss</CardTitle>
            <CardDescription className="text-xs">Stopped trades</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="flex items-center">
              <div className="text-xl font-bold mr-1">
                {loading ? <Skeleton className="h-5 w-12" /> : performance.stopLossTrades}
              </div>
              <ArrowDown className="h-4 w-4 text-rose-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">In Progress</CardTitle>
            <CardDescription className="text-xs">Active trades</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="flex items-center">
              <div className="text-xl font-bold mr-1">
                {loading ? <Skeleton className="h-5 w-12" /> : performance.inProgressTrades}
              </div>
              <Clock className="h-4 w-4 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Total Trades</CardTitle>
            <CardDescription className="text-xs">All trades</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="text-xl font-bold">
              {loading ? <Skeleton className="h-5 w-12" /> : performance.totalTrades}
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Long Positions</CardTitle>
            <CardDescription className="text-xs">Bullish trades</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="flex items-center">
              <div className="text-xl font-bold mr-1">
                {loading ? <Skeleton className="h-5 w-12" /> : performance.longTrades}
              </div>
              <ArrowUp className="h-4 w-4 text-emerald-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Short Positions</CardTitle>
            <CardDescription className="text-xs">Bearish trades</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="flex items-center">
              <div className="text-xl font-bold mr-1">
                {loading ? <Skeleton className="h-5 w-12" /> : performance.shortTrades}
              </div>
              <ArrowDown className="h-4 w-4 text-rose-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm">Time Filter</CardTitle>
            <CardDescription className="text-xs">Current view</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="text-xl font-bold capitalize">
              {timeFilter === 'ytd' ? 'YTD' : timeFilter}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Trades</CardTitle>
          <CardDescription>
            Track win rate and precise trade outcomes
          </CardDescription>
          <div className="text-xs text-muted-foreground mt-1">
            Stock data has 15-minute delay, crypto prices are real-time
          </div>
          <Tabs defaultValue="all" className="w-full" onValueChange={(value) => setTimeFilter(value as TimeFilter)}>
            <TabsList className="grid grid-cols-6 w-full md:w-auto">
              <TabsTrigger value="all" className="text-xs">All Time</TabsTrigger>
              <TabsTrigger value="1d" className="text-xs">1 Day</TabsTrigger>
              <TabsTrigger value="5d" className="text-xs">5 Days</TabsTrigger>
              <TabsTrigger value="ytd" className="text-xs">YTD</TabsTrigger>
              <TabsTrigger value="1y" className="text-xs">1 Year</TabsTrigger>
              <TabsTrigger value="5y" className="text-xs">5 Years</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Trade History</CardTitle>
          <CardDescription>
            Complete list of all trade suggestions from all users
          </CardDescription>
          <div className="text-xs text-amber-400/80 mt-1 flex items-center">
            <Info className="h-3 w-3 mr-1" />
            Stock prices are delayed by 15 minutes for accurate trade outcome tracking
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-full" />
              ))}
            </div>
          ) : error ? (
            <div className="text-rose-400 flex items-center text-sm">
              <Info className="h-4 w-4 mr-2" />
              <span>Error loading trades: {error}</span>
            </div>
          ) : trades.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground text-sm">
              <Calendar className="h-10 w-10 mx-auto mb-3 opacity-20" />
              <p>No trades found for the selected time period</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow className="text-xs">
                    <TableHead className="py-2">Date</TableHead>
                    <TableHead className="py-2">Ticker</TableHead>
                    <TableHead className="py-2">Direction</TableHead>
                    <TableHead className="py-2">Entry Price</TableHead>
                    <TableHead className="py-2">Current Price</TableHead>
                    <TableHead className="py-2">Take Profit</TableHead>
                    <TableHead className="py-2">Stop Loss</TableHead>
                    <TableHead className="py-2">Confidence</TableHead>
                    <TableHead className="py-2">Status</TableHead>
                    <TableHead className="py-2">Profit/Loss</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="text-xs">
                  {trades.map((trade) => (
                    <TableRow key={trade.id}>
                      <TableCell className="font-medium py-2">
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                          {formatDate(trade.created_at)}
                        </div>
                      </TableCell>
                      <TableCell className="font-bold py-2">{trade.ticker}</TableCell>
                      <TableCell className="py-2">
                        <Badge className={`${getDirectionColor(trade.direction)} text-xs py-0 px-1.5`}>
                          {trade.direction}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-2">${trade.entry_price?.toFixed(2) || 'N/A'}</TableCell>
                      <TableCell className="py-2">
                        {priceData[trade.ticker] && !isNaN(priceData[trade.ticker].price) ? (
                          <div className="flex items-center">
                            <span className={`font-medium ${getPriceChangeColor(trade.direction, trade.entry_price, priceData[trade.ticker].price)}`}>
                              ${priceData[trade.ticker].price.toFixed(2)}
                            </span>
                            {trade.entry_price && (
                              <span className="ml-1 text-xs text-muted-foreground">
                                {getPriceChangeIndicator(trade.direction, trade.entry_price, priceData[trade.ticker].price)}
                              </span>
                            )}
                            {priceData[trade.ticker].dataType === 'delayed_15min' && (
                              <span className="ml-1 text-[10px] text-amber-400/80">
                                (15m delay)
                              </span>
                            )}
                            {priceData[trade.ticker].dataType === 'crypto_live' && (
                              <span className="ml-1 text-[10px] text-purple-400/80">
                                (live)
                              </span>
                            )}
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Skeleton className="h-3 w-12" />
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="py-2">${trade.take_profit?.toFixed(2) || 'N/A'}</TableCell>
                      <TableCell className="py-2">${trade.stop_loss?.toFixed(2) || 'N/A'}</TableCell>
                      <TableCell className="py-2">
                        <Badge className={`${getConfidenceColor(trade.confidence)} text-xs py-0 px-1.5`}>
                          {trade.confidence}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-2">
                        <div className="flex flex-col gap-1">
                          {trade.status === 'closed' && trade.outcome ? (
                            <Badge className={`${trade.outcome === 'take_profit' ? 'bg-emerald-500/20 text-emerald-300' : 'bg-rose-500/20 text-rose-300'} text-xs py-0 px-1.5`}>
                              {trade.outcome === 'take_profit' ? 'Take Profit' : 'Stop Loss'}
                            </Badge>
                          ) : (
                            <Badge className={`${getStatusColor(trade.status)} text-xs py-0 px-1.5`}>
                              {trade.status}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="py-2">
                        {trade.profit_amount !== undefined && trade.profit_amount !== null ? (
                          <span className={trade.profit_amount >= 0 ? 'text-emerald-400' : 'text-rose-400'}>
                            {trade.profit_amount >= 0 ? '+' : ''}
                            ${Number(trade.profit_amount).toFixed(2)}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Trades;
