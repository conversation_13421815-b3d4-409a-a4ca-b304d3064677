import { Link } from 'react-router-dom';

export default function About() {
  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <main className="container max-w-2xl mx-auto py-16 px-4">
        <h1 className="text-[2.5rem] font-[450] tracking-[-0.02em] mb-12">What is Osis?</h1>

        <div className="space-y-7 text-[1.0625rem] text-white/[0.82]">
          <p className="leading-[1.7]">
            Hey, I'm <PERSON> co-founder of Osis. I've been exploring financial markets for 3+ years, and I've noticed how many people struggle with the same problem: making sense of market information is unnecessarily complex. Charts everywhere, conflicting opinions, and endless noise. Then I saw how AI was evolving, and it clicked — we could build something that actually makes financial education more accessible.
          </p>

          <p className="leading-[1.7]">
            That's what Osis does. We organize and present information about stocks and crypto with clarity you won't find anywhere else. Wondering about technical patterns? We'll explain them. Curious about market trends? We'll show the data. Interested in different perspectives? We'll present various viewpoints. No fluff, no confusion — just educational insights that help you understand markets better.
          </p>

          <p className="leading-[1.7]">
            We're just getting started. Soon, Osis will offer even more personalized educational experiences, adapting to your learning style and interests while providing deeper context for market information. Every feature we add comes from conversations with users like you — because the best educational tools are built by listening to the people who use them.
          </p>

          <p className="leading-[1.7]">
            Our goal? To make Osis the most convenient and comprehensive resource for financial market education in the world. We believe that by making market information more accessible and easier to understand, we can help people become more informed about the complex world of investing. I started Osis with just an idea and zero coding experience, driven by the vision of transforming how people learn about markets.
          </p>

          <p className="leading-[1.7] text-white/[0.6] text-sm mt-8">
            <strong>Disclaimer:</strong> Osis provides educational information only and does not make recommendations to buy or sell any securities. All information provided by Osis is for educational and informational purposes only and should not be construed as investment advice. Always conduct your own research and consult with a licensed financial advisor before making any investment decisions.
          </p>
        </div>
      </main>
    </div>
  );
}