import express from 'express';
import Stripe from 'stripe';
import { supabase } from '@/integrations/supabase/client';

const router = express.Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-02-24.acacia',
});

router.post('/create-checkout', async (req, res) => {
  try {
    const { priceId, userId, returnUrl } = req.body;

    if (!priceId || !userId || !returnUrl) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${returnUrl}?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${returnUrl}?canceled=true`,
      customer_email: (await supabase.auth.getUser()).data.user?.email,
      metadata: {
        userId: userId,
      },
    });

    return res.json({ url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return res.status(500).json({ error: 'Failed to create checkout session' });
  }
});

export default router; 