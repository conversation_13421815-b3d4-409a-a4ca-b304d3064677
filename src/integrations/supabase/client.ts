// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Hardcoded values for testing
const supabaseUrl = 'https://pajqstbgncpbpcaffbpm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhanFzdGJnbmNwYnBjYWZmYnBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MTAxODgsImV4cCI6MjA2MTA4NjE4OH0.G2KNGyoP-qPi9x28lZcvMBzQbOwlLH4Vz1mm3Qp9WRo';

// Log the values for debugging
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Anon Key:', supabaseAnonKey);

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create client with the public/anon key (safe for client-side code)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});