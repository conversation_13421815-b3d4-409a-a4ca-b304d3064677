// Financial Metrics Constants
// Based on Polygon.io Financials API documentation

export interface FinancialMetric {
  id: string;
  label: string;
  statement: 'balance_sheet' | 'income_statement' | 'cash_flow_statement' | 'comprehensive_income';
  category: string;
  description: string;
  unit: string;
}

export const FINANCIAL_METRICS: FinancialMetric[] = [
  // Balance Sheet Metrics
  {
    id: 'assets',
    label: 'Total Assets',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Total assets of the company',
    unit: 'USD'
  },
  {
    id: 'current_assets',
    label: 'Current Assets',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Assets that can be converted to cash within one year',
    unit: 'USD'
  },
  {
    id: 'cash',
    label: 'Cash and Cash Equivalents',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Cash and short-term investments',
    unit: 'USD'
  },
  {
    id: 'accounts_receivable',
    label: 'Accounts Receivable',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Money owed to the company by customers',
    unit: 'USD'
  },
  {
    id: 'inventory',
    label: 'Inventory',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Value of goods held for sale',
    unit: 'USD'
  },
  {
    id: 'noncurrent_assets',
    label: 'Non-current Assets',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Long-term assets not expected to be converted to cash within one year',
    unit: 'USD'
  },
  {
    id: 'fixed_assets',
    label: 'Fixed Assets',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Property, plant, and equipment',
    unit: 'USD'
  },
  {
    id: 'intangible_assets',
    label: 'Intangible Assets',
    statement: 'balance_sheet',
    category: 'Assets',
    description: 'Non-physical assets like patents and trademarks',
    unit: 'USD'
  },
  {
    id: 'liabilities',
    label: 'Total Liabilities',
    statement: 'balance_sheet',
    category: 'Liabilities',
    description: 'Total debts and obligations of the company',
    unit: 'USD'
  },
  {
    id: 'current_liabilities',
    label: 'Current Liabilities',
    statement: 'balance_sheet',
    category: 'Liabilities',
    description: 'Debts due within one year',
    unit: 'USD'
  },
  {
    id: 'accounts_payable',
    label: 'Accounts Payable',
    statement: 'balance_sheet',
    category: 'Liabilities',
    description: 'Money owed to suppliers and vendors',
    unit: 'USD'
  },
  {
    id: 'long_term_debt',
    label: 'Long Term Debt',
    statement: 'balance_sheet',
    category: 'Liabilities',
    description: 'Debt obligations due after one year',
    unit: 'USD'
  },
  {
    id: 'equity',
    label: 'Total Equity',
    statement: 'balance_sheet',
    category: 'Equity',
    description: 'Shareholders\' equity in the company',
    unit: 'USD'
  },
  {
    id: 'equity_attributable_to_parent',
    label: 'Shareholders\' Equity',
    statement: 'balance_sheet',
    category: 'Equity',
    description: 'Equity attributable to parent company shareholders',
    unit: 'USD'
  },

  // Income Statement Metrics
  {
    id: 'revenues',
    label: 'Total Revenue',
    statement: 'income_statement',
    category: 'Revenue',
    description: 'Total income from business operations',
    unit: 'USD'
  },
  {
    id: 'cost_of_revenue',
    label: 'Cost of Revenue',
    statement: 'income_statement',
    category: 'Expenses',
    description: 'Direct costs of producing goods or services',
    unit: 'USD'
  },
  {
    id: 'gross_profit',
    label: 'Gross Profit',
    statement: 'income_statement',
    category: 'Profitability',
    description: 'Revenue minus cost of revenue',
    unit: 'USD'
  },
  {
    id: 'operating_expenses',
    label: 'Operating Expenses',
    statement: 'income_statement',
    category: 'Expenses',
    description: 'Costs of running the business operations',
    unit: 'USD'
  },
  {
    id: 'selling_general_and_administrative_expenses',
    label: 'SG&A Expenses',
    statement: 'income_statement',
    category: 'Expenses',
    description: 'Selling, general, and administrative expenses',
    unit: 'USD'
  },
  {
    id: 'research_and_development',
    label: 'R&D Expenses',
    statement: 'income_statement',
    category: 'Expenses',
    description: 'Research and development costs',
    unit: 'USD'
  },
  {
    id: 'depreciation_and_amortization',
    label: 'Depreciation & Amortization',
    statement: 'income_statement',
    category: 'Expenses',
    description: 'Non-cash expenses for asset depreciation',
    unit: 'USD'
  },
  {
    id: 'operating_income_loss',
    label: 'Operating Income',
    statement: 'income_statement',
    category: 'Profitability',
    description: 'Income from core business operations',
    unit: 'USD'
  },
  {
    id: 'interest_expense_operating',
    label: 'Interest Expense',
    statement: 'income_statement',
    category: 'Expenses',
    description: 'Cost of borrowing money',
    unit: 'USD'
  },
  {
    id: 'income_tax_expense_benefit',
    label: 'Income Tax Expense',
    statement: 'income_statement',
    category: 'Taxes',
    description: 'Taxes paid on income',
    unit: 'USD'
  },
  {
    id: 'net_income_loss',
    label: 'Net Income',
    statement: 'income_statement',
    category: 'Profitability',
    description: 'Bottom line profit after all expenses',
    unit: 'USD'
  },
  {
    id: 'basic_earnings_per_share',
    label: 'Basic EPS',
    statement: 'income_statement',
    category: 'Per Share',
    description: 'Earnings per share (basic calculation)',
    unit: 'USD / shares'
  },
  {
    id: 'diluted_earnings_per_share',
    label: 'Diluted EPS',
    statement: 'income_statement',
    category: 'Per Share',
    description: 'Earnings per share (diluted calculation)',
    unit: 'USD / shares'
  },

  // Cash Flow Statement Metrics
  {
    id: 'net_cash_flow_from_operating_activities',
    label: 'Operating Cash Flow',
    statement: 'cash_flow_statement',
    category: 'Operating Activities',
    description: 'Cash generated from core business operations',
    unit: 'USD'
  },
  {
    id: 'net_cash_flow_from_investing_activities',
    label: 'Investing Cash Flow',
    statement: 'cash_flow_statement',
    category: 'Investing Activities',
    description: 'Cash used for investments in assets',
    unit: 'USD'
  },
  {
    id: 'net_cash_flow_from_financing_activities',
    label: 'Financing Cash Flow',
    statement: 'cash_flow_statement',
    category: 'Financing Activities',
    description: 'Cash from financing activities (debt, equity)',
    unit: 'USD'
  },
  {
    id: 'net_cash_flow',
    label: 'Net Cash Flow',
    statement: 'cash_flow_statement',
    category: 'Total',
    description: 'Total change in cash position',
    unit: 'USD'
  },

  // Comprehensive Income Metrics
  {
    id: 'comprehensive_income_loss',
    label: 'Comprehensive Income',
    statement: 'comprehensive_income',
    category: 'Total Income',
    description: 'Net income plus other comprehensive income',
    unit: 'USD'
  },
  {
    id: 'other_comprehensive_income_loss',
    label: 'Other Comprehensive Income',
    statement: 'comprehensive_income',
    category: 'Other Income',
    description: 'Income items not included in net income',
    unit: 'USD'
  }
];

// Calculated Financial Ratios and Metrics
export interface CalculatedMetric {
  id: string;
  label: string;
  category: string;
  description: string;
  unit: string;
  calculation: string;
  requiredFields: string[];
}

export const CALCULATED_METRICS: CalculatedMetric[] = [
  // Profitability Ratios
  {
    id: 'return_on_equity',
    label: 'Return on Equity (ROE)',
    category: 'Profitability Ratios',
    description: 'Net income divided by shareholders\' equity',
    unit: 'Percentage',
    calculation: 'net_income_loss / equity_attributable_to_parent',
    requiredFields: ['income_statement.net_income_loss', 'balance_sheet.equity_attributable_to_parent']
  },
  {
    id: 'return_on_assets',
    label: 'Return on Assets (ROA)',
    category: 'Profitability Ratios',
    description: 'Net income divided by total assets',
    unit: 'Percentage',
    calculation: 'net_income_loss / assets',
    requiredFields: ['income_statement.net_income_loss', 'balance_sheet.assets']
  },
  {
    id: 'operating_margin',
    label: 'Operating Margin',
    category: 'Profitability Ratios',
    description: 'Operating income divided by revenue',
    unit: 'Percentage',
    calculation: 'operating_income_loss / revenues',
    requiredFields: ['income_statement.operating_income_loss', 'income_statement.revenues']
  },
  {
    id: 'net_profit_margin',
    label: 'Net Profit Margin',
    category: 'Profitability Ratios',
    description: 'Net income divided by revenue',
    unit: 'Percentage',
    calculation: 'net_income_loss / revenues',
    requiredFields: ['income_statement.net_income_loss', 'income_statement.revenues']
  },
  {
    id: 'gross_margin',
    label: 'Gross Margin',
    category: 'Profitability Ratios',
    description: 'Gross profit divided by revenue',
    unit: 'Percentage',
    calculation: 'gross_profit / revenues',
    requiredFields: ['income_statement.gross_profit', 'income_statement.revenues']
  },

  // Liquidity Ratios
  {
    id: 'current_ratio',
    label: 'Current Ratio',
    category: 'Liquidity Ratios',
    description: 'Current assets divided by current liabilities',
    unit: 'Ratio',
    calculation: 'current_assets / current_liabilities',
    requiredFields: ['balance_sheet.current_assets', 'balance_sheet.current_liabilities']
  },

  // Leverage Ratios
  {
    id: 'debt_to_equity',
    label: 'Debt-to-Equity Ratio (D/E)',
    category: 'Leverage Ratios',
    description: 'Total debt divided by shareholders\' equity',
    unit: 'Ratio',
    calculation: 'long_term_debt / equity_attributable_to_parent',
    requiredFields: ['balance_sheet.long_term_debt', 'balance_sheet.equity_attributable_to_parent']
  },
  {
    id: 'interest_coverage_ratio',
    label: 'Interest Coverage Ratio',
    category: 'Leverage Ratios',
    description: 'Operating income divided by interest expense',
    unit: 'Ratio',
    calculation: 'operating_income_loss / interest_expense_operating',
    requiredFields: ['income_statement.operating_income_loss', 'income_statement.interest_expense_operating']
  },

  // Cash Flow Metrics
  {
    id: 'free_cash_flow',
    label: 'Free Cash Flow (FCF)',
    category: 'Cash Flow Metrics',
    description: 'Operating cash flow minus capital expenditures',
    unit: 'USD',
    calculation: 'net_cash_flow_from_operating_activities - capital_expenditures',
    requiredFields: ['cash_flow_statement.net_cash_flow_from_operating_activities']
  },
  {
    id: 'owner_earnings',
    label: 'Owner Earnings',
    category: 'Cash Flow Metrics',
    description: 'Net income plus depreciation minus capital expenditures',
    unit: 'USD',
    calculation: 'net_income_loss + depreciation_and_amortization - capital_expenditures',
    requiredFields: ['income_statement.net_income_loss', 'income_statement.depreciation_and_amortization']
  },

  // Growth Metrics
  {
    id: 'revenue_growth',
    label: 'Revenue Growth',
    category: 'Growth Metrics',
    description: 'Year-over-year revenue growth rate',
    unit: 'Percentage',
    calculation: '(current_revenues - prior_revenues) / prior_revenues',
    requiredFields: ['income_statement.revenues']
  },
  {
    id: 'eps_growth',
    label: 'Earnings per Share (EPS) Growth',
    category: 'Growth Metrics',
    description: 'Year-over-year EPS growth rate',
    unit: 'Percentage',
    calculation: '(current_eps - prior_eps) / prior_eps',
    requiredFields: ['income_statement.basic_earnings_per_share']
  },

  // Efficiency Ratios
  {
    id: 'inventory_turnover',
    label: 'Inventory Turnover',
    category: 'Efficiency Ratios',
    description: 'Cost of goods sold divided by average inventory',
    unit: 'Ratio',
    calculation: 'cost_of_revenue / inventory',
    requiredFields: ['income_statement.cost_of_revenue', 'balance_sheet.inventory']
  },

  // Per Share Metrics
  {
    id: 'book_value_per_share',
    label: 'Book Value per Share',
    category: 'Per Share Metrics',
    description: 'Shareholders\' equity divided by shares outstanding',
    unit: 'USD per Share',
    calculation: 'equity_attributable_to_parent / basic_average_shares',
    requiredFields: ['balance_sheet.equity_attributable_to_parent', 'income_statement.basic_average_shares']
  },

  // Dividend Metrics
  {
    id: 'dividend_payout_ratio',
    label: 'Dividend Payout Ratio',
    category: 'Dividend Metrics',
    description: 'Dividends paid divided by net income',
    unit: 'Percentage',
    calculation: 'common_stock_dividends / net_income_loss',
    requiredFields: ['income_statement.common_stock_dividends', 'income_statement.net_income_loss']
  },

  // Valuation Ratios (require market data)
  {
    id: 'price_to_earnings',
    label: 'Price-to-Earnings Ratio (P/E)',
    category: 'Valuation Ratios',
    description: 'Stock price divided by earnings per share',
    unit: 'Ratio',
    calculation: 'market_price / basic_earnings_per_share',
    requiredFields: ['market_data.price', 'income_statement.basic_earnings_per_share']
  },
  {
    id: 'price_to_book',
    label: 'Price-to-Book Ratio (P/B)',
    category: 'Valuation Ratios',
    description: 'Stock price divided by book value per share',
    unit: 'Ratio',
    calculation: 'market_price / book_value_per_share',
    requiredFields: ['market_data.price', 'calculated.book_value_per_share']
  },
  {
    id: 'price_to_free_cash_flow',
    label: 'Price-to-Free Cash Flow (P/FCF)',
    category: 'Valuation Ratios',
    description: 'Market cap divided by free cash flow',
    unit: 'Ratio',
    calculation: 'market_cap / free_cash_flow',
    requiredFields: ['market_data.market_cap', 'calculated.free_cash_flow']
  },

  // Additional Cash Flow Metrics
  {
    id: 'capital_expenditures',
    label: 'Capital Expenditures (CapEx)',
    category: 'Cash Flow Metrics',
    description: 'Cash spent on fixed assets (estimated from investing activities)',
    unit: 'USD',
    calculation: 'abs(net_cash_flow_from_investing_activities)',
    requiredFields: ['cash_flow_statement.net_cash_flow_from_investing_activities']
  },

  // Additional Direct Metrics (available in API)
  {
    id: 'cash_and_equivalents',
    label: 'Cash and Cash Equivalents',
    category: 'Liquidity Metrics',
    description: 'Total cash and short-term investments',
    unit: 'USD',
    calculation: 'cash',
    requiredFields: ['balance_sheet.cash']
  }
];

// Group metrics by statement for easier UI organization
export const METRICS_BY_STATEMENT = {
  balance_sheet: FINANCIAL_METRICS.filter(m => m.statement === 'balance_sheet'),
  income_statement: FINANCIAL_METRICS.filter(m => m.statement === 'income_statement'),
  cash_flow_statement: FINANCIAL_METRICS.filter(m => m.statement === 'cash_flow_statement'),
  comprehensive_income: FINANCIAL_METRICS.filter(m => m.statement === 'comprehensive_income'),
  calculated: CALCULATED_METRICS
};

// Group metrics by category for better organization
export const METRICS_BY_CATEGORY = FINANCIAL_METRICS.reduce((acc, metric) => {
  if (!acc[metric.category]) {
    acc[metric.category] = [];
  }
  acc[metric.category].push(metric);
  return acc;
}, {} as Record<string, FinancialMetric[]>);

// Group calculated metrics by category
export const CALCULATED_METRICS_BY_CATEGORY = CALCULATED_METRICS.reduce((acc, metric) => {
  if (!acc[metric.category]) {
    acc[metric.category] = [];
  }
  acc[metric.category].push(metric);
  return acc;
}, {} as Record<string, CalculatedMetric[]>);

// Statement labels for UI
export const STATEMENT_LABELS = {
  balance_sheet: 'Balance Sheet',
  income_statement: 'Income Statement',
  cash_flow_statement: 'Cash Flow Statement',
  comprehensive_income: 'Comprehensive Income',
  calculated: 'Calculated Ratios'
};

// All available metrics (direct + calculated)
export const ALL_METRICS = [
  ...FINANCIAL_METRICS.map(m => ({ ...m, type: 'direct' as const })),
  ...CALCULATED_METRICS.map(m => ({ ...m, type: 'calculated' as const, statement: 'calculated' as const }))
];
