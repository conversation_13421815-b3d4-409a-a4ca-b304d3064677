import { supabase } from '@/integrations/supabase/client';

// Types for the discover service
export interface PublishedAgent {
  id: string;
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags: string[];
  publisherName: string;
  publisherId: string;
  downloadCount: number;
  averageRating: number;
  totalReviews: number;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  configuration?: any;
}

export interface AgentCategory {
  id: string;
  name: string;
  description?: string;
  icon_url?: string;
  sort_order: number;
  is_active: boolean;
}

export interface AgentReview {
  id: string;
  rating: number;
  reviewText?: string;
  reviewerName: string;
  reviewerId: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DiscoverFilters {
  search?: string;
  category?: string;
  tags?: string[];
  sortBy?: 'newest' | 'popular' | 'rating' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  featured?: boolean;
}

export interface DiscoverResponse {
  success: boolean;
  agents: PublishedAgent[];
  categories: AgentCategory[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  error?: string;
}

export interface PublishAgentRequest {
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
}

export interface ImportAgentRequest {
  publishedAgentId: string;
  customName?: string;
}

export interface CreateReviewRequest {
  publishedAgentId: string;
  rating: number;
  reviewText?: string;
}

export interface UpdateReviewRequest {
  reviewId: string;
  rating: number;
  reviewText?: string;
}

/**
 * Discover published agents with filtering and search
 */
export async function discoverAgents(filters: DiscoverFilters = {}): Promise<DiscoverResponse> {
  try {
    console.log('Discovering agents with filters:', filters);

    const { data, error } = await supabase.functions.invoke('discover-agents', {
      body: filters
    });

    if (error) {
      console.error('Discover agents error:', error);
      return {
        success: false,
        agents: [],
        categories: [],
        pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
        error: error.message || 'Failed to discover agents'
      };
    }

    return data;
  } catch (error) {
    console.error('Discover agents service error:', error);
    return {
      success: false,
      agents: [],
      categories: [],
      pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Publish an agent to the marketplace
 */
export async function publishAgent(request: PublishAgentRequest): Promise<{ success: boolean; publishedAgent?: any; message?: string; error?: string }> {
  try {
    console.log('Publishing agent:', request);

    const { data, error } = await supabase.functions.invoke('publish-agent', {
      body: request
    });

    if (error) {
      console.error('Publish agent error:', error);
      return {
        success: false,
        error: error.message || 'Failed to publish agent'
      };
    }

    return data;
  } catch (error) {
    console.error('Publish agent service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Import a published agent to user's library
 */
export async function importAgent(request: ImportAgentRequest): Promise<{ success: boolean; agent?: any; message?: string; alreadyImported?: boolean; error?: string }> {
  try {
    console.log('Importing agent:', request);

    const { data, error } = await supabase.functions.invoke('import-agent', {
      body: request
    });

    if (error) {
      console.error('Import agent error:', error);
      return {
        success: false,
        error: error.message || 'Failed to import agent'
      };
    }

    return data;
  } catch (error) {
    console.error('Import agent service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Get reviews for a published agent
 */
export async function getAgentReviews(publishedAgentId: string): Promise<{ success: boolean; reviews: AgentReview[]; error?: string }> {
  try {
    console.log('Getting reviews for agent:', publishedAgentId);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'GET',
      body: { publishedAgentId }
    });

    if (error) {
      console.error('Get agent reviews error:', error);
      return {
        success: false,
        reviews: [],
        error: error.message || 'Failed to get agent reviews'
      };
    }

    return data;
  } catch (error) {
    console.error('Get agent reviews service error:', error);
    return {
      success: false,
      reviews: [],
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Create a review for a published agent
 */
export async function createAgentReview(request: CreateReviewRequest): Promise<{ success: boolean; review?: any; message?: string; error?: string }> {
  try {
    console.log('Creating agent review:', request);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'POST',
      body: request
    });

    if (error) {
      console.error('Create agent review error:', error);
      return {
        success: false,
        error: error.message || 'Failed to create review'
      };
    }

    return data;
  } catch (error) {
    console.error('Create agent review service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update an existing review
 */
export async function updateAgentReview(request: UpdateReviewRequest): Promise<{ success: boolean; review?: any; message?: string; error?: string }> {
  try {
    console.log('Updating agent review:', request);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'PUT',
      body: request
    });

    if (error) {
      console.error('Update agent review error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update review'
      };
    }

    return data;
  } catch (error) {
    console.error('Update agent review service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Delete a review
 */
export async function deleteAgentReview(reviewId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    console.log('Deleting agent review:', reviewId);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'DELETE',
      body: { reviewId }
    });

    if (error) {
      console.error('Delete agent review error:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete review'
      };
    }

    return data;
  } catch (error) {
    console.error('Delete agent review service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}
