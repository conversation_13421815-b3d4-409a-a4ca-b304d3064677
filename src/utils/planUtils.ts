// Plan utility functions for managing plan selection

// LocalStorage keys
export const PLAN_TYPE_KEY = 'Osis_selected_plan_type';

// Plan types
export const PLAN_TYPES = {
  BASIC: 'basic',
  PRO: 'pro'
};

// Price IDs for different plans
export const PLAN_PRICE_IDS = {
  [PLAN_TYPES.BASIC]: 'price_1ROYLKDebmd1GpTvct491Kw6', // $5.99 plan
  [PLAN_TYPES.PRO]: 'price_1ROYKjDebmd1GpTv5oYNMKMv'    // $9.99 plan
};

/**
 * Set the selected plan type in localStorage
 * @param planType The plan type to store
 */
export const setSelectedPlanType = (planType: string): void => {
  try {
    localStorage.setItem(PLAN_TYPE_KEY, planType);
  } catch (error) {
    console.error('Error setting plan type in localStorage:', error);
  }
};

/**
 * Get the selected plan type from localStorage
 * @returns The stored plan type or null if not found
 */
export const getSelectedPlanType = (): string | null => {
  try {
    return localStorage.getItem(PLAN_TYPE_KEY);
  } catch (error) {
    console.error('Error getting plan type from localStorage:', error);
    return null;
  }
};

/**
 * Clear the selected plan type from localStorage
 */
export const clearSelectedPlanType = (): void => {
  try {
    localStorage.removeItem(PLAN_TYPE_KEY);
  } catch (error) {
    console.error('Error clearing plan type from localStorage:', error);
  }
};

/**
 * Get the price ID for a given plan type
 * @param planType The plan type
 * @returns The price ID for the plan type or the basic plan price ID as default
 */
export const getPriceIdForPlanType = (planType: string | null): string => {
  if (!planType) {
    return PLAN_PRICE_IDS[PLAN_TYPES.BASIC]; // Default to basic plan
  }
  
  return PLAN_PRICE_IDS[planType] || PLAN_PRICE_IDS[PLAN_TYPES.BASIC];
};
