import posthog from 'posthog-js';

// Initialize PostHog
const initPostHog = () => {
  const POSTHOG_KEY = import.meta.env.POSTHOG_KEY;
  const POSTHOG_HOST = import.meta.env.POSTHOG_HOST || 'https://us.posthog.com';

  if (!POSTHOG_KEY) {
    return;
  }

  posthog.init(POSTHOG_KEY, {
    api_host: POSTHOG_HOST,
    autocapture: true,
    capture_pageview: true,
    capture_pageleave: true,
    debug: process.env.NODE_ENV !== 'production'
  });
};

// Track LLM events
export const trackLLMEvent = (
  eventName: string,
  {
    distinctId,
    traceId,
    input,
    output,
    model = 'gemini-2.0-flash',
    provider = 'google',
    latency,
    properties = {}
  }: {
    distinctId?: string;
    traceId?: string;
    input?: string;
    output?: string;
    model?: string;
    provider?: string;
    latency?: number;
    properties?: Record<string, any>;
  }
) => {
  // Ensure PostHog is initialized
  if (!posthog.__loaded) {
    initPostHog();
  }

  // Calculate tokens (rough estimate)
  const inputTokens = input ? Math.ceil(input.length / 4) : 0;
  const outputTokens = output ? Math.ceil(output.length / 4) : 0;

  // Send event to PostHog
  posthog.capture('$ai_generation', {
    distinct_id: distinctId || 'anonymous',
    $ai_trace_id: traceId || crypto.randomUUID(),
    $ai_model: model,
    $ai_provider: provider,
    $ai_input: input,
    $ai_input_tokens: inputTokens,
    $ai_output_choices: output ? [{ text: output }] : undefined,
    $ai_output_tokens: outputTokens,
    $ai_latency: latency,
    ...properties
  });

  // Log in development
  if (process.env.NODE_ENV !== 'production') {

  }
};

// Export initialization function
export const initAnalytics = initPostHog;

// Export PostHog instance for direct access if needed
export { posthog }; 
