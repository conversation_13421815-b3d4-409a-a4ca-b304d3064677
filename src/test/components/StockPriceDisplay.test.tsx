import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import axios from 'axios';
import { isCryptoCurrency, formatPolygonSymbol } from '@/utils/symbolUtils';

// Mock axios and utility functions
vi.mock('axios');
vi.mock('@/utils/symbolUtils', () => ({
  isCryptoCurrency: vi.fn(),
  formatPolygonSymbol: vi.fn()
}));

describe('StockPriceDisplay', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Default mocks
    (isCryptoCurrency as any).mockResolvedValue(false);
    (formatPolygonSymbol as any).mockResolvedValue('AAPL');
  });

  it('displays loading state initially', () => {
    render(<StockPriceDisplay symbol="AAPL" />);
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('displays stock price and percent change when data is loaded', async () => {
    const mockResponse = {
      data: {
        results: [{
          c: 150.25,
          o: 145.00,
          v: 1000000
        }]
      }
    };

    (axios.get as any).mockResolvedValueOnce(mockResponse);

    render(<StockPriceDisplay symbol="AAPL" />);

    await waitFor(() => {
      expect(screen.getByText('$150.25')).toBeInTheDocument();
      expect(screen.getByText('+3.62%')).toBeInTheDocument();
    });
  });

  it('displays error message when API call fails', async () => {
    (axios.get as any).mockRejectedValueOnce(new Error('API Error'));

    render(<StockPriceDisplay symbol="AAPL" />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load price data')).toBeInTheDocument();
    });
  });

  it('uses custom render prop when provided', async () => {
    const mockResponse = {
      data: {
        results: [{
          c: 150.25,
          o: 145.00,
          v: 1000000
        }]
      }
    };

    (axios.get as any).mockResolvedValueOnce(mockResponse);

    const customRender = (price: string, percentChange: string) => (
      <div data-testid="custom-render">
        Custom: {price} ({percentChange})
      </div>
    );

    render(<StockPriceDisplay symbol="AAPL" render={customRender} />);

    await waitFor(() => {
      expect(screen.getByTestId('custom-render')).toBeInTheDocument();
      expect(screen.getByText('Custom: 150.25 (+3.62%)')).toBeInTheDocument();
    });
  });

  it('displays crypto badge for cryptocurrency symbols', async () => {
    (isCryptoCurrency as any).mockResolvedValue(true);
    (formatPolygonSymbol as any).mockResolvedValue('X:BTCUSD');

    const mockResponse = {
      data: {
        results: [{
          c: 50000.00,
          o: 48000.00,
          v: 1000
        }]
      }
    };

    (axios.get as any).mockResolvedValueOnce(mockResponse);

    render(<StockPriceDisplay symbol="BTC" />);

    await waitFor(() => {
      expect(screen.getByText('Crypto')).toBeInTheDocument();
      expect(screen.getByText('$50,000.00')).toBeInTheDocument();
    });
  });
}); 