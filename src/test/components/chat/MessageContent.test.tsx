import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import MessageContent from '@/components/chat/MessageContent';

// Mock the AdvancedStockChart component
vi.mock('@/components/charts/AdvancedStockChart', () => ({
  AdvancedStockChart: () => <div data-testid="stock-chart">Mock Stock Chart</div>
}));

describe('MessageContent', () => {
  it('renders text content correctly', () => {
    const message = {
      content: {
        text: 'Test message content',
        stockChart: null
      }
    };

    render(<MessageContent message={message} />);
    expect(screen.getByText('Test message content')).toBeInTheDocument();
    expect(screen.queryByTestId('stock-chart')).not.toBeInTheDocument();
  });

  it('renders stock chart when stockChart data is present', () => {
    const message = {
      content: {
        text: 'Stock analysis',
        stockChart: {
          symbol: 'AAPL'
        }
      }
    };

    render(<MessageContent message={message} />);
    expect(screen.getByText('Stock analysis')).toBeInTheDocument();
    expect(screen.getByTestId('stock-chart')).toBeInTheDocument();
  });

  it('applies correct styling classes', () => {
    const message = {
      content: {
        text: 'Test message',
        stockChart: null
      }
    };

    render(<MessageContent message={message} />);
    const contentDiv = screen.getByTestId('message-content');
    expect(contentDiv).toHaveClass('prose', 'dark:prose-invert');
  });
}); 