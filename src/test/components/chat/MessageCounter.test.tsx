import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import MessageCounter from '@/components/chat/MessageCounter';

describe('MessageCounter', () => {
  it('displays correct message for multiple tokens remaining', () => {
    render(<MessageCounter tokensRemaining={3} />);
    expect(screen.getByText('3 Messages Left')).toBeInTheDocument();
  });

  it('displays correct message for one token remaining', () => {
    render(<MessageCounter tokensRemaining={1} />);
    expect(screen.getByText('1 Message Left')).toBeInTheDocument();
  });

  it('displays correct message for zero tokens remaining', () => {
    render(<MessageCounter tokensRemaining={0} />);
    expect(screen.getByText('0 Messages Left')).toBeInTheDocument();
  });

  it('uses correct color classes based on tokens remaining', () => {
    const { rerender } = render(<MessageCounter tokensRemaining={3} />);
    expect(screen.getByTestId('status-dot')).toHaveClass('bg-emerald-500');

    rerender(<MessageCounter tokensRemaining={1} />);
    expect(screen.getByTestId('status-dot')).toHaveClass('bg-orange-500');

    rerender(<MessageCounter tokensRemaining={0} />);
    expect(screen.getByTestId('status-dot')).toHaveClass('bg-red-500');
  });

  it('accepts and uses custom maxTokens prop', () => {
    render(<MessageCounter tokensRemaining={5} maxTokens={10} />);
    expect(screen.getByText('5 Messages Left')).toBeInTheDocument();
  });
}); 