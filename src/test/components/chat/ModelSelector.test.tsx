import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import ModelSelector from '@/components/chat/ModelSelector';

describe('ModelSelector', () => {
  it('renders with correct model information', () => {
    render(<ModelSelector />);
    
    // Check for model label
    expect(screen.getByText('Osis V1')).toBeInTheDocument();
    
    // Check for model description
    expect(screen.getByText('best model')).toBeInTheDocument();
    
    // Check for the pulsing dot indicator
    const pulsingDot = screen.getByTestId('model-status-indicator');
    expect(pulsingDot).toBeInTheDocument();
    expect(pulsingDot).toHaveClass('bg-emerald-400');
  });

  it('has the correct styling classes', () => {
    render(<ModelSelector />);
    
    const container = screen.getByTestId('model-selector-container');
    expect(container).toHaveClass(
      'border',
      'border-emerald-500/30',
      'bg-gradient-to-r',
      'from-emerald-900/20',
      'to-emerald-700/10'
    );
  });
}); 