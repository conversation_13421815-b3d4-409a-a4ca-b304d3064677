import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ChatInterface from '../../../components/chat/ChatInterface';

// Mock the required components and hooks
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useParams: () => ({ chatId: '1' }),
}));

vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn().mockReturnValue({
    data: [],
    isLoading: false,
    error: null,
  }),
  useQueryClient: vi.fn().mockReturnValue({
    invalidateQueries: vi.fn(),
  }),
}));

vi.mock('../../../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn().mockResolvedValue({ data: { session: { user: { id: 'user123' } } }, error: null }),
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: { tokens_remaining: 5 }, error: null }),
        }),
      }),
    }),
  },
}));

// Mock child components
vi.mock('../../../components/chat/MessageCard', () => ({
  default: ({ message }: { message: any }) => (
    <div data-testid="message-card">
      {message.role}: {message.content.text}
    </div>
  ),
}));

vi.mock('../../../components/chat/ModelSelector', () => ({
  default: () => <div data-testid="model-selector">Model Selector</div>,
}));

vi.mock('../../../components/chat/MessageCounter', () => ({
  default: ({ tokensRemaining }: { tokensRemaining: number }) => (
    <div data-testid="message-counter">Messages Left: {tokensRemaining}</div>
  ),
}));

// Mock axios
vi.mock('axios', () => ({
  default: {
    post: vi.fn().mockResolvedValue({ data: { message: 'Response from AI' } }),
  },
}));

describe('ChatInterface Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the chat interface with basic elements', async () => {
    // Render with mocked components
    render(<ChatInterface />);
    
    // Wait for component to load
    await waitFor(() => {
      // Check for key elements
      expect(screen.getByTestId('model-selector')).toBeInTheDocument();
      expect(screen.getByTestId('message-counter')).toBeInTheDocument();
    });
  });
}); 