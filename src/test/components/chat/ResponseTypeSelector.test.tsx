import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ResponseTypeSelector from '@/components/chat/ResponseTypeSelector';

describe('ResponseTypeSelector', () => {
  const mockOnToggle = vi.fn();

  beforeEach(() => {
    mockOnToggle.mockClear();
  });

  it('renders all response type buttons', () => {
    render(
      <ResponseTypeSelector 
        selectedTypes={[]} 
        onToggle={mockOnToggle} 
      />
    );

    expect(screen.getByTitle('YouTube Research')).toBeInTheDocument();
    expect(screen.getByTitle('X Research')).toBeInTheDocument();
  });

  it('applies correct styling for selected types', () => {
    render(
      <ResponseTypeSelector 
        selectedTypes={['youtube']} 
        onToggle={mockOnToggle} 
      />
    );

    const youtubeButton = screen.getByTitle('YouTube Research');
    const xButton = screen.getByTitle('X Research');

    expect(youtubeButton).toHaveClass('bg-[#1A1A1A]', 'border', 'border-white/10');
    expect(xButton).toHaveClass('bg-black/20');
  });

  it('calls onToggle with correct type ID when clicked', () => {
    render(
      <ResponseTypeSelector 
        selectedTypes={[]} 
        onToggle={mockOnToggle} 
      />
    );

    const youtubeButton = screen.getByTitle('YouTube Research');
    fireEvent.click(youtubeButton);

    expect(mockOnToggle).toHaveBeenCalledWith('youtube');
  });

  it('renders icons correctly', () => {
    render(
      <ResponseTypeSelector 
        selectedTypes={[]} 
        onToggle={mockOnToggle} 
      />
    );

    expect(screen.getByTestId('youtube-icon')).toBeInTheDocument();
    expect(screen.getByAltText('X Logo')).toBeInTheDocument();
  });
}); 