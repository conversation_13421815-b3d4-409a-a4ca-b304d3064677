import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import VoiceControl from '@/components/chat/VoiceControl';
import { supabase } from '@/integrations/supabase/client';

// Mock supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    functions: {
      invoke: vi.fn()
    }
  }
}));

// Mock useToast
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

describe('VoiceControl', () => {
  const mockOnPlayToggle = vi.fn();
  const defaultProps = {
    messageId: 'test-message-id',
    text: 'Test message',
    onPlayToggle: mockOnPlayToggle
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders play button when not playing', () => {
    render(<VoiceControl {...defaultProps} isPlaying={false} />);
    expect(screen.getByTestId('play-button')).toBeInTheDocument();
    expect(screen.queryByTestId('stop-button')).not.toBeInTheDocument();
  });

  it('renders stop button when playing', () => {
    render(<VoiceControl {...defaultProps} isPlaying={true} />);
    expect(screen.getByTestId('stop-button')).toBeInTheDocument();
    expect(screen.queryByTestId('play-button')).not.toBeInTheDocument();
  });

  it('calls generateVoice when play button is clicked', async () => {
    const mockAudioUrl = 'http://example.com/audio.mp3';
    (supabase.functions.invoke as any).mockResolvedValueOnce({
      data: { audioUrl: mockAudioUrl },
      error: null
    });

    render(<VoiceControl {...defaultProps} />);
    fireEvent.click(screen.getByRole('button'));

    expect(supabase.functions.invoke).toHaveBeenCalledWith('generate-voice', {
      body: { text: defaultProps.text, messageId: defaultProps.messageId }
    });
  });

  it('creates audio element when audio URL is received', async () => {
    const mockAudioUrl = 'http://example.com/audio.mp3';
    (supabase.functions.invoke as any).mockResolvedValueOnce({
      data: { audioUrl: mockAudioUrl },
      error: null
    });

    render(<VoiceControl {...defaultProps} />);
    fireEvent.click(screen.getByRole('button'));

    // Wait for the audio element to be created
    const audio = await screen.findByTestId('audio-player');
    expect(audio).toHaveAttribute('src', mockAudioUrl);
  });

  it('calls onPlayToggle when audio ends', async () => {
    const mockAudioUrl = 'http://example.com/audio.mp3';
    (supabase.functions.invoke as any).mockResolvedValueOnce({
      data: { audioUrl: mockAudioUrl },
      error: null
    });

    render(<VoiceControl {...defaultProps} />);
    fireEvent.click(screen.getByRole('button'));

    // Wait for the audio element to be created
    const audio = await screen.findByTestId('audio-player');
    fireEvent.ended(audio);

    expect(mockOnPlayToggle).toHaveBeenCalled();
  });
}); 