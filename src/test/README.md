# Testing Guide for Trade Sensei Chat

This directory contains tests for the Trade Sensei Chat application. The tests are written using Vitest and React Testing Library.

## Test Structure

The tests are organized in a structure that mirrors the source code:

```
src/test/
├── App.test.tsx                 # Tests for the main App component
├── components/                  # Tests for components
│   └── chat/                    # Tests for chat components
│       ├── ChatInterface.test.tsx
│       ├── MessageCounter.test.tsx
│       └── ModelSelector.test.tsx
├── utils/                       # Tests for utility functions
│   └── symbolUtils.test.ts
└── setup.ts                     # Test setup file
```

## Running Tests

To run the tests, use the following commands:

```bash
# Run tests in watch mode
npm test

# Run tests with coverage
npm run test:coverage
```

## Test Setup

The test setup is defined in `src/test/setup.ts`. This file includes:

- Configuration for React Testing Library
- Mocks for Supabase client
- Mocks for browser APIs like matchMedia and ResizeObserver

## Writing New Tests

When writing new tests:

1. Create a test file with the same name as the component or utility you're testing, with a `.test.tsx` or `.test.ts` extension
2. Import the necessary testing utilities:
   ```typescript
   import { describe, it, expect, vi, beforeEach } from 'vitest';
   import { render, screen, fireEvent } from '@testing-library/react';
   ```
3. Mock any dependencies that the component or utility relies on
4. Write your tests using the describe/it pattern
5. Use React Testing Library's queries to find elements in the rendered component

## Mocking Dependencies

Common dependencies that need to be mocked:

- Supabase client (already mocked in setup.ts)
- React Router (useNavigate, useParams, Link, etc.)
- External APIs (using vi.mock and vi.fn())
- UI components from libraries

Example of mocking React Router:

```typescript
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useParams: () => ({ chatId: '1' }),
  Link: ({ children }) => <a href="#">{children}</a>,
}));
```

## Troubleshooting

If you encounter issues with the tests:

- Check that all dependencies are properly mocked
- Ensure that the component is rendered with the necessary providers
- Use `screen.debug()` to see the rendered HTML
- Check for console errors during test execution 