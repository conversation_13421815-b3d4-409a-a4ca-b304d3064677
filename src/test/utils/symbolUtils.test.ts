import { describe, it, expect, vi, beforeEach } from 'vitest';
import { isCryptoCurrency, formatPolygonSymbol, getSymbolDisplayName } from '@/utils/symbolUtils';

// Mock fetch
global.fetch = vi.fn();

describe('symbolUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset fetch mock
    (global.fetch as any).mockReset();
  });

  describe('isCryptoCurrency', () => {
    it('returns false for empty symbol', async () => {
      expect(await isCryptoCurrency('')).toBe(false);
    });

    it('returns false for one-letter stock symbols', async () => {
      const oneLetterStocks = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K',
        'L', 'M', 'O', 'R', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
      
      for (const symbol of oneLetterStocks) {
        expect(await isCryptoCurrency(symbol)).toBe(false);
      }
    });

    it('returns true for common crypto symbols when API is not available', async () => {
      const commonCryptos = ['BTC', 'ETH', 'XRP', 'LTC', 'DOGE'];
      
      for (const symbol of commonCryptos) {
        expect(await isCryptoCurrency(symbol)).toBe(true);
      }
    });

    it('handles API errors gracefully', async () => {
      (global.fetch as any).mockRejectedValue(new Error('API Error'));
      
      // Should fall back to common crypto detection
      expect(await isCryptoCurrency('BTC')).toBe(true);
      expect(await isCryptoCurrency('AAPL')).toBe(false);
    });
  });

  describe('formatPolygonSymbol', () => {
    it('returns empty string for empty input', async () => {
      expect(await formatPolygonSymbol('')).toBe('');
    });

    it('formats crypto symbols correctly', async () => {
      // Mock isCryptoCurrency to return true for testing crypto formatting
      vi.mock('@/utils/symbolUtils', () => ({
        isCryptoCurrency: vi.fn().mockResolvedValue(true)
      }));

      expect(await formatPolygonSymbol('BTC')).toBe('X:BTCUSD');
      expect(await formatPolygonSymbol('ETH/USD')).toBe('X:ETHUSD');
      expect(await formatPolygonSymbol('XRP-USD')).toBe('X:XRPUSD');
    });

    it('preserves existing X: prefix for crypto symbols', async () => {
      expect(await formatPolygonSymbol('X:BTCUSD')).toBe('X:BTCUSD');
    });

    it('returns uppercase symbol for stocks', async () => {
      // Mock isCryptoCurrency to return false for testing stock formatting
      vi.mock('@/utils/symbolUtils', () => ({
        isCryptoCurrency: vi.fn().mockResolvedValue(false)
      }));

      expect(await formatPolygonSymbol('aapl')).toBe('AAPL');
      expect(await formatPolygonSymbol('msft')).toBe('MSFT');
    });
  });

  describe('getSymbolDisplayName', () => {
    it('returns empty string for empty input', async () => {
      expect(await getSymbolDisplayName('')).toBe('');
    });

    it('formats crypto symbols with USD pair', async () => {
      // Mock isCryptoCurrency to return true for testing crypto formatting
      vi.mock('@/utils/symbolUtils', () => ({
        isCryptoCurrency: vi.fn().mockResolvedValue(true)
      }));

      expect(await getSymbolDisplayName('BTC')).toBe('BTC/USD');
      expect(await getSymbolDisplayName('X:ETHUSD')).toBe('ETH/USD');
    });

    it('returns uppercase symbol for stocks', async () => {
      // Mock isCryptoCurrency to return false for testing stock formatting
      vi.mock('@/utils/symbolUtils', () => ({
        isCryptoCurrency: vi.fn().mockResolvedValue(false)
      }));

      expect(await getSymbolDisplayName('aapl')).toBe('AAPL');
      expect(await getSymbolDisplayName('msft')).toBe('MSFT');
    });

    it('handles errors gracefully', async () => {
      // Mock isCryptoCurrency to throw an error
      vi.mock('@/utils/symbolUtils', () => ({
        isCryptoCurrency: vi.fn().mockRejectedValue(new Error('Test error'))
      }));

      expect(await getSymbolDisplayName('TEST')).toBe('TEST');
    });
  });
}); 