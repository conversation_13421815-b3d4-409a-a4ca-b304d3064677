import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import App from '../App';
import { supabase } from '../integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

// Mock the react-router-dom module
vi.mock('react-router-dom', () => ({
  BrowserRouter: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Routes: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Route: ({ element }: { element: React.ReactNode }) => element,
  Navigate: () => <div>Navigate Component</div>,
  useNavigate: () => vi.fn(),
  useParams: () => ({ chatId: '1' }),
  useLocation: () => ({ pathname: '/' }),
  Link: ({ children }: { children: React.ReactNode }) => <a href="#">{children}</a>,
}));

// Mock the ChatInterface component
vi.mock('../components/chat/ChatInterface', () => ({
  default: () => <div data-testid="chat-interface">Mock Chat Interface</div>,
}));

// Mock the AuthPage component
vi.mock('../pages/AuthPage', () => ({
  default: () => <div data-testid="auth-page">Mock Auth Page</div>,
}));

// Mock the MainLayout component
vi.mock('../components/layout/MainLayout', () => ({
  MainLayout: ({ children }: { children: React.ReactNode }) => <div data-testid="main-layout">{children}</div>,
}));

// Mock all page components
vi.mock('../pages/Settings', () => ({
  default: () => <div data-testid="settings-page">Settings Page</div>,
}));

vi.mock('../pages/ModelSettings', () => ({
  default: () => <div data-testid="model-settings-page">Model Settings Page</div>,
}));

vi.mock('../pages/About', () => ({
  default: () => <div data-testid="about-page">About Page</div>,
}));

vi.mock('../pages/Subscription', () => ({
  default: () => <div data-testid="subscription-page">Subscription Page</div>,
}));

vi.mock('../pages/ManageSubscription', () => ({
  default: () => <div data-testid="manage-subscription-page">Manage Subscription Page</div>,
}));

// Mock the Login component (which is now removed and replaced with a redirect)
vi.mock('../components/Login', () => ({
  default: () => <div data-testid="login-page">Login Page</div>,
}));

// Mock useToast hook
vi.mock('@/components/ui/use-toast', () => ({
  useToast: vi.fn().mockReturnValue({
    toast: vi.fn()
  }),
}));

// Mock the Supabase client module
vi.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      signOut: vi.fn(),
      onAuthStateChange: vi.fn().mockReturnValue({
        data: {
          subscription: {
            unsubscribe: vi.fn()
          }
        }
      })
    },
  },
}));

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders AuthPage when user is not authenticated', async () => {
    // Mock the Supabase auth to return no session
    vi.mocked(supabase.auth.getSession).mockResolvedValueOnce({
      data: { session: null },
      error: null,
    });

    render(<App />);

    // Wait for the authentication check to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-page')).toBeInTheDocument();
    });
  });

  it('renders MainLayout with ChatInterface when user is authenticated', async () => {
    // Mock the Supabase auth to return a session
    vi.mocked(supabase.auth.getSession).mockResolvedValueOnce({
      data: { session: { user: { id: 'user123' } } as any },
      error: null,
    });

    render(<App />);

    // Wait for the authentication check to complete
    await waitFor(() => {
      expect(screen.getByTestId('main-layout')).toBeInTheDocument();
    });
  });

  it('handles authentication errors', async () => {
    // Mock the Supabase auth to return an error
    vi.mocked(supabase.auth.getSession).mockResolvedValueOnce({
      data: { session: null },
      error: {
        name: 'AuthError',
        message: 'Authentication error',
        status: 401
      } as any,
    });

    render(<App />);

    // Wait for the error to be handled
    await waitFor(() => {
      expect(useToast().toast).toHaveBeenCalledWith({
        variant: "destructive",
        title: "Authentication Error",
        description: "Please sign in again to continue."
      });
      expect(supabase.auth.signOut).toHaveBeenCalled();
      expect(screen.getByTestId('auth-page')).toBeInTheDocument();
    });
  });
});